cmake_minimum_required(VERSION 3.8)
project(aqita_barcodes LANGUAGES C CXX)

# Compiler settings
if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Find ROS 2 and other dependencies
find_package(ament_cmake_auto REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclcpp_components REQUIRED)
find_package(OpenCV REQUIRED)
find_package(aqita_interfaces REQUIRED)
find_package(nav2_aqita_msgs REQUIRED)

# Find CUDA (required for GPU support)
find_package(CUDA REQUIRED)
message(STATUS "Found CUDA version: ${CUDA_VERSION}")

# Set ONNX Runtime paths (MODIFY THESE PATHS TO MATCH YOUR INSTALLATION)
set(ONNXRuntime_ROOT_DIR "/usr/local")  # or path where you extracted ONNX Runtime
set(ONNXRuntime_INCLUDE_DIR "${ONNXRuntime_ROOT_DIR}/include/onnxruntime")
set(ONNXRuntime_LIB_DIR "${ONNXRuntime_ROOT_DIR}/lib")

# Verify paths
if(NOT EXISTS "${ONNXRuntime_INCLUDE_DIR}/core/session/onnxruntime_cxx_api.h")
  message(FATAL_ERROR "ONNX Runtime headers not found at ${ONNXRuntime_INCLUDE_DIR}")
endif()

if(NOT EXISTS "${ONNXRuntime_LIB_DIR}/libonnxruntime.so")
  message(FATAL_ERROR "ONNX Runtime library not found at ${ONNXRuntime_LIB_DIR}")
endif()

message(STATUS "ONNX Runtime includes: ${ONNXRuntime_INCLUDE_DIR}")
message(STATUS "ONNX Runtime library path: ${ONNXRuntime_LIB_DIR}")

# Include directories
include_directories(
    ${OpenCV_INCLUDE_DIRS}
    ${ONNXRuntime_INCLUDE_DIR}
    ${ONNXRuntime_INCLUDE_DIR}/core/session  # Add this specific path
    ${CUDA_INCLUDE_DIRS}
)

link_directories(
    ${ONNXRuntime_LIB_DIR}
)

# Discover and include ament dependencies automatically
ament_auto_find_build_dependencies()

# ------------------------------------------------------------------------------
# Add the PhenixDecoderCPPWrapper static library
# ------------------------------------------------------------------------------
add_subdirectory(src/PhenixDecoderCPPWrapper)

# Add include directory so other targets can use its headers
include_directories(src/PhenixDecoderCPPWrapper)
# ------------------------------------------------------------------------------

# YOLOv8 decoder node
ament_auto_add_library(yolov8_decoder_node SHARED src/yolov8_decoder_node.cpp)
rclcpp_components_register_nodes(yolov8_decoder_node "aqita::ros2::inference::YoloV8DecoderNode")
target_link_libraries(yolov8_decoder_node ${OpenCV_LIBRARIES})

# Bounding-box visualizer node
ament_auto_add_library(bbox_visualizer_node SHARED src/bbox_visualizer_node.cpp)
rclcpp_components_register_nodes(bbox_visualizer_node "aqita::ros2::inference::ImageWithBoundingBoxes")
target_link_libraries(bbox_visualizer_node ${OpenCV_LIBRARIES})

# Process crops node (uses PhenixDecoderCPPWrapper)
ament_auto_add_library(process_crops_node SHARED src/process_crops_node.cpp)
rclcpp_components_register_nodes(process_crops_node "aqita::ros2::inference::ProcessCrops")
target_link_libraries(process_crops_node
  ${OpenCV_LIBRARIES}
  PhenixDecoderCPPWrapper
  onnxruntime  # Link the library by name
  ${CUDA_LIBRARIES}
  cudart
)

# Template for new nodes (copy and modify as needed)
# ament_auto_add_library(<new-node-name> SHARED src/<new-node-name>.cpp)
# rclcpp_components_register_nodes(<new-node-name> "aqita::ros2::inference::<NewNodeClass>")
# target_link_libraries(<new-node-name> ${OpenCV_LIBRARIES} PhenixDecoderCPPWrapper)

# ------------------------------------------------------------------------------
# Install launch and test directories
install(DIRECTORY launch
  DESTINATION share/${PROJECT_NAME}
)

# Optionally install the shared library if needed at runtime
# install(FILES ${DECODER_SHARED_LIBRARY_TO_COPY}
#   DESTINATION lib
# )

# ------------------------------------------------------------------------------
# Testing setup
if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  set(ament_cmake_cpplint_FOUND TRUE)
  set(ament_cmake_copyright_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_auto_package()
