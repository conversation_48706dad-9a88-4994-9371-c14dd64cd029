# Instructions for Installing the Dependencies

## Pre Instalation in host
```bash
cd ~/ROS/aqita-dev/src/
git clone -b release-3.2 https://github.com/NVIDIA-ISAAC-ROS/isaac_ros_object_detection.git && \
git clone -b release-3.2 https://github.com/NVIDIA-ISAAC-ROS/isaac_ros_dnn_inference.git && \
git clone -b release-3.2 https://github.com/NVIDIA-ISAAC-ROS/isaac_ros_examples.git && \
git clone -b humble https://github.com/ros-visualization/rqt.git && \
git clone -b humble-devel https://github.com/ros-visualization/rqt_image_view.git && \
git clone -b humble https://github.com/ros-perception/image_common.git && \
git clone -b humble https://github.com/ros-perception/image_pipeline.git 

```
# run inside docker:
```bash
colcon build --symlink-install --packages-up-to isaac_ros_dnn_image_encoder --cmake-args -DBUILD_TESTING=OFF  && \
colcon build --symlink-install --packages-up-to isaac_ros_tensor_rt --cmake-args -DBUILD_TESTING=OFF && \
colcon build --symlink-install --packages-up-to isaac_ros_yolov8 --cmake-args -DBUILD_TESTING=OFF && \
colcon build --symlink-install --packages-up-to isaac_ros_examples --cmake-args -DBUILD_TESTING=OFF && \
colcon build --symlink-install --packages-select-regex rqt* --cmake-args -DBUILD_TESTING=OFF && \
colcon build --symlink-install --packages-up-to camera_info_manager --cmake-args -DBUILD_TESTING=OFF && \
colcon build --symlink-install --packages-up-to image_publisher --cmake-args -DBUILD_TESTING=OFF && \
colcon build --symlink-install --packages-up-to isaac_ros_nitros_tensor_list_type --cmake-args -DBUILD_TESTING=OFF && \
source install/setup.bash


```

## Installation of ros-humble-magic-enum ros-humble-ompl
```bash
sudo rm -f /etc/apt/sources.list.d/ros2.list && \
sudo mkdir -p /usr/share/keyrings && \
curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key | \
  gpg --dearmor | sudo tee /usr/share/keyrings/ros-archive-keyring.gpg > /dev/null && \
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] \
http://packages.ros.org/ros2/ubuntu jammy main" | \
sudo tee /etc/apt/sources.list.d/ros2.list > /dev/null && \
sudo apt update && \
sudo apt-get install ros-humble-magic-enum ros-humble-ompl
```

This is going to install the needed packages

## Installation of ONNX runtime
```bash
wget https://github.com/microsoft/onnxruntime/releases/download/v1.20.0/onnxruntime-linux-x64-gpu-1.20.0.tgz && \
tar -xvzf onnxruntime-linux-x64-gpu-1.20.0.tgz && \
cd onnxruntime-linux-x64-gpu-1.20.0 && \
ldd lib/libonnxruntime_providers_cuda.so | grep cublas || true && \
sudo cp -r lib/* /usr/local/lib/ && \
sudo cp -r include/* /usr/local/include/ && \
sudo ldconfig && \
sudo mkdir -p /usr/local/include/onnxruntime/core/session && \
sudo mv /usr/local/include/onnxruntime_*.h /usr/local/include/onnxruntime/core/session/ && \
sudo mv /usr/local/include/cpu_provider_factory.h /usr/local/include/onnxruntime/core/session/ && \
sudo mv /usr/local/include/provider_options.h /usr/local/include/onnxruntime/core/session/ && \
ls -l /usr/local/include/onnxruntime/core/session/ && \
cd ..
```
This is going to install the `onnxruntime` library that is compatible with `CUDA 12` and `cuDNN 9.0` which are the ones used in the docker image.
It is also going to do all of the necessary mappings

## AQITA VISION
Everything else is same as in `aqita-vision`

```bash
sudo sysctl -w net.core.rmem_max=**********
sudo sysctl -w net.core.rmem_default=**********
sudo sysctl -w net.core.wmem_max=**********
sudo sysctl -w net.core.wmem_default=**********
```

```bash
colcon build --symlink-install --packages-select aqita_barcodes
source install/setup.bash
```

To test a YOLOv8 based barcode detection model follow these steps

1. Create an assets folder for pictures and model files using a terminal outside of the running container

```bash
mkdir -p ~/ROS/aqita-dev/aqita_assets/models/yolov8/barcodes/
mkdir -p ~/ROS/aqita-dev/aqita_assets/pictures/barcodes/
```

2. Get the [onnx](https://uvionix.sharepoint.com/:u:/s/AQITADev/EX5fAOhHq41Pmn2IQXTN3QkBtTD6mBUYTKe5OeL9Rc5_NQ?e=PqTzp8) and the [plan](https://uvionix.sharepoint.com/:u:/s/AQITADev/EZujBxlHZHVBj5wdU2bjUSABLyzzIHfhlnR4RTuzU4PYTg?e=gQZsQa) model files and place them inside the "~/ROS/aqita-dev/aqita_assets/models/yolov8/barcodes/" directory

3. Get example [images](https://uvionix.sharepoint.com/:f:/s/AQITADev/Ei3ivVNJHxZIqQLF9Z2qRVcBTiFKhJHTJo_xqQ3t12XUfQ?e=tSMtnX) and place them inside the "~/ROS/aqita-dev/aqita_assets/pictures/barcodes/" directory. Rename all images in this directory following the pattern IMG_01.jpeg, IMG_02.jpeg, IMG_03.jpeg ...

4. Inside the container run the test launch file

```bash
ros2 launch aqita_barcodes test_yolov8_barcode_detector.launch.py image_publisher_pub_rate:=25
```
A message with the total number of barcode detections should appear in the terminal. To visualize the detections bounding boxes run rqt in another terminal attached to the running conatiner 

```bash
rqt -s rqt_image_view
```
