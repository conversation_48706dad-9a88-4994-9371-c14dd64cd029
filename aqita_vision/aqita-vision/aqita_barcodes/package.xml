<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>aqita_barcodes</name>
  <version>0.1.0</version>
  <description>Barcode recognition package</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>TODO: License declaration</license>

  <buildtool_depend>ament_cmake_auto</buildtool_depend>
  <depend>rclcpp</depend>
  <depend>rclcpp_components</depend>
  <depend>message_filters</depend>
  <depend>std_msgs</depend>
  <depend>vision_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>tf2_geometry_msgs</depend>
  <depend>image_transport</depend>
  <depend>isaac_ros_managed_nitros</depend>
  <depend>isaac_ros_nitros_tensor_list_type</depend>
  <depend>OpenCV</depend>
  <depend>cv_bridge</depend>
  <depend>aqita_interfaces</depend>
  <depend>nav2_aqita_msgs</depend>


  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>