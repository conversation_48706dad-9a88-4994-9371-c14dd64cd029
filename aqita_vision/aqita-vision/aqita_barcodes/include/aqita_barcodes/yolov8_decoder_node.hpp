
// TODO: License description

#ifndef AQITA_ROS_INFERENCE__YOLOV8_DECODER_NODE_HPP_
#define AQITA_ROS_INFERENCE__YOLOV8_DECODER_NODE_HPP_

#include <memory>
#include <string>
#include "rclcpp/rclcpp.hpp"
#include "isaac_ros_managed_nitros/managed_nitros_subscriber.hpp"
#include "std_msgs/msg/string.hpp"
#include "vision_msgs/msg/detection2_d_array.hpp"
#include "isaac_ros_nitros_tensor_list_type/nitros_tensor_list_view.hpp"

namespace aqita
{
namespace ros2
{
namespace inference
{

/**
 * @brief Node for decoding the inference results from a YOLOv8 model and publishing the results as a Detection2DArray message topic
 * @param input_tensor_name name of the input tensor for the decoder node
 * @param tensor_rt_output_tensors_topic name of the output tensors topic published by TensorRT as an inference result
 * @param confidence_threshold threshold used to filter boxes by score in the non maximum suppression algorithm
 * @param nms_threshold threshold used in non maximum suppression
 * @param nms_eta coefficient in adaptive threshold formula of the non maximum suppression algorithm
 * @param num_classes number of classes on which the model was trained on
 * @param num_anchors total number of predictions per image
 * @param output_detections_topic name of the topic on which the Detection2DArray messages will be published
*/
class YoloV8DecoderNode : public rclcpp::Node
{
public:
  // constructor
  explicit YoloV8DecoderNode(const rclcpp::NodeOptions options = rclcpp::NodeOptions());

  // destructor
  ~YoloV8DecoderNode();

private:
  // callback function to process new tensor messages
  void InputCallback(const nvidia::isaac_ros::nitros::NitrosTensorListView & msg);

  // subscription to input NitrosTensorList messages
  std::shared_ptr<nvidia::isaac_ros::nitros::ManagedNitrosSubscriber<
      nvidia::isaac_ros::nitros::NitrosTensorListView>> nitros_sub_;

  // publisher for output Detection2DArray messages
  rclcpp::Publisher<vision_msgs::msg::Detection2DArray>::SharedPtr detections_publisher_;

  // node parameters
  std::string input_tensor_name_;
  std::string tensor_rt_output_tensors_topic_;
  std::string output_detections_topic_;
  double confidence_threshold_;
  double nms_threshold_;
  double nms_eta_;
  int num_classes_;
  int num_anchors_;
};

}  // namespace inference
}  // namespace ros2
}  // namespace aqita

#endif  // AQITA_ROS_INFERENCE__YOLOV8_DECODER_NODE_HPP_