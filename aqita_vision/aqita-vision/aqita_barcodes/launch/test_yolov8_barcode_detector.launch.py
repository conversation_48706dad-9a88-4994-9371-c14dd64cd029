
# TODO: License description

# NOTE: Launch file for testing a barcode detection YOLOv8 model

from launch import LaunchDescription
from launch_ros.actions import LoadComposableN<PERSON>, Node
from launch_ros.descriptions import ComposableNode
from launch.actions import Declare<PERSON>aunchArgument, OpaqueFunction
from launch.conditions import UnlessCondition
from launch.substitutions import LaunchConfiguration

def launch_function(context):

    # Create the launch configuration substitutions
    image_topic = LaunchConfiguration('image_topic').perform(context)
    camera_info_topic = LaunchConfiguration('camera_info_topic').perform(context)
    input_image_width = LaunchConfiguration('input_image_width')
    input_image_height = LaunchConfiguration('input_image_height')
    network_image_width = LaunchConfiguration('network_image_width')
    network_image_height = LaunchConfiguration('network_image_height')
    model_file_path = LaunchConfiguration('model_file_path')
    engine_file_path = LaunchConfiguration('engine_file_path')
    model_input_tensor_name = LaunchConfiguration('model_input_tensor_name').perform(context)
    model_output_tensor_name = LaunchConfiguration('model_output_tensor_name').perform(context)
    num_classes = LaunchConfiguration('num_classes')
    num_anchors = LaunchConfiguration('num_anchors')
    tensor_rt_verbose_logging = LaunchConfiguration('tensor_rt_verbose_logging')
    force_engine_update = LaunchConfiguration('force_engine_update')
    attach_to_shared_component_container = LaunchConfiguration('attach_to_shared_component_container')
    component_container_name = LaunchConfiguration('component_container_name').perform(context)
    run_bbox_visualizer = LaunchConfiguration('run_bbox_visualizer').perform(context)
    bbox_visualizer_min_conf = LaunchConfiguration('bbox_visualizer_min_conf')
    bbox_visualizer_sync_queue_size = LaunchConfiguration('bbox_visualizer_sync_queue_size')
    bbox_visualizer_sync_slop = LaunchConfiguration('bbox_visualizer_sync_slop')
    run_image_publisher = LaunchConfiguration('run_image_publisher').perform(context)
    image_publisher_filename = LaunchConfiguration('image_publisher_filename')
    image_publisher_pub_rate = LaunchConfiguration('image_publisher_pub_rate')

    # If we do not attach to a shared component container we have to create our own container
    inference_container = Node(
        name=component_container_name,
        package='rclcpp_components',
        executable='component_container_mt',
        output='screen',
        condition=UnlessCondition(attach_to_shared_component_container)
    )

    # Create the image resize node
    resize_node = ComposableNode(
        name='infer_image_resize_node',
        package='isaac_ros_image_proc',
        plugin='nvidia::isaac_ros::image_proc::ResizeNode',
        parameters=[{
            'input_width': input_image_width,
            'input_height': input_image_height,
            'output_width': network_image_width,
            'output_height': network_image_height,
            'disable_padding': False,
            'num_blocks': 40,
            'keep_aspect_ratio': False
        }],
        remappings=[('image', image_topic),
                    ('camera_info', camera_info_topic),
                    ('resize/image', '/infer/yolov8/input_image/image_rect_raw'),
                    ('resize/camera_info', '/infer/yolov8/input_image/camera_info')]
    )

    # Create the DNN encoder node
    encoder_node = ComposableNode(
        name='infer_dnn_image_encoder',
        package='isaac_ros_dnn_image_encoder',
        plugin='nvidia::isaac_ros::dnn_inference::DnnImageEncoderNode',
        remappings=[('encoded_tensor', '/infer/yolov8/tensor_rt_input_stream'),('image','/infer/yolov8/input_image/image_rect_raw')],
        parameters=[{
            'input_image_width': input_image_width,
            'input_image_height': input_image_height,
            'network_image_width': network_image_width,
            'network_image_height': network_image_height,
            'image_mean': [0.0, 0.0, 0.0],
            'image_stddev': [1.0, 1.0, 1.0],
            'bbox_loc' : [-1, -1]
        }]
    )

    # Create the tensor RT inference node
    tensor_rt_node = ComposableNode(
        name='infer_tensor_rt_node',
        package='isaac_ros_tensor_rt',
        plugin='nvidia::isaac_ros::dnn_inference::TensorRTNode',
        remappings=[('tensor_pub','/infer/yolov8/tensor_rt_input_stream'),('tensor_sub','/infer/yolov8/tensor_rt_output_stream')],
        parameters=[{
            'model_file_path': model_file_path,
            'engine_file_path': engine_file_path,
            'output_binding_names': [model_output_tensor_name],
            'output_tensor_names': ['infer_yolov8_output_tensor'],
            'input_binding_names': [model_input_tensor_name],
            'input_tensor_names': ['input_tensor'],
            'verbose': tensor_rt_verbose_logging,
            'force_engine_update': force_engine_update
        }]
    )

    # Create the decoder node
    decoder_node = ComposableNode(
        name='infer_yolov8_decoder_node',
        package='aqita_barcodes',
        plugin='aqita::ros2::inference::YoloV8DecoderNode',
        parameters=[{
            'input_tensor_name': 'infer_yolov8_output_tensor',
            'tensor_rt_output_tensors_topic': '/infer/yolov8/tensor_rt_output_stream',
            'confidence_threshold': 0.25,
            'nms_threshold': 0.45,
            'nms_eta': 5.0,
            'num_classes': num_classes,
            'num_anchors': num_anchors,
            'output_detections_topic': '/infer/yolov8/output/detections'
        }]
    )

    # Create the resize node for visualization
    infer_vis_resize_width = 640
    infer_vis_resize_height = 640
    infer_vis_resize_node = ComposableNode(
        name='infer_vis_image_resize_node',
        package='isaac_ros_image_proc',
        plugin='nvidia::isaac_ros::image_proc::ResizeNode',
        parameters=[{
            'input_width': input_image_width,
            'input_height': input_image_height,
            'output_width': infer_vis_resize_width,
            'output_height': infer_vis_resize_height,
            'keep_aspect_ratio': False,
            'disable_padding': False,
            'num_blocks': 40
        }],
        remappings=[('image', image_topic),
                    ('camera_info', camera_info_topic),
                    ('resize/image', '/infer/yolov8/input_image_low_res_rgb8/image_rect_raw'),
                    ('resize/camera_info', '/infer/yolov8/input_image_low_res_rgb8/camera_info')]
    )

    # # Create the inference visualizer node
    # infer_bbox_visualizer_node = ComposableNode(
    #     name='bbox_visualizer_node',
    #     package='aqita_barcodes',
    #     plugin='aqita::ros2::inference::ImageWithBoundingBoxes',
    #     parameters=[{
    #         'image_topic': '/infer/yolov8/input_image_low_res_rgb8/image_rect_raw',
    #         'detections_topic': '/infer/yolov8/output/detections',
    #         'output_image_topic': '/infer/yolov8/output/image_with_bboxes',
    #         'sync_queue_size': bbox_visualizer_sync_queue_size,
    #         'sync_slop': bbox_visualizer_sync_slop,
    #         'min_conf': bbox_visualizer_min_conf,
    #         'object_detector_img_size': network_image_width,
    #         'bbox_thickness': 2
    #     }]
    # )

    # Create the processing crops node visualizer node
    infer_bbox_visualizer_node = ComposableNode(
        name='bbox_viprocess_crops_node',
        package='aqita_barcodes',
        plugin='aqita::ros2::inference::ProcessCrops',
        parameters=[{
            'image_topic': "image_raw",
            'detections_topic': '/infer/yolov8/output/detections',
            'output_boxes_topic': '/infer/yolov8/output/crops',
            'sync_queue_size': bbox_visualizer_sync_queue_size,
            'sync_slop': bbox_visualizer_sync_slop,
            'min_conf': bbox_visualizer_min_conf,
            'input_img_height': 2560,
            'input_img_width': 2560
        }]
    )

    # Image publisher node
    img_publisher = Node(
        package='image_publisher',
        executable='image_publisher_node',
        output='screen',
        remappings=[('image_raw', image_topic),('camera_info', camera_info_topic)],
        parameters=[{
            'filename': image_publisher_filename,
            'publish_rate': float(image_publisher_pub_rate.perform(context))}])

    # Create the launch action
    composable_nodes = [resize_node, encoder_node, tensor_rt_node, decoder_node]

    if run_bbox_visualizer.lower() == 'true':
        # composable_nodes.append(infer_vis_resize_node)
        composable_nodes.append(infer_bbox_visualizer_node)

    load_composable_nodes = LoadComposableNodes(
        target_container=component_container_name,
        composable_node_descriptions=composable_nodes
    )

    launch_actions = [inference_container, load_composable_nodes]

    if run_image_publisher.lower() == 'true':
        launch_actions.append(img_publisher)

    return launch_actions

def generate_launch_description():

    # Declare the launch arguments
    launch_args = [
        DeclareLaunchArgument(
            'image_topic',
            default_value='image_raw',
            description='Name of the input image topic'),
        DeclareLaunchArgument(
            'camera_info_topic',
            default_value='camera_info',
            description='Name of the input image camera info topic'),
        DeclareLaunchArgument(
            'input_image_width',
            default_value='3024',
            description='Input image width'),
        DeclareLaunchArgument(
            'input_image_height',
            default_value='4032',
            description='Input image height'),
        DeclareLaunchArgument(
            'network_image_width',
            default_value='2560',
            description='Width of the image to be provided to the object detection model'),
        DeclareLaunchArgument(
            'network_image_height',
            default_value='2560',
            description='Height of the image to be provided to the object detection model'),
        DeclareLaunchArgument(
            'model_file_path',
            default_value='/workspace/src/aqita-ros/aqita_vision/aqita_assets/models/yolov8/barcodes/barcodes-yolov8s.onnx',
            description='The absolute path to the ONNX model file'),
        DeclareLaunchArgument(
            'engine_file_path',
            default_value='/workspace/src/aqita-ros/aqita_vision/aqita_assets/models/yolov8/barcodes/barcodes-yolov8s.plan',
            description='The absolute path to the TensorRT engine file'),
        DeclareLaunchArgument(
            'model_input_tensor_name',
            default_value='images',
            description='Name of the YOLOv8 model input tensor (can be obtained by loading the ONNX model file in https://netron.app/)'),
        DeclareLaunchArgument(
            'model_output_tensor_name',
            default_value='output0',
            description='Name of the YOLOv8 model output tensor (can be obtained by loading the ONNX model file in https://netron.app/)'),
        DeclareLaunchArgument(
            'num_classes',
            default_value='1',
            description='Number of classes on which the inference model was trained on'),
        DeclareLaunchArgument(
            'num_anchors',
            default_value='134400',
            description='Total number of predictions per image that the model generates'),
        DeclareLaunchArgument(
            'tensor_rt_verbose_logging',
            default_value='False',
            description='Whether TensorRT should verbosely log or not'),
        DeclareLaunchArgument(
            'force_engine_update',
            default_value='False',
            description='Whether TensorRT should update the TensorRT engine file or not'),
        DeclareLaunchArgument(
            name='attach_to_shared_component_container',
            default_value='False',
            description='Whether to attach the node to a shared component container'),
        DeclareLaunchArgument(
            name='component_container_name',
            default_value='yolov8_inference_container',
            description='Name of the container for the node'),
        DeclareLaunchArgument(
            name='run_bbox_visualizer',
            default_value='True',
            description='Whether to run the bounding-box visualization node'),
        DeclareLaunchArgument(
            name='bbox_visualizer_min_conf',
            default_value='0.5',
            description='Detections with confidence score lower than this threshold will not be visualized'),
        DeclareLaunchArgument(
            name='bbox_visualizer_sync_queue_size',
            default_value='20',
            description='Queue size used in the synchronization between the input image and the detection messages'),
        DeclareLaunchArgument(
            name='bbox_visualizer_sync_slop',
            default_value='0.05',
            description='Maximum sync delay between the input image and the detection messages in seconds'),
        DeclareLaunchArgument(
            name='run_image_publisher',
            default_value='True',
            description='Whether to run the image publisher node'),
        DeclareLaunchArgument(
            name='image_publisher_filename',
            default_value='/workspace/src/aqita-ros/aqita_vision/aqita_assets/pictures/barcodes/IMG_01.jpeg',
            description=''),
        DeclareLaunchArgument(
            name='image_publisher_pub_rate',
            default_value='10.0',
            description='Rate of the image published by the image publisher node')
    ]

    return LaunchDescription(launch_args + [OpaqueFunction(function=launch_function)])
