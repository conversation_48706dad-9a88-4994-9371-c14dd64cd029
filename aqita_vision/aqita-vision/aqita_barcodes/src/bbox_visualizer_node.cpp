
// TODO: License description

#include <rclcpp/rclcpp.hpp>
#include <rclcpp/duration.hpp>
#include <image_transport/image_transport.hpp>
#include <message_filters/subscriber.h>
#include <message_filters/sync_policies/approximate_time.h>
#include <message_filters/synchronizer.h>
#include <vision_msgs/msg/detection2_d_array.hpp>
#include <sensor_msgs/msg/image.hpp>
#include <cv_bridge/cv_bridge.h>
#include <opencv2/opencv.hpp>

namespace aqita
{
namespace ros2
{
namespace inference
{

/** 
 * @brief Node for drawing bounding boxes on an input image using the object detection inference results
 * @param image_topic topic name of the input image on which the bounding boxes will be drawn
 * @param detections_topic topic name of the object detection inference results as Detection2DArray messages
 * @param output_image_topic topic name of the output image with the bounding boxes to be published
 * @param sync_queue_size queue size used in the synchronization between the input image and the detection messages
 * @param sync_slop maximum sync delay between the input image and the detection messages in seconds
 * @param min_conf detections are skipped (no bbox is drawn) if the confidence score is lower than this threshold
 * @param object_detector_img_size size of the image on which the object detector operates (rectangular image is assumed)
 * @param bbox_thickness desired thickness of the bounding boxes to draw
*/
class ImageWithBoundingBoxes : public rclcpp::Node
{
public:
    // constructor
    explicit ImageWithBoundingBoxes(const rclcpp::NodeOptions &options) : Node("bbox_visualizer_node", options)
    {
        RCLCPP_INFO(get_logger(), "Initializing bounding-box visualizer node...");

        // declare the parameters
        declare_parameter<std::string>("image_topic", "/camera/image/image_rect_raw");
        declare_parameter<std::string>("detections_topic", "detections");
        declare_parameter<std::string>("output_image_topic", "annotated_image");
        declare_parameter<int>("sync_queue_size", 10);
        declare_parameter<double>("sync_slop", 0.05);
        declare_parameter<double>("min_conf", 0.5);
        declare_parameter<int>("object_detector_img_size", 640);
        declare_parameter<int>("bbox_thickness", 2);

        // get the parameters
        image_topic_ = get_parameter("image_topic").as_string();
        detections_topic_ = get_parameter("detections_topic").as_string();
        output_image_topic_ = get_parameter("output_image_topic").as_string();
        sync_queue_size_ = get_parameter("sync_queue_size").as_int();
        sync_slop_ = get_parameter("sync_slop").as_double();
        min_conf_ = get_parameter("min_conf").as_double();
        object_detector_img_size_ = get_parameter("object_detector_img_size").as_int();
        bbox_thickness_ = get_parameter("bbox_thickness").as_int();

        // subscribers
        image_sub_ = std::make_shared<message_filters::Subscriber<sensor_msgs::msg::Image>>(this, image_topic_);
        detections_sub_ = std::make_shared<message_filters::Subscriber<vision_msgs::msg::Detection2DArray>>(this, detections_topic_);

        // time synchronizer
        time_sync_ = std::make_shared<message_filters::Synchronizer<SyncPolicy>>(SyncPolicy(sync_queue_size_), *image_sub_, *detections_sub_);
        time_sync_->setMaxIntervalDuration(rclcpp::Duration::from_seconds(sync_slop_));
        time_sync_->registerCallback(std::bind(&ImageWithBoundingBoxes::detections_callback, this, std::placeholders::_1, std::placeholders::_2));

        // annotated image publisher
        image_pub_ = image_transport::create_publisher(this, output_image_topic_);
    }

    // destructor
    ~ImageWithBoundingBoxes(){}

private:
    using SyncPolicy = message_filters::sync_policies::ApproximateTime<sensor_msgs::msg::Image, vision_msgs::msg::Detection2DArray>;

    // time sync callback function
    void detections_callback(const sensor_msgs::msg::Image::ConstSharedPtr &image_msg,
                             const vision_msgs::msg::Detection2DArray::ConstSharedPtr &detections_msg)
    {
        // convert the ROS image message to OpenCV image
        cv_bridge::CvImagePtr cv_ptr;
        try
        {
            cv_ptr = cv_bridge::toCvCopy(image_msg);
        }
        catch (const cv_bridge::Exception &e)
        {
            RCLCPP_ERROR(get_logger(), "CV Bridge error: %s", e.what());
            return;
        }

        // convert the image to a 3-channel gray image for drawing (OpenCV expects 3-channel for colored rectangles)
        cv::Mat gray_image, gray_image_bgr8;
        cv::cvtColor(cv_ptr->image, gray_image, cv::COLOR_BGR2GRAY);
        cv::cvtColor(gray_image, gray_image_bgr8, cv::COLOR_GRAY2BGR);

        // get the total number of detections
        int num_detections = detections_msg->detections.size();
        
        // process all detections
        for (const auto &detection : detections_msg->detections)
        {
            // skip detections with insufficient confidence score
            if ( detection.results[0].hypothesis.score < min_conf_ )
            {
                num_detections = std::max(num_detections - 1, 0);
                continue;
            }
        
            // get the bbox parameters
            double center_x = image_msg->width * (detection.bbox.center.position.x) / object_detector_img_size_;
            double center_y = image_msg->height * (detection.bbox.center.position.y) / object_detector_img_size_;
            double width = image_msg->width * detection.bbox.size_x / object_detector_img_size_;
            double height = image_msg->height * detection.bbox.size_y / object_detector_img_size_;
                   
            // draw a bounding box
            int x = static_cast<int>(center_x - width * 0.5);
            int y = static_cast<int>(center_y - height * 0.5);
            cv::rectangle(gray_image_bgr8, cv::Rect(x, y, static_cast<int>(width), static_cast<int>(height)), cv::Scalar(0, 255, 0), bbox_thickness_);
        }

        RCLCPP_INFO(get_logger(), "Detections received: %d", num_detections);
        
        // publish the annotated image
        cv_bridge::CvImage annotated_image_msg;
        annotated_image_msg.header = image_msg->header;
        annotated_image_msg.encoding = sensor_msgs::image_encodings::BGR8;
        annotated_image_msg.image = gray_image_bgr8;
        image_pub_.publish(annotated_image_msg.toImageMsg());
    }

    std::shared_ptr<message_filters::Subscriber<sensor_msgs::msg::Image>> image_sub_;
    std::shared_ptr<message_filters::Subscriber<vision_msgs::msg::Detection2DArray>> detections_sub_;
    std::shared_ptr<message_filters::Synchronizer<SyncPolicy>> time_sync_;
    image_transport::Publisher image_pub_;

    // node parameters
    std::string image_topic_;
    std::string detections_topic_;
    std::string output_image_topic_;
    double sync_slop_;
    double min_conf_;
    int sync_queue_size_;
    int object_detector_img_size_;
    int bbox_thickness_;
};

}   // namespace inference
}   // namespace ros2
}   // namespace aqita

// export the node as a plugin
#include "rclcpp_components/register_node_macro.hpp"
RCLCPP_COMPONENTS_REGISTER_NODE(aqita::ros2::inference::ImageWithBoundingBoxes)
