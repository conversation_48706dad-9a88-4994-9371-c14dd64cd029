cmake_minimum_required(VERSION 3.5)
# cspell:ignore dpkg

project(PhenixDecoderCPPWrapper  CXX)


set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED True)
# set(CMAKE_VERBOSE_MAKEFILE ON)

#set(SPECIFIC_ENV ubuntu16)
#set(SPECIFIC_ENV ubuntu18)
#set(SPECIFIC_ENV CUDA)
#set(SPECIFIC_ENV amazonlinux2)

set (PHENIX_DECODER_API_DIR /workspace/src/aqita-ros/aqita_vision/aqita_assets/models/PhenixDecoderAPI)
if (SPECIFIC_ENV)
    set (PHENIX_DECODER_LIBS_DIR_SUFFIX     _${SPECIFIC_ENV} )
else()
    set (PHENIX_DECODER_LIBS_DIR_SUFFIX     "" )
endif()


set(DECODER_WRAPPER_FILES Decoder.cpp 
                          DecoderManager.cpp 
                          StatusMessage.cpp 
                          Symid.cpp 
                          PartialSymid.cpp)


add_library(PhenixDecoderCPPWrapper STATIC   ${DECODER_WRAPPER_FILES})


target_include_directories(PhenixDecoderCPPWrapper PUBLIC   . )
target_include_directories(PhenixDecoderCPPWrapper PUBLIC   ${PHENIX_DECODER_API_DIR}/include )


if (WIN32)
    # assuming that VisualStudio generator is used for Windows build.
    # if not, you need to replace ${CMAKE_VS_PLATFORM_NAME} by "x64" or "Win32" accordingly to your platform
    set (DECODER_BUILD_RELATIVE_DIR       ${CMAKE_SYSTEM_NAME}${SYSTEM_NAME_VARIANT}/${CMAKE_VS_PLATFORM_NAME})
    set (PHENIX_DECODER_LIBS_DIR          ${PHENIX_DECODER_API_DIR}/lib/${DECODER_BUILD_RELATIVE_DIR}${PHENIX_DECODER_LIBS_DIR_SUFFIX})
    set (PHENIX_DECODER_PATHNAME          ${PHENIX_DECODER_LIBS_DIR}/PhenixDecoder${CMAKE_SHARED_LIBRARY_SUFFIX} )
    set (DECODER_SHARED_LIBRARY_TO_COPY   ${PHENIX_DECODER_PATHNAME}  PARENT_SCOPE)

    target_link_libraries(PhenixDecoderCPPWrapper PRIVATE ${PHENIX_DECODER_LIBS_DIR}/PhenixDecoder.lib)

else()
    if (APPLE)
        set (DECODER_BUILD_RELATIVE_DIR   ${CMAKE_SYSTEM_NAME}${SYSTEM_NAME_VARIANT}/${CMAKE_SYSTEM_PROCESSOR})
        
    else()
        if (NOT DEFINED CMAKE_CXX_LIBRARY_ARCHITECTURE OR CMAKE_CXX_LIBRARY_ARCHITECTURE STREQUAL "")
            if (CMAKE_SYSTEM_NAME STREQUAL Linux)
                find_program(DPKG_ARCHITECTURE_PROGRAM dpkg-architecture)
                if (DPKG_ARCHITECTURE_PROGRAM)
                    message(STATUS "DPKG_ARCHITECTURE_PROGRAM found: ${DPKG_ARCHITECTURE_PROGRAM}")
                    execute_process(
                        COMMAND ${DPKG_ARCHITECTURE_PROGRAM} -q DEB_TARGET_GNU_TYPE
                        OUTPUT_VARIABLE   DEB_TARGET_GNU_TYPE
                        OUTPUT_STRIP_TRAILING_WHITESPACE)
            
                    set (CMAKE_CXX_LIBRARY_ARCHITECTURE   ${DEB_TARGET_GNU_TYPE})
                    message(STATUS "CMAKE_CXX_LIBRARY_ARCHITECTURE not defined, set to '${CMAKE_CXX_LIBRARY_ARCHITECTURE}'")
                    message(STATUS )

                else()
                    # variable CMAKE_CXX_LIBRARY_ARCHITECTURE may be empty for some Linux distribution (i.e. ArchLinux)
                    # if so, let assume that architecture is "x86_64-linux-gnu"
                    set (CMAKE_CXX_LIBRARY_ARCHITECTURE   x86_64-linux-gnu)
                    message(WARNING "CMAKE_CXX_LIBRARY_ARCHITECTURE not defined, defaulting to '${CMAKE_CXX_LIBRARY_ARCHITECTURE}'")
                endif()
            else()
                message(FATAL_ERROR " CMAKE_CXX_LIBRARY_ARCHITECTURE not defined or empty\n"
                                    " You need to define it manually in CPPWrapper/CMakeLists.txt file\n"
                                    " It can be any folder name found in 'SDK.x.y.z/PhenixDecoderAPI/libs/Linux'\n"
                                    " (i.e. aarch64-linux-gnu, x86_64-linux-gnu_ubuntu16 ...)" )
            endif()
        endif()    
    
        set (DECODER_BUILD_RELATIVE_DIR   ${CMAKE_SYSTEM_NAME}${SYSTEM_NAME_VARIANT}/${CMAKE_CXX_LIBRARY_ARCHITECTURE})
    endif()
    if (SPECIFIC_ENV)
        set(DECODER_BUILD_RELATIVE_DIR   ${DECODER_BUILD_RELATIVE_DIR}_${SPECIFIC_ENV})
    endif()


    set (PHENIX_DECODER_LIBS_DIR          ${PHENIX_DECODER_API_DIR}/lib/${DECODER_BUILD_RELATIVE_DIR}${PHENIX_DECODER_LIBS_DIR_SUFFIX})
    set (PHENIX_DECODER_PATHNAME          ${PHENIX_DECODER_LIBS_DIR}/libPhenixDecoder${CMAKE_SHARED_LIBRARY_SUFFIX})
    set (DECODER_SHARED_LIBRARY_TO_COPY   ${PHENIX_DECODER_PATHNAME} )
    set (DECODER_SHARED_LIBRARY_TO_COPY   ${PHENIX_DECODER_PATHNAME}  PARENT_SCOPE)
    
    target_link_libraries(PhenixDecoderCPPWrapper  ${PHENIX_DECODER_PATHNAME}   m)

endif()

set (DECODER_BUILD_RELATIVE_DIR  ${DECODER_BUILD_RELATIVE_DIR} PARENT_SCOPE)
set (PHENIX_DECODER_PATHNAME     ${PHENIX_DECODER_PATHNAME} PARENT_SCOPE)


message(STATUS "PROJECT_NAME:                      ${PROJECT_NAME}")
message(STATUS "   CMAKE_HOST_SYSTEM_NAME:         ${CMAKE_HOST_SYSTEM_NAME}")
message(STATUS "")
message(STATUS "   CMAKE_CXX_COMPILER:             ${CMAKE_CXX_COMPILER}")
message(STATUS "   CMAKE_CXX_COMPILER_VERSION:     ${CMAKE_CXX_COMPILER_VERSION}")
message(STATUS "   CMAKE_CXX_FLAGS:                ${CMAKE_CXX_FLAGS}")
message(STATUS "")
message(STATUS "   CMAKE_SYSTEM_PROCESSOR:         ${CMAKE_SYSTEM_PROCESSOR}")
message(STATUS "   SPECIFIC_ENV:                   ${SPECIFIC_ENV}")
message(STATUS "")
message(STATUS "   PHENIX_DECODER_API_DIR:         ${PHENIX_DECODER_API_DIR}")
message(STATUS "   PHENIX_DECODER_LIBS_DIR_SUFFIX: ${PHENIX_DECODER_LIBS_DIR_SUFFIX}")
message(STATUS "   PHENIX_DECODER_LIBS_DIR:        ${PHENIX_DECODER_LIBS_DIR}")
message(STATUS "")
message(STATUS "   DECODER_SHARED_LIBRARY_TO_COPY: ${DECODER_SHARED_LIBRARY_TO_COPY}")
message(STATUS "")



