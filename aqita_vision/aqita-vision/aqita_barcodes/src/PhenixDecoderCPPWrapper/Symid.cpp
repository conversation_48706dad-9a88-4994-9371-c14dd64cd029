#include "Symid.h"

using namespace viziotix;
std::string fnx::getSymidName(fnxSymid symid)
{
    switch (symid)
    {
    case fnxSymid::FNX_ID_UNKNOWN:
        return "Unknown symbology";
    case fnxSymid::FNX_ID_DATAMATRIX:
        return "Datamatrix";
    case fnxSymid::FNX_ID_CODE39:
        return "Code 39";
    case fnxSymid::FNX_ID_QRCODE:
        return "Qr Code";
    case fnxSymid::FNX_ID_UPCA:
        return "UPC-A";
    case fnxSymid::FNX_ID_UPCE:
        return "UPC-E";
    case fnxSymid::FNX_ID_EAN13:
        return "EAN-13";
    case fnxSymid::FNX_ID_EAN8:
        return "EAN-8";
    case fnxSymid::FNX_ID_ITF:
        return "Interleave 2 of 5";
    case fnxSymid::FNX_ID_CODE128:
        return "Code 128";
    case fnxSymid::FNX_ID_PDF417:
        return "PDF417";
    case fnxSymid::FNX_ID_GS1_DATABAR:
        return "GS1 Databar";
    case fnxSymid::FNX_ID_GS1_DATABAR_EX:
        return "GS1 Databar Expanded";
    case fnxSymid::FNX_ID_GS1_DATABAR_LIMITED:
        return "GS1 Databar Limited";
    case fnxSymid::FNX_ID_AZTEC:
        return "Aztec Code";
    case fnxSymid::FNX_ID_GS1_128:
        return "GS1-128";
    case fnxSymid::FNX_ID_GS1_DATAMATRIX:
        return "GS1 Datamatrix";
    case fnxSymid::FNX_ID_MSI:
        return "MSI";
    case fnxSymid::FNX_ID_CODE32:
        return "Code 32";
    case fnxSymid::FNX_ID_ITF_14:
        return "ITF-14";
    case fnxSymid::FNX_ID_ISBT128:
        return "ISBT128";
    case fnxSymid::FNX_ID_UPCA_ADDON2:
        return "UPC-A with Addon 2";
    case fnxSymid::FNX_ID_UPCA_ADDON5:
        return "UPC-A with Addon 5";
    case fnxSymid::FNX_ID_UPCE_ADDON2:
        return "UPC-E with Addon 2";
    case fnxSymid::FNX_ID_UPCE_ADDON5:
        return "UPC-E with Addon 5";
    case fnxSymid::FNX_ID_EAN13_ADDON2:
        return "EAN-13 with Addon 2";
    case fnxSymid::FNX_ID_EAN13_ADDON5:
        return "EAN-13 with Addon 5";
    case fnxSymid::FNX_ID_CODE93:
        return "Code 93";
    case fnxSymid::FNX_ID_CODABAR:
        return "Codabar";
    case fnxSymid::FNX_ID_OCR_MRZ:
        return "OCR-MRZ";
    default:
        return "Unknown symbology";
    }
}
