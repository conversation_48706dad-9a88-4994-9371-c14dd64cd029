#pragma once

#include <string>

#include "PhenixDecoderAPI.h"
#include "Result.h"
#include "PhenixPartialDecodeAPI.h"
#include "PartialResult.h"

namespace viziotix
{
    namespace fnx
    {
        class DecoderListener
        {
        public:
            virtual void OnResultReceived(Result result) =0;
        };

        class PartialDecodeListener
        {
        public:
            virtual void OnResultReceived(PartialResult result) = 0;
        };
    }
}



