#pragma once

#include "DecoderListener.h"

namespace viziotix
{
    namespace fnx
    {
        using Image = fnxImage;
        using ImageFormat = fnxImageFormat;
        using Status = fnxStatus;
        using Point = fnxPoint;
        using SettingTag = fnxSettingTag;

        class Decoder
        {
        private:
            friend class DecoderManager;

            int m_handle = 0;
            DecoderListener* m_listener = nullptr;
            PartialDecodeListener* m_listenerPartial = nullptr;
            Decoder(DecoderListener* listener, PartialDecodeListener* listenerPartial);
            ~Decoder();

            void Callback(const fnxResult* result, void* caller);
            void CallbackPartial(const fnxPartialResult* result, void* caller);

        public:

            Status processImage(Image& pImage);
            Status stopProcessing();
            Status readIntSetting(SettingTag tag, int& value);
            Status writeIntSetting(SettingTag tag, int value);
            Status readBufferSetting(SettingTag tag, unsigned char** buffer, int& nBytesInBuffer);
            Status writeBufferSetting(SettingTag tag, unsigned char* buffer, int nBytesInBuffer);
        };

    }
}


