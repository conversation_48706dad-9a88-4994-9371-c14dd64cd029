#pragma once

#include <string>
#include <cstring>

#include "PhenixDecoderAPI.h"
#include "Symid.h"

namespace viziotix
{
    namespace fnx
    {
        using Symid = fnxSymid;
        using Point = fnxPoint;

        class Result
        {
        private:
            std::string     m_resultString = "";
            Symid           m_symid = Symid::FNX_ID_UNKNOWN;
            unsigned int    m_format = 0;
            Point           m_corners[4] = { {0,0}, {0,0}, {0,0}, {0,0} };
            std::string     m_optDataString = "{}";

        public:
            Result(const std::string resultString, const Symid symbolId, const unsigned int format, const Point corners[4], const std::string optDataString)
                : m_resultString(resultString), m_symid(symbolId), m_format(format), m_optDataString(optDataString)
            {
                std::memcpy(m_corners, corners, 4 * sizeof(Point));
            };

            std::string& getString()
            {
                return m_resultString;
            };

            std::string getSymbolName()
            {
                return getSymidName(m_symid);
            };

            unsigned int getStringFormat()
            {
                return m_format;
            };

            Point* getCorners()
            {
                return m_corners;
            };

            std::string& getOptDataString()
            {
                return m_optDataString;
            };
        };
    }
}



