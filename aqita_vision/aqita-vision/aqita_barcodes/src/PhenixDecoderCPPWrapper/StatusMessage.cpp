#include "StatusMessage.h"

#include <sstream>

using namespace viziotix;
std::string  fnx::getStatusMessage(fnxStatus status)
{
    switch (status)
    {
    case FNX_SUCCESS:                        return "Function call succeeded";
    case FNX_INVALID_HANDLE:                 return "An invalid decoder handle was passed to the function";
    case FNX_MEMORY_ALLOCATION_ERROR:        return "Decoder was not able to allocate memory";
    case FNX_UNSUPPORTED:                    return "The function is not supported in this version";
    case FNX_SETTING_TAG_UNKNOWN:            return "Provided SettingTag is not valid";
    case FNX_SETTING_VALUE_INVALID:          return "Provided value associated with this SettingTag is not valid";
    case FNX_IMAGE_FORMAT_NOT_SUPPORTED:     return "Input image format is not supported";
    case FNX_IMAGE_SIZE_NOT_SUPPORTED:       return "Image size is too small (< 48 pixels)";
    case FNX_SETTING_TAG_NOT_LICENSED:       return "Provided SettingTag is not licensed";
    case FNX_INVALID_CONTEXT:                return "Function call is invalid in current context";
    case FNX_LICENSE_ACTIVATION_FAILED:      return "The license activation failed";
    case FNX_LICENSE_EXPIRED:                return "The license has expired or system time has been tampered";
    case FNX_LICENSE_SUSPENDED:              return "The license has been suspended";
    case FNX_LICENSE_GRACE_PERIOD_OVER:      return "The grace period for server sync is over";
    case FNX_INVALID_FILE_PATH:              return "Invalid file path";
    case FNX_FILE_PERMISSION:                return "No permission to write to file.";
    case FNX_LICENSE_TIME_ERROR:             return "The system time and the network time are different";
    case FNX_LICENSE_NETWORK_ERROR:          return "The connection to the server failed due to network error";
    case FNX_LICENSE_REVOKED:                return "The license has been revoked";
    case FNX_LICENSE_INVALID_KEY:            return "The license key is invalid";
    case FNX_LICENSE_INVALID_TYPE:           return "Invalid license type. Make sure floating license is not being used";
    case FNX_LICENSE_ACTIVATION_LIMIT:       return "The license has reached its allowed activations limit";
    case FNX_LICENSE_ACTIVATION_NOT_FOUND:   return "The license activation was deleted on the server";
    case FNX_LICENSE_DEACTIVATION_LIMIT:     return "The license has reached it's allowed deactivations limit";
    case FNX_LICENSE_MACHINE_FINGERPRINT:    return "The device fingerprint has changed";
    case FNX_LICENSE_TIME_MODIFIED:          return "The system time has been tampered (backdated)";
    case FNX_LICENSE_RELEASE_VERSION_ERROR:  return "The release version is not allowed";
    case FNX_LICENSE_VM_ERROR:               return "Running in VM: please contact <NAME_EMAIL>";
    case FNX_LICENSE_COUNTRY_ERROR:          return "Country is not allowed";
    case FNX_LICENSE_IP_ERROR:               return "IP address is not allowed";
    case FNX_LICENSE_CONTAINER_ERROR:        return "Running in container: please contact <NAME_EMAIL>";
    case FNX_LICENSE_SERVER_ERROR:           return "Server error";
    case FNX_PA_APPLICATION_NAME_ERROR:      return "Post activation error: Application name error";
    case FNX_PA_RELEASE_VERSION_ERROR:       return "Post activation error: The release version is not allowed";
    case FNX_PA_PLATFORM_ERROR:              return "Post activation error: The platform is not allowed";
    case FNX_OP_LICENSE_ACTIVATION_FAILED:   return "The on-premise license activation failed";
    case FNX_OP_LICENSE_INVALID_URL:         return "Missing or invalid on-premise server url";
    case FNX_OP_LICENSE_TIME_ERROR:          return "The on-premise system time and the network time are different";
    case FNX_OP_LICENSE_NETWORK_ERROR:       return "The connection to the on-premise server failed due to network error";
    case FNX_OP_LICENSE_NOT_FOUND:           return "The on-premise license does not exist on server or has already expired (the request to refresh the license is delayed)";
    case FNX_OP_LICENSE_EXPIRED_INET:        return "The on-premise license lease has expired due to network error (the request to refresh the license fails due to network error)";
    case FNX_OP_LICENSE_ACTIVATION_LIMIT:    return "The on-premise license has reached its allowed activations limit";
    case FNX_OP_LICENSE_SERVER_ERROR:        return "On-premise server error";
    case FNX_OP_LICENSE_TIME_MODIFIED:       return "The on-premise server system time has been tampered (backdated)";
    case FNX_OP_LICENSE_NOT_ACTIVATED:       return "The on-premise server has not been activated using a license key";
    case FNX_OP_LICENSE_EXPIRED:             return "The on-premise server license has expired";
    case FNX_OP_LICENSE_SUSPENDED:           return "The on-premise server license has been suspended";
    case FNX_OP_LICENSE_GRACE_PERIOD_OVER:   return "The on-premise server grace period for server sync is over";
    }

    std::ostringstream stringStream;
    stringStream << "Status code "<< status << ", Please contact <NAME_EMAIL>";

    return stringStream.str();
}
