#pragma once

#include <string>

#include "Decoder.h"

namespace viziotix
{
    namespace fnx
    {
        class DecoderManager
        {
        public:
            static DecoderManager* get();
            
            std::string getLibraryVersion();
            Status      initializeLibrary();
            Status      deinitializeLibrary();
            Status      activateLicense(std::string key);
            Decoder*    createDecoder(DecoderListener* decoderListener, PartialDecodeListener* partialDecodeListener);
            Decoder*    createDecoder(DecoderListener* decoderListener);
            void        destroyDecoder(Decoder *decoder);
            Status      offlineActivationRequest(std::string key, std::string filepath);
            Status      offlineActivateLicense(std::string filepath);
        };
    }
}



