#include "DecoderManager.h"

using Status = fnxStatus;

using namespace viziotix::fnx;

DecoderManager* DecoderManager::get()
{
    static DecoderManager manager;    
    return &manager;
}

std::string DecoderManager::getLibraryVersion()
{
    return fnxGetLibraryVersion();
}

Status DecoderManager::initializeLibrary()
{
    return fnxInitializeLibrary();
}


Status DecoderManager::deinitializeLibrary()
{
    return fnxDeinitializeLibrary();
}


Status DecoderManager::activateLicense(std::string key)
{
    return fnxActivateLicense(key.c_str());
}

Decoder* DecoderManager::createDecoder(DecoderListener* decoderListener, PartialDecodeListener* partialDecodeListener)
{
    Decoder* decoder = new Decoder(decoderListener, partialDecodeListener);
    if (decoder->m_handle == 0)
    {
        // something went wrong inside decoder library, exit on error
        delete decoder;
        decoder = nullptr;
    }

    return decoder;
}

Decoder* DecoderManager::createDecoder(DecoderListener* decoderListener)
{
    return createDecoder(decoderListener, nullptr);
}

void DecoderManager::destroyDecoder(Decoder* decoder)
{
    // destroy decoder
    delete decoder;
}

Status DecoderManager::offlineActivationRequest(std::string key, std::string filepath)
{
    return fnxOfflineActivationRequest(key.c_str(), filepath.c_str());
}

Status DecoderManager::offlineActivateLicense(std::string filepath)
{
    return fnxOfflineActivateLicense(filepath.c_str());
}
