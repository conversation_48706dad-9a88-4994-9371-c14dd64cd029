#pragma once

#include <string>
#include <cstring>
#include <vector>

#include "PhenixPartialDecodeAPI.h"
#include "PartialSymid.h"

namespace viziotix
{
    namespace fnx
    {
        using PartialSymid = fnxPartialSymid;
        using Point = fnxPoint;
        using PartialDecodeType = fnxPartialDecodeType;

        class PartialMessage
        {
        private:
            std::string     m_resultString = "";
            unsigned int    m_format = 0;

        public:
            PartialMessage(const std::string resultString, const unsigned int format): m_resultString(resultString), m_format(format)
            {
            }

            std::string& getString()
            {
                return m_resultString;
            };

            unsigned int getStringFormat()
            {
                return m_format;
            };
        };

        class PartialResult
        {
        private:
            PartialDecodeType             m_partialDecodeType = PartialDecodeType::FNX_FULL_BARCODE;
            unsigned int                  m_barcodeScore = 0;
            std::vector<unsigned int>     m_codewords;
            std::vector<unsigned int>     m_codewordScores;
            PartialSymid                  m_symid = PartialSymid::FNX_ID_PARTIAL_UNKNOWN;
            std::vector <PartialMessage>  m_possibleMessages;
            Point                         m_corners[4] = { {0,0}, {0,0}, {0,0}, {0,0} };

        public:
            PartialResult(const fnxPartialResult* result)
                : m_partialDecodeType(result->partialDecodeType), m_barcodeScore(result->barcodeScore), m_symid(result->partialSymid)
            {
                std::memcpy(m_corners, result->corners, 4 * sizeof(Point));

                for (unsigned int i = 0; i < result->nbCodewords; i++)
                {
                    m_codewords.push_back(result->codewords[i]);
                    m_codewordScores.push_back(result->codewordScores[i]);
                }

                for (unsigned int i = 0; i < result->nbPossibleMessages; i++)
                {
                    std::string str(result->possibleMessages[i].data, result->possibleMessages[i].dataLength);
                    m_possibleMessages.push_back(PartialMessage(str, result->possibleMessages[i].format));
                }
            };

            std::string getSymbolName()
            {
                return getPartialSymidName(m_symid);
            };

            Point* getCorners()
            {
                return m_corners;
            };

            PartialDecodeType getDecodeFrom()
            {
                return m_partialDecodeType;
            };

            std::string getStringDecodeFrom()
            {
                switch (m_partialDecodeType)
                {
                case PartialDecodeType::FNX_DECODED_FROM_START:
                    return "start";
                case PartialDecodeType::FNX_DECODED_FROM_STOP:
                    return "stop";
                default:
                    return "full barcode";
                }
            };

            unsigned int getBarcodeScore()
            {
                return m_barcodeScore;
            }

            std::vector<unsigned int>& getCodewords()
            {
                return m_codewords;
            }

            std::vector<unsigned int>& getCodewordScores()
            {
                return m_codewordScores;
            }

            std::vector <PartialMessage>& getPartialMessages()
            {
                return m_possibleMessages;
            }
        };
    }
}



