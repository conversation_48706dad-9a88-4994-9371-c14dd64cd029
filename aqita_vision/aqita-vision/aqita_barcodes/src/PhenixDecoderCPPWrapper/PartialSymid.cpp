#include "PartialSymid.h"

using namespace viziotix;
std::string fnx::getPartialSymidName(fnxPartialSymid partialSymid)
{
    switch (partialSymid)
    {
    case fnxPartialSymid::FNX_ID_PARTIAL_UNKNOWN:
        return "Unknown symbology";
    case fnxPartialSymid::FNX_ID_PARTIAL_CODE39:
        return "Partial Code 39";
    case fnxPartialSymid::FNX_ID_PARTIAL_EAN13_UPCA:
        return "Partial EAN-13/UPC-A";
    case fnxPartialSymid::FNX_ID_PARTIAL_EAN8:
        return "Partial EAN-8";
    case fnxPartialSymid::FNX_ID_PARTIAL_CODE128:
        return "Partial Code 128/GS1-128";
    default:
        return "Unknown symbology";
    }
}
