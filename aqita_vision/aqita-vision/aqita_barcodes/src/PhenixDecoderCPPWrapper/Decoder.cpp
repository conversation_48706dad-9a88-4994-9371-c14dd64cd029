
#include "PhenixDecoderAPI.h"
#include "PhenixPartialDecodeAPI.h"
#include "Decoder.h"

using namespace viziotix::fnx;

Decoder::Decoder(DecoderListener* listener, PartialDecodeListener* listenerPartial)
{
    m_listener = listener;
    m_listenerPartial = listenerPartial;

    m_handle = fnxCreateDecoder();
    if (m_handle > 0)
    {
        auto result_callback_lambda = [](const fnxResult* result, void* caller)->int
        {
            ((Decoder *)caller)->Callback(result, caller);
            return 0;
        };

        auto partialResult_callback_lambda = [](const fnxPartialResult* result, void* caller)->int
        {
            ((Decoder*)caller)->CallbackPartial(result, caller);
            return 0;
        };

        fnxSetResultCallback(m_handle, result_callback_lambda, this);
        fnxSetPartialResultCallback(m_handle, partialResult_callback_lambda, this);
    }
}

Decoder::~Decoder()
{
    fnxDestroyDecoder(m_handle);
}

void Decoder::Callback(const fnxResult* result, void* caller)
{
    std::string str(result->data, result->length);

    std::string optDataString;
    const char* pOptData;
    int optDataLength;

    if (fnxGetOptionalData(0, &pOptData, &optDataLength) == Status::FNX_SUCCESS)
    {
        optDataString = std::string(pOptData, optDataLength);
    }
    else
    {
        optDataString = "{}";
    }

    Result decodeResult(str, result->symid, result->format, result->corners, optDataString);

    // call listener 
	if (m_listener != nullptr)
	{
        m_listener->OnResultReceived(decodeResult);
	}
}

void Decoder::CallbackPartial(const fnxPartialResult* result, void* caller)
{
    PartialResult decodeResult(result);

    // call listener
    if (m_listenerPartial != nullptr)
    {
        m_listenerPartial->OnResultReceived(decodeResult);
    }
}

Status Decoder::processImage(Image& image)
{
    return fnxProcessImage(m_handle, &image);
}

Status Decoder::stopProcessing()
{
    return fnxStopProcessing(m_handle);
}

Status Decoder::readIntSetting(SettingTag tag, int& value)
{
    return fnxReadIntSetting(m_handle, tag, &value);
}

Status Decoder::writeIntSetting(SettingTag tag, int value)
{
    return fnxWriteIntSetting(m_handle, tag, value);
}

Status Decoder::readBufferSetting(SettingTag tag, unsigned char** buffer, int& nBytesInBuffer)
{
    return fnxReadBufferSetting(m_handle, tag, buffer, &nBytesInBuffer);
}

Status Decoder::writeBufferSetting(SettingTag tag, unsigned char* buffer, int nBytesInBuffer)
{
    return fnxWriteBufferSetting(m_handle, tag, buffer, nBytesInBuffer);
}
