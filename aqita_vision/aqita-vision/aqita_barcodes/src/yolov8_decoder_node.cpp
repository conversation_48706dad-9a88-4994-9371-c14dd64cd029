
// TODO: License description

#include "aqita_barcodes/yolov8_decoder_node.hpp"
#include <cuda_runtime.h>
#include <algorithm>
#include <cmath>
#include <iostream>
#include <vector>
#include "isaac_ros_nitros_tensor_list_type/nitros_tensor_list_view.hpp"
#include "isaac_ros_nitros_tensor_list_type/nitros_tensor_list.hpp"
#include <opencv4/opencv2/opencv.hpp>
#include <opencv4/opencv2/dnn.hpp>
#include <opencv4/opencv2/dnn/dnn.hpp>
#include "vision_msgs/msg/detection2_d_array.hpp"
#include "tf2_geometry_msgs/tf2_geometry_msgs.hpp"

namespace aqita
{
namespace ros2
{
namespace inference
{

// constructor
YoloV8DecoderNode::YoloV8DecoderNode(const rclcpp::NodeOptions options) : rclcpp::Node("yolov8_decoder_node", options)
{
  RCLCPP_INFO(get_logger(), "Initializing YOLOv8 decoder node...");

  // declare the parameters
  declare_parameter<std::string>("input_tensor_name", "yolov8_output_tensor");
  declare_parameter<std::string>("tensor_rt_output_tensors_topic", "tensor_sub");
  declare_parameter<double>("confidence_threshold", 0.25);
  declare_parameter<double>("nms_threshold", 0.45);
  declare_parameter<double>("nms_eta", 5.0);
  declare_parameter<int>("num_classes", 1);
  declare_parameter<int>("num_anchors", 8400);
  declare_parameter<std::string>("output_detections_topic", "detections_output");
  declare_parameter<int>("output_detections_pub_qos", 50);

  // get the parameters
  input_tensor_name_ = get_parameter("input_tensor_name").as_string();
  tensor_rt_output_tensors_topic_ = get_parameter("tensor_rt_output_tensors_topic").as_string();
  confidence_threshold_ = get_parameter("confidence_threshold").as_double();
  nms_threshold_ = get_parameter("nms_threshold").as_double();
  nms_eta_ = get_parameter("nms_eta").as_double();
  num_classes_ = get_parameter("num_classes").as_int();
  num_anchors_ = get_parameter("num_anchors").as_int();
  output_detections_topic_ = get_parameter("output_detections_topic").as_string();

  RCLCPP_INFO(get_logger(), "Input tensor name set to: %s", input_tensor_name_.c_str());
  RCLCPP_INFO(get_logger(), "TensorRT output tensors topic set to: %s", tensor_rt_output_tensors_topic_.c_str());
  RCLCPP_INFO(get_logger(), "Confidence threshhold set to: %.2f", confidence_threshold_);
  RCLCPP_INFO(get_logger(), "NMS threshhold set to: %.2f", nms_threshold_);
  RCLCPP_INFO(get_logger(), "NMS eta set to: %.2f", nms_eta_);
  RCLCPP_INFO(get_logger(), "Number of classes set to: %d", num_classes_);
  RCLCPP_INFO(get_logger(), "Number of anchors set to: %d", num_anchors_);
  RCLCPP_INFO(get_logger(), "Output detections topic set to: %s", output_detections_topic_.c_str());

  // create the tensor messages subscriber
  nitros_sub_ = std::make_shared<nvidia::isaac_ros::nitros::ManagedNitrosSubscriber<
    nvidia::isaac_ros::nitros::NitrosTensorListView>>(
    this,
    tensor_rt_output_tensors_topic_,
    nvidia::isaac_ros::nitros::nitros_tensor_list_nchw_rgb_f32_t::supported_type_name,
    std::bind(&YoloV8DecoderNode::InputCallback, this,
    std::placeholders::_1));

  // create the detections publisher
  rclcpp::QoS detection_qos = rclcpp::QoS(rclcpp::QoSInitialization::from_rmw(rmw_qos_profile_sensor_data));
  detection_qos.reliable();
  detection_qos.keep_last(10);
  detection_qos.durability_volatile();
  detections_publisher_ = create_publisher<vision_msgs::msg::Detection2DArray>(output_detections_topic_, detection_qos);
}

// destructor
YoloV8DecoderNode::~YoloV8DecoderNode() = default;

// callback function to process new tensor messages
void YoloV8DecoderNode::InputCallback(const nvidia::isaac_ros::nitros::NitrosTensorListView & msg)
{
  // get the tensor with the inference results
  auto tensor = msg.GetNamedTensor(input_tensor_name_);
  size_t buffer_size{tensor.GetTensorSize()};

  // create the vector with the inference results on the CPU
  std::vector<float> results_vector{};
  results_vector.resize(buffer_size);

  // copy the inference results from the GPU to the CPU
  cudaMemcpy(results_vector.data(), tensor.GetBuffer(), buffer_size, cudaMemcpyDefault);

  // process all possible detections (anchors) to create the bounding boxes
  std::vector<cv::Rect> bboxes;
  std::vector<float> scores;
  std::vector<int> indices;
  std::vector<int> classes;

  float * results_data = reinterpret_cast<float *>(results_vector.data());

  for (int i = 0; i < num_anchors_; i++)
  {
    // get the detection center point coordinates
    float x = *(results_data + i);
    float y = *(results_data + (num_anchors_ * 1) + i);

    // get the width and height of the bounding box
    float w = *(results_data + (num_anchors_ * 2) + i);
    float h = *(results_data + (num_anchors_ * 3) + i);
    float width = w;
    float height = h;

    // calculate the bounding box top left corner coordinates
    float x1 = (x - (0.5 * w));
    float y1 = (y - (0.5 * h));

    // get the confidence values in a vector
    std::vector<float> conf;
    for (int j = 0; j < num_classes_; j++)
    {
      conf.push_back(*(results_data + (num_anchors_ * (4 + j)) + i));
    }

    // find the class index with the maximum confidence
    std::vector<float>::iterator ind_max_conf;
    ind_max_conf = std::max_element(std::begin(conf), std::end(conf));
    int max_index = distance(std::begin(conf), ind_max_conf);
    float val_max_conf = *max_element(std::begin(conf), std::end(conf));

    // fill in the results
    bboxes.push_back(cv::Rect(x1, y1, width, height));
    indices.push_back(i);
    scores.push_back(val_max_conf);
    classes.push_back(max_index);
  }

  // perform NMS
  RCLCPP_DEBUG(get_logger(), "Count of bboxes: %lu", bboxes.size());
  cv::dnn::NMSBoxes(bboxes, scores, confidence_threshold_, nms_threshold_, indices, nms_eta_);
  RCLCPP_DEBUG(get_logger(), "# boxes after NMS: %lu", indices.size());

  // construct the final detections array
  vision_msgs::msg::Detection2DArray final_detections_arr;

  for (size_t i = 0; i < indices.size(); i++)
  {
    int ind = indices[i];
    vision_msgs::msg::Detection2D detection;

    float w = bboxes[ind].width;
    float h = bboxes[ind].height;
    float x_center = bboxes[ind].x + (0.5 * w);
    float y_center = bboxes[ind].y + (0.5 * h);
    detection.bbox.center.position.x = x_center;
    detection.bbox.center.position.y = y_center;
    detection.bbox.size_x = w;
    detection.bbox.size_y = h;

    // write the class probabilities
    vision_msgs::msg::ObjectHypothesisWithPose hyp;
    hyp.hypothesis.class_id = std::to_string(classes.at(ind));
    hyp.hypothesis.score = scores.at(ind);
    detection.results.push_back(hyp);

    // add a timestamp
    detection.header.stamp.sec = msg.GetTimestampSeconds();
    detection.header.stamp.nanosec = msg.GetTimestampNanoseconds();

    // write the detection in the detections array
    final_detections_arr.detections.push_back(detection);
  }

  // add a timestamp and publish the detections array
  final_detections_arr.header.stamp.sec = msg.GetTimestampSeconds();
  final_detections_arr.header.stamp.nanosec = msg.GetTimestampNanoseconds();
  detections_publisher_->publish(final_detections_arr);
}

}  // namespace inference
}  // namespace ros2
}  // namespace aqita

// Register as component
#include "rclcpp_components/register_node_macro.hpp"
RCLCPP_COMPONENTS_REGISTER_NODE(aqita::ros2::inference::YoloV8DecoderNode)
