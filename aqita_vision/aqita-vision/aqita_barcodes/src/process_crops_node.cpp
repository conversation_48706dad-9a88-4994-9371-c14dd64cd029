#include <rclcpp/rclcpp.hpp>
#include <rclcpp/duration.hpp>
#include <image_transport/image_transport.hpp>
#include <message_filters/subscriber.h>
#include <message_filters/sync_policies/approximate_time.h>
#include <message_filters/synchronizer.h>
#include <vision_msgs/msg/detection2_d_array.hpp>
#include <sensor_msgs/msg/image.hpp>
#include <cv_bridge/cv_bridge.h>
#include <opencv2/opencv.hpp>
#include <std_msgs/msg/header.hpp>
#include "PhenixDecoderCPPWrapper/DecoderManager.h"
#include "PhenixDecoderCPPWrapper/DecoderListener.h"
#include "PhenixDecoderCPPWrapper/StatusMessage.h"
#include <filesystem>
#include <sstream>
#include <numeric>
#include <fstream>
#include <ctime>
#include <iomanip>
#include <onnxruntime/core/session/onnxruntime_cxx_api.h>
#define STB_IMAGE_IMPLEMENTATION
#include "PhenixDecoderCPPWrapper/stb_image.h"
#include "PhenixDecoderCPPWrapper/json.hpp"

#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "nav2_aqita_msgs/action/inference.hpp"
#include <std_msgs/msg/int32.hpp>

using Inference = nav2_aqita_msgs::action::Inference;
using GoalHandleDoWork = rclcpp_action::ServerGoalHandle<Inference>;
using namespace viziotix::fnx;


namespace aqita
{
namespace ros2
{
namespace inference
{

class MyListener : public DecoderListener
{
public:
    std::vector<Result> resultList;
    std::ofstream logFile;
    std::string filename;

    explicit MyListener(const std::string& file = "viziotix_decodes.txt") 
        : filename(file) 
    {
        // Open in append mode, create if doesn't exist
        logFile.open(filename, std::ios::app);
        if (!logFile.is_open()) {
            std::cerr << "ERROR: Could not open log file: " << filename << std::endl;
            throw std::runtime_error("Failed to open log file");
        }
        logFile << "\n\n==== New Decoding Session ====\n";
        logFile.flush();
    }

    ~MyListener() {
        if (logFile.is_open()) {
            logFile << "==== Session Ended ====\n\n";
            logFile.close();
        }
    }

    void OnResultReceived(Result result) override
    {
        try {
            // Verify file is still good
            if (!logFile.good()) {
                std::cerr << "WARNING: Log file bad state, reopening..." << std::endl;
                logFile.close();
                logFile.open(filename, std::ios::app);
                if (!logFile.is_open()) return;
            }

            resultList.push_back(result);
            
            // Get timestamp
            auto now = std::chrono::system_clock::now();
            auto in_time = std::chrono::system_clock::to_time_t(now);
            
            // Write to file
            logFile << "[" << std::put_time(std::localtime(&in_time), "%Y-%m-%d %H:%M:%S") << "] "
                    << "Symbology: " << result.getSymbolName() << " | "
                    << "Data: " << result.getString() << " | "
                    << "Format: " << result.getStringFormat() << "\n";
            
            logFile.flush();  // Immediate write
            
            // Debug output to console
            std::cout << "DEBUG: Decoded - " << result.getString() << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "ERROR in OnResultReceived: " << e.what() << std::endl;
        }
    }

    void ClearResults() {
        resultList.clear();
    }
};

class ProcessCrops : public rclcpp::Node
{
public:
    explicit ProcessCrops(const rclcpp::NodeOptions &options) : Node("process_crops_node", options)
    {
        RCLCPP_INFO(get_logger(), "Initializing ProcessCrops node...");

        declare_parameter<std::string>("image_topic", "/camera/image/image_rect_raw");
        declare_parameter<std::string>("detections_topic", "detections");
        declare_parameter<std::string>("output_boxes_topic", "super_res_crops");
        declare_parameter<int>("sync_queue_size", 10);
        declare_parameter<double>("sync_slop", 0.05);
        declare_parameter<double>("min_conf", 0.5);
        declare_parameter<int>("input_img_height", 640);
        declare_parameter<int>("input_img_width", 640);
        declare_parameter<std::string>("super_res_model_path", "/workspace/src/aqita-ros/aqita_vision/aqita_assets/models/super_resolution/gp_loss_ffl_tsc_0_7_tsc_weights_23_21.onnx");
        declare_parameter<bool>("use_gpu", true);

        image_topic_ = get_parameter("image_topic").as_string();
        detections_topic_ = get_parameter("detections_topic").as_string();
        output_boxes_topic_ = get_parameter("output_boxes_topic").as_string();
        sync_queue_size_ = get_parameter("sync_queue_size").as_int();
        sync_slop_ = get_parameter("sync_slop").as_double();
        min_conf_ = get_parameter("min_conf").as_double();
        input_img_height_ = get_parameter("input_img_height").as_int();
        input_img_width_ = get_parameter("input_img_width").as_int();
        super_res_model_path_ = get_parameter("super_res_model_path").as_string();
        use_gpu_ = get_parameter("use_gpu").as_bool();

        image_sub_ = std::make_shared<message_filters::Subscriber<sensor_msgs::msg::Image>>(this, image_topic_);
        detections_sub_ = std::make_shared<message_filters::Subscriber<vision_msgs::msg::Detection2DArray>>(this, detections_topic_);

        time_sync_ = std::make_shared<message_filters::Synchronizer<SyncPolicy>>(SyncPolicy(sync_queue_size_), *image_sub_, *detections_sub_);
        time_sync_->setMaxIntervalDuration(rclcpp::Duration::from_seconds(sync_slop_));
        time_sync_->registerCallback(std::bind(&ProcessCrops::detections_callback, this, std::placeholders::_1, std::placeholders::_2));

        crops_pub_ = this->create_publisher<sensor_msgs::msg::Image>(output_boxes_topic_, 10);
        test_crop_pub_ = this->create_publisher<sensor_msgs::msg::Image>("/test_crop_topic", 10);
        
        // Initialize ONNX Runtime
        initialize_onnx_runtime();

        /** Action server */
         action_server_ = rclcpp_action::create_server<Inference>(
            this,
            "inference",
            std::bind(&ProcessCrops::handle_goal, this, std::placeholders::_1, std::placeholders::_2),
            std::bind(&ProcessCrops::handle_cancel, this, std::placeholders::_1),
            std::bind(&ProcessCrops::handle_accepted, this, std::placeholders::_1)
            );

        /** Publish detected barcodes count to monitoring system in MC */
        barcode_pub_ = this->create_publisher<std_msgs::msg::Int32>("/barcodes", 10);
    }

    ~ProcessCrops() {}

private:
    using SyncPolicy = message_filters::sync_policies::ApproximateTime<sensor_msgs::msg::Image, vision_msgs::msg::Detection2DArray>;

    bool _detecting{true};
    int _barcodes{0};

    void initialize_onnx_runtime()
    {
        try {
            // Initialize environment
            ort_env_ = std::make_unique<Ort::Env>(ORT_LOGGING_LEVEL_WARNING, "super_resolution");
            
            // Session options
            Ort::SessionOptions session_options;
            session_options.SetIntraOpNumThreads(1);
            session_options.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_ALL);

            // Configure GPU if available and requested
            auto available_providers = Ort::GetAvailableProviders();
            bool gpu_available = std::find(available_providers.begin(), available_providers.end(), "CUDAExecutionProvider") != available_providers.end();
            
            if (use_gpu_ && gpu_available) {
                RCLCPP_INFO(get_logger(), "Initializing ONNX Runtime with GPU support");
                OrtCUDAProviderOptions cuda_options;
                cuda_options.device_id = 0;
                session_options.AppendExecutionProvider_CUDA(cuda_options);
            } else {
                RCLCPP_INFO(get_logger(), "Initializing ONNX Runtime with CPU only");
            }

            // Create session
            ort_session_ = std::make_unique<Ort::Session>(*ort_env_, super_res_model_path_.c_str(), session_options);

            // Get input/output info - UPDATED API
            Ort::AllocatorWithDefaultOptions allocator;
            
            // Get input name
            input_name_ = ort_session_->GetInputNameAllocated(0, allocator).get();
            
            // Get output name
            output_name_ = ort_session_->GetOutputNameAllocated(0, allocator).get();
            
            RCLCPP_INFO(get_logger(), "ONNX Runtime initialized successfully with model: %s", super_res_model_path_.c_str());
            RCLCPP_INFO(get_logger(), "Input name: %s, Output name: %s", input_name_.c_str(), output_name_.c_str());
        } catch (const Ort::Exception& e) {
            RCLCPP_ERROR(get_logger(), "ONNX Runtime initialization failed: %s", e.what());
            throw std::runtime_error("Failed to initialize ONNX Runtime");
        }
    }

    cv::Mat apply_super_resolution(const cv::Mat& input)
    {
        try {
            // Load image and convert to float [0,1] range
            cv::Mat img_float;
            input.convertTo(img_float, CV_32F, 1.0 / 255.0);

            // Convert BGR to RGB
            cv::Mat img_rgb;
            cv::cvtColor(img_float, img_rgb, cv::COLOR_BGR2RGB);

            // Convert image to tensor-like format with channels first
            std::vector<int64_t> input_shape = {1, 3, input.rows, input.cols};
            cv::Mat img_channels[3];
            cv::split(img_rgb, img_channels);

            // Flatten channels into contiguous memory
            std::vector<float> input_data;
            for (int c = 0; c < 3; ++c) {
                input_data.insert(input_data.end(), img_channels[c].begin<float>(), img_channels[c].end<float>());
            }

            // Create ONNX input tensor
            Ort::MemoryInfo memory_info = Ort::MemoryInfo::CreateCpu(OrtAllocatorType::OrtArenaAllocator, OrtMemType::OrtMemTypeDefault);
            Ort::Value input_tensor = Ort::Value::CreateTensor<float>(
                memory_info,
                input_data.data(),
                input_data.size(),
                input_shape.data(),
                input_shape.size()
            );

            // Run inference
            const char* input_names[] = {input_name_.c_str()};
            const char* output_names[] = {output_name_.c_str()};
            auto output_tensors = ort_session_->Run(
                Ort::RunOptions{nullptr},
                input_names,
                &input_tensor,
                1,
                output_names,
                1
            );

            // Extract output tensor data
            float* output_data = output_tensors[0].GetTensorMutableData<float>();
            auto output_shape = output_tensors[0].GetTensorTypeAndShapeInfo().GetShape();

            // Convert output tensor to image (assuming format is 1x3xHxW)
            cv::Mat output_channels[3];
            int total_pixels = output_shape[2] * output_shape[3];

            for (int c = 0; c < 3; ++c) {
                output_channels[c] = cv::Mat(output_shape[2], output_shape[3], CV_32F, output_data + c * total_pixels);
            }

            // Merge channels into an image
            cv::Mat output_image;
            cv::merge(output_channels, 3, output_image);

            // Convert RGB to BGR
            cv::cvtColor(output_image, output_image, cv::COLOR_RGB2BGR);

            // Clamp and convert to 8-bit
            output_image = cv::min(cv::max(output_image, 0.0f), 1.0f);
            output_image.convertTo(output_image, CV_8U, 255.0);

            return output_image;

        } catch (const Ort::Exception& e) {
            RCLCPP_ERROR(get_logger(), "ONNX Runtime inference failed: %s", e.what());
            return input;
        } catch (const cv::Exception& e) {
            RCLCPP_ERROR(get_logger(), "OpenCV processing failed: %s", e.what());
            return input;
        }
    }


    cv::Mat rotate(const cv::Mat& image)
    {
        cv::Mat gray, adjusted, blackhat, gradX, blurred, thresh, edges;
        cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);
        cv::convertScaleAbs(gray, adjusted, 2.0, 50);  // alpha=2.0, beta=50

        cv::Mat kernel = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(21, 7));
        cv::morphologyEx(adjusted, blackhat, cv::MORPH_BLACKHAT, kernel);

        cv::Sobel(blackhat, gradX, CV_32F, 1, 0);
        cv::convertScaleAbs(gradX, gradX);
        cv::GaussianBlur(gradX, blurred, cv::Size(9, 9), 0);
        cv::threshold(blurred, thresh, 0, 255, cv::THRESH_BINARY | cv::THRESH_OTSU);
        cv::Canny(thresh, edges, 50, 150, 3);

        std::vector<cv::Vec4i> lines;
        cv::HoughLinesP(edges, lines, 1, CV_PI / 180, 50, 50, 5);

        if (!lines.empty())
        {
            std::vector<double> angles;
            for (const auto& line : lines)
            {
                double angle = std::atan2(line[3] - line[1], line[2] - line[0]) * 180.0 / CV_PI;
                angles.push_back(angle);
            }

            double median_angle;
            std::sort(angles.begin(), angles.end());
            if (angles.size() % 2 == 0)
                median_angle = (angles[angles.size() / 2 - 1] + angles[angles.size() / 2]) / 2.0;
            else
                median_angle = angles[angles.size() / 2];

            int h = image.rows, w = image.cols;
            int size = std::max(h, w);
            cv::Mat padded;
            cv::copyMakeBorder(image, padded,
                            (size - h) / 2, (size - h + 1) / 2,
                            (size - w) / 2, (size - w + 1) / 2,
                            cv::BORDER_CONSTANT, cv::Scalar(255, 255, 255));

            if (std::abs(median_angle) != 90.0)
            {
                cv::Point2f center(size / 2.0f, size / 2.0f);
                cv::Mat M = cv::getRotationMatrix2D(center, median_angle, 1.0);

                double cos = std::abs(M.at<double>(0, 0));
                double sin = std::abs(M.at<double>(0, 1));
                int new_w = int((h * sin) + (w * cos));
                int new_h = int((h * cos) + (w * sin));

                M.at<double>(0, 2) += (new_w / 2.0) - center.x;
                M.at<double>(1, 2) += (new_h / 2.0) - center.y;

                cv::Mat rotated;
                cv::warpAffine(padded, rotated, M, cv::Size(new_w, new_h), cv::INTER_LINEAR, cv::BORDER_CONSTANT, cv::Scalar(255, 255, 255));

                cv::Mat rotated90;
                cv::rotate(rotated, rotated90, cv::ROTATE_90_CLOCKWISE);
                return rotated90;
            }
            return padded;
        }
        return image;
    }
    
    fnxImage convert_cvmat_to_fnx_image(const cv::Mat& cvImage) {
        fnxImage image = {0};
        
        image.data = cvImage.data;
        RCLCPP_INFO(get_logger(), "Converted Image");
        image.width = cvImage.cols;
        image.height = cvImage.rows;
        image.stride = cvImage.step; // For most cases, step == cols * channels
        
        // Set the format based on OpenCV image type
        if (cvImage.type() == CV_8UC1) { // Grayscale (Y800)
            image.format = FNX_Y800;
        } 
        else if (cvImage.type() == CV_8UC3) { 
            // Check if it's BGR or RGB (OpenCV usually uses BGR by default)
            image.format = FNX_BGR; // Assuming BGR ordering (common in OpenCV)
        }
        else if (cvImage.type() == CV_8UC4) {
            // Check if it's BGRA or RGBA
            image.format = FNX_BGRA; // Assuming BGRA ordering
        }
        else {
            RCLCPP_ERROR(get_logger(), "Unsupported OpenCV image format");
            // Handle error or throw an exception
        }
        
        return image;
    }

    void detections_callback(const sensor_msgs::msg::Image::ConstSharedPtr &image_msg,
                             const vision_msgs::msg::Detection2DArray::ConstSharedPtr &detections_msg)
    {
        if(_detecting){
            // // Initialize a Viziotix decoder manager, initialize a session and activate the license for the session
            // std::string key = "3A4F17-BFD5F4-4EDAAD-B078DB-BE2001-04CEBC";
            // DecoderManager* manager = DecoderManager::get();
            // RCLCPP_INFO(get_logger(), "Phenix Decoder Versions=%s", manager->getLibraryVersion().c_str());
        
            // Status status_init = manager->initializeLibrary();
            // RCLCPP_INFO(get_logger(), "Initialized Viziotix Session");

            // Status status = manager->activateLicense(key);
            // int viziotix_activation_status = static_cast<int>(status);
            // RCLCPP_INFO(get_logger(), "Status code: %d", viziotix_activation_status);
            // RCLCPP_INFO(get_logger(), "Viziotix license validated successfuly");
            
            // // Initialize a Viziotix Decoder Listener
            // MyListener viziotix_decoder_listener;
            // RCLCPP_INFO(get_logger(), "Created a Viziotix Decoder Listener");

            // // Initialize a Viziotix Decoder
            // Decoder* viziotix_decoder = manager->createDecoder(&viziotix_decoder_listener);
            // RCLCPP_INFO(get_logger(), "Created a Viziotix Decoder");

            // // if (status)
            // // {
            // //     RCLCPP_INFO(get_logger(), "Warning (initializeLibrary)");
            // //     Status status = manager->activateLicense(key);
            // //     RCLCPP_INFO(get_logger(), "Status code: %d", static_cast<int>(status));
            // // }

            // viziotix_decoder->writeIntSetting(SettingTag::FNX_CODE39_ENABLE,                  1);
            // viziotix_decoder->writeIntSetting(SettingTag::FNX_CODE39_NANO_SCAN,               1);
            // viziotix_decoder->writeIntSetting(SettingTag::FNX_EAN13_ENABLE,                   1);
            // viziotix_decoder->writeIntSetting(SettingTag::FNX_EAN8_ENABLE,                    1);
            // viziotix_decoder->writeIntSetting(SettingTag::FNX_UPCA_ENABLE,                    1);
            // viziotix_decoder->writeIntSetting(SettingTag::FNX_UPCE_ENABLE,                    1);
            // viziotix_decoder->writeIntSetting(SettingTag::FNX_EAN_UPC_NANO_SCAN,              1);
            // viziotix_decoder->writeIntSetting(SettingTag::FNX_CODE128_ENABLE,                 1);
            // viziotix_decoder->writeIntSetting(SettingTag::FNX_GS1_128_ENABLE,                 1);
            // viziotix_decoder->writeIntSetting(SettingTag::FNX_CODE128_NANO_SCAN,              1);
            // viziotix_decoder->writeIntSetting(SettingTag::FNX_DATAMATRIX_ENABLE,              1);
            // viziotix_decoder->writeIntSetting(SettingTag::FNX_GS1_DATAMATRIX_ENABLE,          1);
            // viziotix_decoder->writeIntSetting(SettingTag::FNX_QR_CODE_ENABLE,                 1);
            // viziotix_decoder->writeIntSetting(SettingTag::FNX_OPERATING_N_MULTI_SCALE_ENABLE, 1);
            // viziotix_decoder->writeIntSetting(SettingTag::FNX_PROCESSING_1D_HIGH_DENSITY,     1);
            // viziotix_decoder->writeIntSetting(SettingTag::FNX_PROCESSING_1D_PERSPECTIVE,      1);

            cv_bridge::CvImagePtr cv_ptr;
            try
            {
                cv_ptr = cv_bridge::toCvCopy(image_msg);
            }
            catch (const cv_bridge::Exception &e)
            {
                RCLCPP_ERROR(get_logger(), "CV Bridge error: %s", e.what());
                return;
            }

            std::string crops_dir = "/workspace/src/aqita-ros/aqita_vision/crops";
            if (!std::filesystem::exists(crops_dir)) {
                std::filesystem::create_directory(crops_dir);
            }

            int num_detections = detections_msg->detections.size();

            //Iterate over all detections - all barcodes
            for (const auto &detection : detections_msg->detections)
            {
                // Get the crop from the image based on the detections
                if (detection.results[0].hypothesis.score < min_conf_)
                {
                    num_detections = std::max(num_detections - 1, 0);
                    continue;
                }

                double center_x = image_msg->width * detection.bbox.center.position.x / input_img_width_;
                double center_y = image_msg->height * detection.bbox.center.position.y / input_img_height_;
                double width = image_msg->width * detection.bbox.size_x / input_img_width_;
                double height = image_msg->height * detection.bbox.size_y / input_img_height_;

                int x = std::max(0, static_cast<int>(center_x - width * 0.5));
                int y = std::max(0, static_cast<int>(center_y - height * 0.5));
                int w = std::min(static_cast<int>(width), static_cast<int>(image_msg->width - x));
                int h = std::min(static_cast<int>(height), static_cast<int>(image_msg->height - y));

                cv::Rect roi(x, y, w, h);
                if (roi.area() <= 0) continue;

                cv::Mat crop = cv_ptr->image(roi);
                //RCLCPP_INFO(get_logger(), "Processing crop at (x=%d, y=%d, w=%d, h=%d)", x, y, w, h);
                
                // Apply super-resolution and image enhancement
                cv::Mat upsampled = apply_super_resolution(crop);

                // Apply rotation logic
                cv::Mat rotated = rotate(upsampled);
                
                // // Convert the cv::Mat image to fnxImage - this is the format that is expected by
                // // the Viziotix Decoder
                // fnxImage fnx_image = convert_cvmat_to_fnx_image(rotated);

                // // Process the image with the Viziotix Decoder
                // Status viziotix_decode_status = viziotix_decoder->processImage(fnx_image);
                // RCLCPP_INFO(get_logger(), "Viziotix Decoder Status: %d", static_cast<int>(viziotix_decode_status));

                // std::ostringstream filename_base;
                // filename_base << crops_dir << "/crop_" << image_msg->header.stamp.sec << "_" << image_msg->header.stamp.nanosec << "_" << x << "_" << y;
                // std::string before_path = filename_base.str() + "_before.png";
                // std::string after_path = filename_base.str() + "_after.png";
                // RCLCPP_INFO(get_logger(), "For Image %s", after_path.c_str());
                // try {
                //     cv::imwrite(before_path, crop);
                //     cv::imwrite(after_path, rotated);
                // } catch (const cv::Exception &e) {
                //     RCLCPP_ERROR(get_logger(), "Error saving images: %s", e.what());
                // }

                cv_bridge::CvImage crop_msg;
                crop_msg.header = image_msg->header;
                crop_msg.encoding = sensor_msgs::image_encodings::BGR8;
                crop_msg.image = rotated;

                test_crop_pub_->publish(*crop_msg.toImageMsg());
                crops_pub_->publish(*crop_msg.toImageMsg());
            }

            RCLCPP_INFO(get_logger(), "Detections processed: %d barcodes ", num_detections);
            _barcodes = num_detections;
            publish_barcodes();
            // manager->deinitializeLibrary();
            // RCLCPP_INFO(get_logger(), "Deinitialized Viziotix Library");
        }
    }

    
    void publish_barcodes(){
        auto msg = std_msgs::msg::Int32();
        msg.data = _barcodes;
        barcode_pub_->publish(msg);
    }
    rclcpp_action::GoalResponse handle_goal(
    const rclcpp_action::GoalUUID &,
    std::shared_ptr<const Inference::Goal> goal)
  {
    /** Those are required params for complet the scan Waypoint */
    _slot_id = static_cast<int32_t>(goal->slot_id);
    _timestamp = goal->timestamp;

    RCLCPP_INFO(this->get_logger(), "Slot ID: %d", _slot_id);
    return rclcpp_action::GoalResponse::ACCEPT_AND_EXECUTE;
  }

  rclcpp_action::CancelResponse handle_cancel(const std::shared_ptr<GoalHandleDoWork> goal_handle) {
    if(goal_handle->is_canceling()){
      RCLCPP_DEBUG(this->get_logger(), "Cancel request received.!!!");
    }
    RCLCPP_INFO(this->get_logger(), "Cancel request received.");
    return rclcpp_action::CancelResponse::ACCEPT;
  }

  void handle_accepted(const std::shared_ptr<GoalHandleDoWork> goal_handle) {
    std::thread{std::bind(&ProcessCrops::execute, this, goal_handle)}.detach();
  }

  void execute(const std::shared_ptr<GoalHandleDoWork> goal_handle) {
    
    /** copy the goal for using in other functions */
    _goal_handle = goal_handle;

    _detecting = true;
    
    /** Just waiting time, before to return status */
    rclcpp::sleep_for(std::chrono::milliseconds(3000));
    
    _detecting = false;

    /** Return status of the request */

    auto result = std::make_shared<Inference::Result>();
    result->success = true;
    result->result_message = "Task completed successfully";
    goal_handle->succeed(result);

    RCLCPP_DEBUG(this->get_logger(), "Scanning completed for slot with id %d!",_slot_id);
  }
    
    
    std::shared_ptr<message_filters::Subscriber<sensor_msgs::msg::Image>> image_sub_;
    std::shared_ptr<message_filters::Subscriber<vision_msgs::msg::Detection2DArray>> detections_sub_;
    std::shared_ptr<message_filters::Synchronizer<SyncPolicy>> time_sync_;
    rclcpp::Publisher<sensor_msgs::msg::Image>::SharedPtr crops_pub_;
    rclcpp::Publisher<sensor_msgs::msg::Image>::SharedPtr test_crop_pub_;
    rclcpp::Publisher<std_msgs::msg::Int32>::SharedPtr barcode_pub_;

    std::string image_topic_;
    std::string detections_topic_;
    std::string output_boxes_topic_;
    int sync_queue_size_;
    double sync_slop_;
    double min_conf_;
    int input_img_height_;
    int input_img_width_;

    std::string super_res_model_path_;
    bool use_gpu_;

    // ONNX Runtime members
    std::unique_ptr<Ort::Env> ort_env_;
    std::unique_ptr<Ort::Session> ort_session_;
    std::string input_name_;
    std::string output_name_;

    //Action server
    rclcpp_action::Server<Inference>::SharedPtr action_server_;
    std::shared_ptr<GoalHandleDoWork> _goal_handle;
    std::string _task_name="NO NAME";
    int32_t _slot_id{0};
    rclcpp::Time _timestamp;
};

} // namespace inference
} // namespace ros2
} // namespace aqita

#include "rclcpp_components/register_node_macro.hpp"
RCLCPP_COMPONENTS_REGISTER_NODE(aqita::ros2::inference::ProcessCrops)
