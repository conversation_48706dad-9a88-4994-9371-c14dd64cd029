
# Place this file within /etc/systemd/system

[Unit]
Description=Service to start the Mission Scheduler component
After=network-online.target docker.service

[Service]
User=1000
KillMode=process
TimeoutStopSec=60
Environment=DISPLAY=:0
Environment=MISSION_ID={{mission_id}}
Environment=DRONE_ID={{drone_id}}
EnvironmentFile=/etc/aqita.env
ExecStart=/usr/bin/aqita_scheduler.sh -n mission_scheduler_container
ExecStop=/usr/bin/docker stop mission_scheduler_container
Restart=on-failure
RestartSec=5s

[Install]
WantedBy=default.target