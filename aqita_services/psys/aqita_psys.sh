#!/bin/bash

set -e

# Print functions
function print_error() {
    echo -e "\033[1;31mERROR: $1\033[0m"
}

function print_warning() {
    echo -e "\033[1;33mWARNING: $1\033[0m"
}

function print_info() {
    echo -e "\033[1;32mINFO: $1\033[0m"
}

function usage() {
    print_info "Usage: aqita_psys.sh [OPTIONS]"
    print_info "Options:"
    print_info "  -n, --name NAME       Container name (default: psys_container)"
    print_info "  -i, --image IMAGE     Docker image to use (auto-detected)"
    print_info "  -w, --workspace DIR   Workspace directory to mount (default: current directory)"
    print_info "  -p, --pull            Pull the latest image before running"
    print_info "  -a, --docker-arg ARG  Additional docker run argument"
    print_info "  -h, --help            Show this help message"
}

# Defaults
CONTAINER_NAME="psys_container"
WORKSPACE_DIR="${AQITA_ROS_WS}"
PULL_IMAGE=0
DOCKER_ARGS=()

# Detect architecture
PLATFORM="$(uname -m)"
if [[ $PLATFORM == "x86_64" ]]; then
    IMAGE_NAME="aqita-x86:latest"
    GITHUB_IMAGE="ghcr.io/uvionix/aqita-x86:latest"
elif [[ $PLATFORM == "aarch64" ]]; then
    IMAGE_NAME="aqita-arm:latest"
    GITHUB_IMAGE="ghcr.io/uvionix/aqita-arm:latest"
else
    IMAGE_NAME="aqita-arm:latest"
    GITHUB_IMAGE="ghcr.io/uvionix/aqita-arm:latest"
fi

# Parse arguments
VALID_ARGS=$(getopt -o n:i:w:pa:h --long name:,image:,workspace:,pull,docker-arg:,help -- "$@")
eval set -- "$VALID_ARGS"
while true; do
    case "$1" in
    -n | --name)
        CONTAINER_NAME="$2"
        shift 2
        ;;
    -i | --image)
        IMAGE_NAME="$2"
        shift 2
        ;;
    -w | --workspace)
        WORKSPACE_DIR="$2"
        shift 2
        ;;
    -p | --pull)
        PULL_IMAGE=1
        shift
        ;;
    -a | --docker-arg)
        DOCKER_ARGS+=("$2")
        shift 2
        ;;
    -h | --help)
        usage
        exit 0
        ;;
    --)
        shift
        break
        ;;
    esac
done

# Sanity checks
if [[ $(id -u) -eq 0 ]]; then
    print_error "Do not run this script as root."
    exit 1
fi

if ! id -nG "$USER" | grep -qw docker; then
    print_error "User '$USER' is not in the 'docker' group."
    print_error "Run: sudo usermod -aG docker $USER"
    print_error "Then log out and log back in."
    exit 1
fi

if ! docker ps &>/dev/null; then
    print_error "Cannot communicate with Docker daemon."
    exit 1
fi

if [[ ! -d "$WORKSPACE_DIR" ]]; then
    print_error "Workspace directory does not exist: $WORKSPACE_DIR"
    exit 1
fi

WORKSPACE_DIR=$(realpath "$WORKSPACE_DIR")

# Pull image if needed
if [[ $PULL_IMAGE -eq 1 ]]; then
    docker pull $GITHUB_IMAGE
    docker tag $GITHUB_IMAGE $IMAGE_NAME
fi

if [[ -z $(docker image ls --quiet $IMAGE_NAME) ]]; then
    docker pull $GITHUB_IMAGE
    docker tag $GITHUB_IMAGE $IMAGE_NAME
fi

# Clean up existing container
if docker ps -a --format '{{.Names}}' | grep -w "$CONTAINER_NAME" &>/dev/null; then
    docker stop "$CONTAINER_NAME" >/dev/null || true
    docker rm "$CONTAINER_NAME" >/dev/null || true
fi

# Enable X11
xhost +local:docker

# Docker args
DOCKER_ARGS+=("-v $WORKSPACE_DIR:/workspace")
DOCKER_ARGS+=("-v /tmp/.X11-unix:/tmp/.X11-unix")
DOCKER_ARGS+=("-v $HOME/.Xauthority:/root/.Xauthority:rw")
DOCKER_ARGS+=("-e DISPLAY")
DOCKER_ARGS+=("-e WORKSPACE_DIR=/workspace")
DOCKER_ARGS+=("-e RMW_IMPLEMENTATION=rmw_cyclonedds_cpp")

if [[ -n $SSH_AUTH_SOCK ]]; then
    DOCKER_ARGS+=("-v $SSH_AUTH_SOCK:/ssh-agent")
    DOCKER_ARGS+=("-e SSH_AUTH_SOCK=/ssh-agent")
fi

if [[ $PLATFORM == "aarch64" ]]; then
    DOCKER_ARGS+=("-e NVIDIA_VISIBLE_DEVICES=all")
    DOCKER_ARGS+=("-e NVIDIA_DRIVER_CAPABILITIES=all")
    DOCKER_ARGS+=("-v /tmp/:/tmp/")
    DOCKER_ARGS+=("--pid=host")
    DOCKER_ARGS+=("-v /dev/input:/dev/input")

    if [ -d "/usr/lib/aarch64-linux-gnu/tegra" ]; then
        DOCKER_ARGS+=("-v /usr/lib/aarch64-linux-gnu/tegra:/usr/lib/aarch64-linux-gnu/tegra")
    fi
    if [ -d "/usr/src/jetson_multimedia_api" ]; then
        DOCKER_ARGS+=("-v /usr/src/jetson_multimedia_api:/usr/src/jetson_multimedia_api")
    fi
    if [ -d "/usr/share/vpi3" ]; then
        DOCKER_ARGS+=("-v /usr/share/vpi3:/usr/share/vpi3")
    fi
elif [[ $PLATFORM == "x86_64" ]] && command -v nvidia-smi &>/dev/null; then
    DOCKER_ARGS+=("-e NVIDIA_VISIBLE_DEVICES=all")
    DOCKER_ARGS+=("-e NVIDIA_DRIVER_CAPABILITIES=all")
fi

# Create minimal entrypoint script
ENTRYPOINT_SCRIPT=$(mktemp)
cat >"$ENTRYPOINT_SCRIPT" <<'EOF'
#!/bin/bash
set -e

# Restart udev
service udev restart || true

# Increase network buffers
sysctl -w net.core.rmem_max=2147483647
sysctl -w net.core.rmem_default=2147483647
sysctl -w net.core.wmem_max=2147483647
sysctl -w net.core.wmem_default=2147483647

# Source ROS 2
if [ -f "${WORKSPACE_DIR}/install/setup.bash" ]; then
    echo "Sourcing ${WORKSPACE_DIR}/install/setup.bash"
    source "${WORKSPACE_DIR}/install/setup.bash"
else
    echo "No ROS 2 workspace found at ${WORKSPACE_DIR}/install/setup.bash"
fi

# Launch ROS 2 node as root
echo "Launching ROS 2 node..."
exec ros2 launch aqita_tests test_component_status_pub.launch.py status_topic:=/esc/status node_name:=psys_component
EOF

chmod +x "$ENTRYPOINT_SCRIPT"
DOCKER_ARGS+=("-v $ENTRYPOINT_SCRIPT:/entrypoint.sh")

# NVIDIA runtime if available
RUNTIME_ARG=""
if docker info | grep -q "Runtimes:.*nvidia"; then
    RUNTIME_ARG="--runtime nvidia"
fi

# Run container
docker run --rm \
    --privileged \
    --network host \
    --ipc=host \
    ${DOCKER_ARGS[@]} \
    --name "$CONTAINER_NAME" \
    --entrypoint /entrypoint.sh \
    --workdir /workspace \
    $RUNTIME_ARG \
    $IMAGE_NAME

rm -f "$ENTRYPOINT_SCRIPT"

print_info "Container '$CONTAINER_NAME' started in background."
print_info "Use 'docker logs -f $CONTAINER_NAME' to follow logs."

