#!/usr/bin/env bash

set -e

# Colors for output
RED="\033[1;31m"
GREEN="\033[1;32m"
YELLOW="\033[1;33m"
NC="\033[0m"

# Check if we are in the correct directory
EXPECTED_DIR_NAME="aqita_services"
CURRENT_DIR_NAME="$(basename "$(pwd)")"

if [[ "$CURRENT_DIR_NAME" != "$EXPECTED_DIR_NAME" ]]; then
    echo -e "${RED}ERROR:${NC} This script must be run from the '${EXPECTED_DIR_NAME}' directory."
    exit 1
fi

# Find parent directory containing install/setup.bash
SEARCH_DIR="$(pwd)"
FOUND=0

while [[ "$SEARCH_DIR" != "/" ]]; do
    if [[ -f "$SEARCH_DIR/install/setup.bash" ]]; then
        WORKSPACE_DIR="$SEARCH_DIR"
        FOUND=1
        break
    fi
    SEARCH_DIR="$(dirname "$SEARCH_DIR")"
done

if [[ "$FOUND" -ne 1 ]]; then
    echo -e "${RED}ERROR:${NC} Could not find 'install/setup.bash' in any parent directory."
    exit 1
fi

echo -e "${GREEN}Found workspace directory:${NC} $WORKSPACE_DIR"

# Create /etc/aqita.env
ENV_FILE="/etc/aqita.env"
echo "Creating $ENV_FILE with workspace path..."
echo "AQITA_ROS_WS=$WORKSPACE_DIR" | sudo tee "$ENV_FILE" >/dev/null

echo -e "${GREEN}Created /etc/aqita.env with:${NC}"
cat "$ENV_FILE"

# Copy .service files
echo -e "${YELLOW}Copying .service files to /etc/systemd/system/...${NC}"
find . -type f -name "*.service" | while read -r SERVICE_FILE; do
    echo "  Installing $(basename "$SERVICE_FILE")"
    sudo cp "$SERVICE_FILE" /etc/systemd/system/
done

# Copy .sh files
echo -e "${YELLOW}Copying .sh files to /usr/bin/...${NC}"
find . -type f -name "*.sh" | while read -r SH_FILE; do
    echo "  Installing $(basename "$SH_FILE")"
    sudo cp "$SH_FILE" /usr/bin/
    sudo chmod +x "/usr/bin/$(basename "$SH_FILE")"
done

# Reload systemd
echo -e "${YELLOW}Reloading systemd daemon...${NC}"
sudo systemctl daemon-reload

echo -e "${GREEN}All done!${NC}"

