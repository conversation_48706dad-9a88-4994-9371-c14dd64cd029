
# aqita_services

This repository contains systemd service files and associated scripts for managing AQITA ROS2 components.

## Overview

The `install_services.sh` script automates the installation of these services and their dependencies on your system. It performs the following actions:

- Verifies that you run it from the `aqita_services` directory.  
- Searches parent directories to locate your ROS2 workspace containing `install/setup.bash`.  
- Generates an environment file (`/etc/aqita.env`) with the workspace path.  
- Copies all `.service` files to `/etc/systemd/system/`.  
- Copies all `.sh` files to `/usr/bin/` and makes them executable.  
- Reloads `systemd` so services are ready to use.

## Usage

Follow these steps:

1. Open a terminal.
2. **Navigate into the `aqita_services` directory:**

```bash
cd /path/to/your_ros_workspace/src/aqita_services
```

3. Run the installer:

```bash
./install_services.sh
```

The script will:

- Detect your ROS2 workspace root automatically.

- Write /etc/aqita.env containing:

```bash
AQITA_ROS_WS=/path/to/folder/containing/install/setup.bash
```

- Copy all .service files to /etc/systemd/system/.

- Copy all .sh files to /usr/bin/.

- Reload systemd to apply changes:


## Managing the Services
Once installed, you can manage the services using systemctl. For example:

```bash
sudo systemctl start aqita_slam.service
sudo systemctl status aqita_slam.service
sudo systemctl stop aqita_slam.service
```

The above commands can be used for test purposes. The aqita state machine will start and stop these services automatically when needed.

