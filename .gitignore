**/build


# Prerequisites
*.d

# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod
*.smod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# IDE stuff
*.vscode

# Build files
*build

# ROS2
/build*
/install*
/log*
*.log
data/*

# Python
*.pyc

# Reconstructions
*.ply

# Buffering databases
*.db*

# Parquet archive files
**/*.parquet
*.parquet

