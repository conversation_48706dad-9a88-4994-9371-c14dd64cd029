#!/bin/bash
#
# Script to run Aqita container with proper user permissions
# Adapted from NVIDIA Isaac ROS scripts

set -e

# Print functions for better output
function print_error() {
    echo -e "\033[1;31mERROR: $1\033[0m"
}

function print_warning() {
    echo -e "\033[1;33mWARNING: $1\033[0m"
}

function print_info() {
    echo -e "\033[1;32mINFO: $1\033[0m"
}

function usage() {
    print_info "Usage: run_aqita.sh [OPTIONS]"
    print_info "Options:"
    print_info "  -n, --name NAME       Container name (default: aqita_container)"
    print_info "  -i, --image IMAGE     Docker image to use (auto-detected based on architecture)"
    print_info "  -w, --workspace DIR   Workspace directory to mount (default: current directory)"
    print_info "  -p, --pull            Pull the latest image before running"
    print_info "  -a, --docker-arg ARG  Pass additional arguments to docker run"
    print_info "  -h, --help            Show this help message"
}

# Default values
CONTAINER_NAME="aqita_container"
WORKSPACE_DIR="$(pwd)"
PULL_IMAGE=0
DOCKER_ARGS=()

# Detect platform architecture
PLATFORM="$(uname -m)"
if [[ $PLATFORM == "x86_64" ]]; then
    IMAGE_NAME="aqita-x86:latest"
    GITHUB_IMAGE="ghcr.io/uvionix/aqita-x86:latest"
    print_info "Detected x86_64 platform, using image: $IMAGE_NAME"
elif [[ $PLATFORM == "aarch64" ]]; then
    IMAGE_NAME="aqita-arm:latest"
    GITHUB_IMAGE="ghcr.io/uvionix/aqita-arm:latest"
    print_info "Detected aarch64 platform, using image: $IMAGE_NAME"
else
    print_warning "Unknown platform: $PLATFORM. Defaulting to ARM image."
    IMAGE_NAME="aqita-arm:latest"
    GITHUB_IMAGE="ghcr.io/uvionix/aqita-arm:latest"
fi

# Parse command line arguments
VALID_ARGS=$(getopt -o n:i:w:pa:h --long name:,image:,workspace:,pull,docker-arg:,help -- "$@")
eval set -- "$VALID_ARGS"
while [ : ]; do
    case "$1" in
    -n | --name)
        CONTAINER_NAME="$2"
        shift 2
        ;;
    -i | --image)
        IMAGE_NAME="$2"
        # Update GitHub image based on provided image
        if [[ "$2" == *"-x86"* ]]; then
            GITHUB_IMAGE="ghcr.io/uvionix/aqita-x86:latest"
        elif [[ "$2" == *"-arm"* ]]; then
            GITHUB_IMAGE="ghcr.io/uvionix/aqita-arm:latest"
        else
            GITHUB_IMAGE="ghcr.io/uvionix/$(basename $2 | cut -d ':' -f 1):latest"
        fi
        shift 2
        ;;
    -w | --workspace)
        WORKSPACE_DIR="$2"
        shift 2
        ;;
    -p | --pull)
        PULL_IMAGE=1
        shift
        ;;
    -a | --docker-arg)
        DOCKER_ARGS+=("$2")
        shift 2
        ;;
    -h | --help)
        usage
        exit 0
        ;;
    --)
        shift
        break
        ;;
    esac
done

# Prevent running as root
if [[ $(id -u) -eq 0 ]]; then
    print_error "This script cannot be executed with root privileges."
    print_error "Please re-run without sudo and follow instructions to configure docker for non-root user if needed."
    exit 1
fi

# Check if user can run docker without root
RE="\<docker\>"
if [[ ! $(groups $USER) =~ $RE ]]; then
    print_error "User |$USER| is not a member of the 'docker' group and cannot run docker commands without sudo."
    print_error "Run 'sudo usermod -aG docker \$USER && newgrp docker' to add user to 'docker' group, then re-run this script."
    print_error "See: https://docs.docker.com/engine/install/linux-postinstall/"
    exit 1
fi

# Check if able to run docker commands
if [[ -z "$(docker ps)" ]]; then
    print_error "Unable to run docker commands. If you have recently added |$USER| to 'docker' group, you may need to log out and log back in for it to take effect."
    print_error "Otherwise, please check your Docker installation."
    exit 1
fi

# Validate workspace directory
if [[ ! -d "$WORKSPACE_DIR" ]]; then
    print_error "Specified workspace directory does not exist: $WORKSPACE_DIR"
    exit 1
fi
WORKSPACE_DIR=$(realpath "$WORKSPACE_DIR")

# Note: To push images to GitHub Container Registry, use:
# docker login ghcr.io -u USERNAME -p GITHUB_TOKEN
# docker tag aqita-x86:latest ghcr.io/uvionix/aqita-x86:latest
# docker push ghcr.io/uvionix/aqita-x86:latest
#
# Or for ARM:
# docker tag aqita-arm:latest ghcr.io/uvionix/aqita-arm:latest
# docker push ghcr.io/uvionix/aqita-arm:latest

# Pull image if requested
if [[ $PULL_IMAGE -eq 1 ]]; then
    print_info "Pulling latest image: $GITHUB_IMAGE"
    if ! docker pull $GITHUB_IMAGE; then
        print_warning "Failed to pull image from GitHub Container Registry. Check if the image exists or your authentication."
    else
        docker tag $GITHUB_IMAGE $IMAGE_NAME
        print_info "Successfully pulled and tagged image as: $IMAGE_NAME"
    fi
fi

# Check if image exists
if [[ -z $(docker image ls --quiet $IMAGE_NAME) ]]; then
    print_warning "Image $IMAGE_NAME not found locally. Attempting to pull from GitHub."

    if ! docker pull $GITHUB_IMAGE; then
        print_error "Failed to pull $GITHUB_IMAGE from GitHub Container Registry."
        print_error "Make sure the image exists and you have proper authentication."
        print_error "You may need to run: docker login ghcr.io -u USERNAME -p GITHUB_TOKEN"
        exit 1
    fi

    docker tag $GITHUB_IMAGE $IMAGE_NAME
    print_info "Successfully pulled and tagged image as: $IMAGE_NAME"

    if [[ -z $(docker image ls --quiet $IMAGE_NAME) ]]; then
        print_error "Failed to find or pull image $IMAGE_NAME. Aborting."
        exit 1
    fi
fi

# Remove any exited containers with the same name
if [ "$(docker ps -a --quiet --filter status=exited --filter name=$CONTAINER_NAME)" ]; then
    print_info "Removing exited container: $CONTAINER_NAME"
    docker rm $CONTAINER_NAME >/dev/null
fi

# Re-use existing container if it's already running
if [ "$(docker ps -a --quiet --filter status=running --filter name=$CONTAINER_NAME)" ]; then
    print_info "Attaching to running container: $CONTAINER_NAME"
    WORKSPACE=$(docker exec $CONTAINER_NAME printenv WORKSPACE_DIR || echo "/workspace")
    print_info "Docker workspace: $WORKSPACE"

    # Create a temporary script to execute inside the container
    TEMP_EXEC_SCRIPT=$(mktemp)
    cat >$TEMP_EXEC_SCRIPT <<EOF
#!/bin/bash
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp

# Source ROS 2 setup if it exists
if [ -f "$WORKSPACE/install/setup.bash" ]; then
    echo "Sourcing $WORKSPACE/install/setup.bash"
    source "$WORKSPACE/install/setup.bash"
else
    echo "No ROS 2 workspace found at $WORKSPACE/install/setup.bash"
fi

# Start an interactive shell
exec /bin/bash
EOF
    chmod +x $TEMP_EXEC_SCRIPT

    # Copy the script to the container and execute it
    CONTAINER_SCRIPT="/tmp/exec_script_$(date +%s).sh"
    docker cp $TEMP_EXEC_SCRIPT $CONTAINER_NAME:$CONTAINER_SCRIPT
    docker exec -it -u $(id -u):$(id -g) --workdir $WORKSPACE $CONTAINER_NAME $CONTAINER_SCRIPT

    # Clean up
    docker exec $CONTAINER_NAME rm -f $CONTAINER_SCRIPT
    rm -f $TEMP_EXEC_SCRIPT
    exit 0
fi

xhost +local:docker
# Set up Docker arguments
DOCKER_ARGS+=("-v $WORKSPACE_DIR:/workspace")
DOCKER_ARGS+=("-v /tmp/.X11-unix:/tmp/.X11-unix")
DOCKER_ARGS+=("-v $HOME/.Xauthority:/home/<USER>/.Xauthority:rw")
DOCKER_ARGS+=("-e DISPLAY")
DOCKER_ARGS+=("-e USER")
DOCKER_ARGS+=("-e WORKSPACE_DIR=/workspace")
DOCKER_ARGS+=("-e HOST_USER_UID=$(id -u)")
DOCKER_ARGS+=("-e HOST_USER_GID=$(id -g)")
DOCKER_ARGS+=("-e USERNAME=admin")
DOCKER_ARGS+=("-v /run/dbus/system_bus_socket:/run/dbus/system_bus_socket")
# Add RMW_IMPLEMENTATION env var for CycloneDDS
DOCKER_ARGS+=("-e RMW_IMPLEMENTATION=rmw_cyclonedds_cpp")

# Forward SSH Agent to container if the ssh agent is active
if [[ -n $SSH_AUTH_SOCK ]]; then
    DOCKER_ARGS+=("-v $SSH_AUTH_SOCK:/ssh-agent")
    DOCKER_ARGS+=("-e SSH_AUTH_SOCK=/ssh-agent")
fi

# Platform-specific arguments
if [[ $PLATFORM == "aarch64" ]]; then
    # For Jetson platforms
    print_info "Setting up ARM/Jetson-specific Docker arguments"
    DOCKER_ARGS+=("-e NVIDIA_VISIBLE_DEVICES=all")
    DOCKER_ARGS+=("-e NVIDIA_DRIVER_CAPABILITIES=all")
    DOCKER_ARGS+=("-v /tmp/:/tmp/")
    DOCKER_ARGS+=("--pid=host")
    DOCKER_ARGS+=("-v /dev/input:/dev/input")

    # For Jetson Orin/Xavier specific paths
    if [ -d "/usr/lib/aarch64-linux-gnu/tegra" ]; then
        DOCKER_ARGS+=("-v /usr/lib/aarch64-linux-gnu/tegra:/usr/lib/aarch64-linux-gnu/tegra")
    fi

    if [ -d "/usr/src/jetson_multimedia_api" ]; then
        DOCKER_ARGS+=("-v /usr/src/jetson_multimedia_api:/usr/src/jetson_multimedia_api")
    fi

    if [ -d "/usr/share/vpi3" ]; then
        DOCKER_ARGS+=("-v /usr/share/vpi3:/usr/share/vpi3")
    fi

    # If jtop present, give the container access
    if [[ $(getent group jtop) ]]; then
        DOCKER_ARGS+=("-v /run/jtop.sock:/run/jtop.sock:ro")
    fi
elif [[ $PLATFORM == "x86_64" ]]; then
    print_info "Setting up x86_64-specific Docker arguments"
    # Add any x86_64-specific arguments here if needed
    # For NVIDIA GPU support on x86_64
    if command -v nvidia-smi &>/dev/null; then
        DOCKER_ARGS+=("-e NVIDIA_VISIBLE_DEVICES=all")
        DOCKER_ARGS+=("-e NVIDIA_DRIVER_CAPABILITIES=all")
    fi
fi

# Create entrypoint script that will be mounted into container
ENTRYPOINT_SCRIPT=$(mktemp)
cat >$ENTRYPOINT_SCRIPT <<'EOF'
#!/bin/bash

# Apply realsense udev rules
sudo service udev restart

USERNAME="${USERNAME:-admin}"
HOST_USER_UID="${HOST_USER_UID:-1000}"
HOST_USER_GID="${HOST_USER_GID:-1000}"

# Set network buffer sizes to increase performance
echo "Setting network buffer sizes for high performance networking"
sysctl -w net.core.rmem_max=2147483647
sysctl -w net.core.rmem_default=2147483647
sysctl -w net.core.wmem_max=2147483647
sysctl -w net.core.wmem_default=2147483647

echo "Creating non-root user '${USERNAME}' for host user uid=${HOST_USER_UID}:gid=${HOST_USER_GID}"

# Create/update group
if [ ! $(getent group ${HOST_USER_GID}) ]; then
  groupadd --gid ${HOST_USER_GID} ${USERNAME} &>/dev/null
else
  CONFLICTING_GROUP_NAME=$(getent group ${HOST_USER_GID} | cut -d: -f1)
  groupmod -o --gid ${HOST_USER_GID} -n ${USERNAME} ${CONFLICTING_GROUP_NAME} &>/dev/null || true
fi

# Create/update user
if [ ! $(getent passwd ${HOST_USER_UID}) ]; then
  useradd --no-log-init --uid ${HOST_USER_UID} --gid ${HOST_USER_GID} -m ${USERNAME} &>/dev/null
else
  CONFLICTING_USER_NAME=$(getent passwd ${HOST_USER_UID} | cut -d: -f1)
  usermod -l ${USERNAME} -u ${HOST_USER_UID} -g ${HOST_USER_GID} -m -d /home/<USER>/dev/null || true
  mkdir -p /home/<USER>
  # Wipe files that may create issues for users with large uid numbers
  rm -f /var/log/lastlog /var/log/faillog
fi

# Update user permissions
chown ${USERNAME}:${USERNAME} /home/<USER>
echo "${USERNAME} ALL=(root) NOPASSWD:ALL" > /etc/sudoers.d/${USERNAME}
chmod 0440 /etc/sudoers.d/${USERNAME}

# Add user to necessary groups
for group in video plugdev sudo; do
  if getent group $group >/dev/null; then
    adduser ${USERNAME} $group &>/dev/null || true
  fi
done

# If jtop present, give the user access
if [ -S /run/jtop.sock ]; then
  JETSON_STATS_GID="$(stat -c %g /run/jtop.sock)"
  addgroup --gid ${JETSON_STATS_GID} jtop &>/dev/null || true
  adduser ${USERNAME} jtop &>/dev/null || true
fi

# Setup workspace directory permissions
mkdir -p "${WORKSPACE_DIR:-/workspace}"
chown ${USERNAME}:${USERNAME} "${WORKSPACE_DIR:-/workspace}"

# Add RMW_IMPLEMENTATION to user's bashrc to have it in all terminal sessions
echo "export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp" >> /home/<USER>/.bashrc

# Source ROS 2 setup.bash if it exists
cat >> /home/<USER>/.bashrc << 'EOBASHRC'
# Source ROS 2 workspace if it exists
if [ -f "${WORKSPACE_DIR}/install/setup.bash" ]; then
  echo "Sourcing ${WORKSPACE_DIR}/install/setup.bash"
  source "${WORKSPACE_DIR}/install/setup.bash"
  alias build_aqita="colcon build --symlink-install --packages-select-regex aqita_*"
  alias build_nav2_aqita="colcon build --symlink-install --packages-select-regex nav2_aqita_*"
  alias launch_nav2_aqita_sim="ros2 launch nav2_aqita_bringup test_isaac_sim_navigation.launch.py"
  alias launch_aqita_sim_stereo3="
    ros2 launch aqita_launch \
        test_isaac_stereo_multicam.launch.py \
        run_navigation:=true \
        enable_left_camera:=true \
        enable_right_camera:=true \
  "
else
  echo "No ROS 2 workspace found at ${WORKSPACE_DIR}/install/setup.bash"
fi
EOBASHRC

# Execute command with gosu to drop to user
if [ -x "$(command -v gosu)" ]; then
  exec gosu ${USERNAME} "$@"
else
  # If gosu is not available, use su-exec or su
  if [ -x "$(command -v su-exec)" ]; then
    exec su-exec ${USERNAME} "$@"
  else
    exec su -l ${USERNAME} -c "$*"
  fi
fi
EOF

chmod +x $ENTRYPOINT_SCRIPT
DOCKER_ARGS+=("-v $ENTRYPOINT_SCRIPT:/entrypoint.sh")
DOCKER_ARGS+=("-v /run/dbus/system_bus_socket:/run/dbus/system_bus_socket")
DOCKER_ARGS+=("-v $HOME/.aws-iot-device-client:/aws_client_settings_dir")
DOCKER_ARGS+=("-v /usr/lib/systemd/system:/systemd")
DOCKER_ARGS+=("-e HOME_DIR=$HOME")

# Run container
print_info "Running container: $CONTAINER_NAME"
print_info "Using image: $IMAGE_NAME (detected platform: $PLATFORM)"
print_info "Mounting workspace from: $WORKSPACE_DIR"
print_info "Using RMW_IMPLEMENTATION=rmw_cyclonedds_cpp for ROS 2"
print_info "Will attempt to source install/setup.bash if it exists"
print_info "Network buffer sizes will be increased for high-performance networking"

# Set appropriate runtime based on platform and available runtimes
RUNTIME_ARG=""
if docker info | grep -q "Runtimes:.*nvidia"; then
    RUNTIME_ARG="--runtime nvidia"
    print_info "Using NVIDIA runtime"
elif [[ $PLATFORM == "aarch64" ]] && [ -x "$(command -v nvidia-smi)" ]; then
    RUNTIME_ARG="--runtime nvidia"
    print_info "Using NVIDIA runtime on Jetson"
fi

docker run -it --rm \
    --privileged \
    --network host \
    --ipc=host \
    ${DOCKER_ARGS[@]} \
    --name "$CONTAINER_NAME" \
    --entrypoint /entrypoint.sh \
    --workdir /workspace \
    $RUNTIME_ARG \
    $IMAGE_NAME \
    /bin/bash

# Cleanup temporary entrypoint script
rm -f $ENTRYPOINT_SCRIPT

