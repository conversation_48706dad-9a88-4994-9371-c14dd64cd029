<?xml version="1.0"?>

<!--
// TODO: License description
-->

<!-- Robot description file for a modular Realsense camera. The modular Realsense camera consists of a depth module and a
processing interface board with an onboard IMU. This file defines the relative position and orientation between the various
frames associated with the depth module, the processing board and the robot itself. -->

<robot xmlns:xacro="http://wiki.ros.org/xacro">
  <xacro:property name="M_PI"     value="3.1415926535897931" />

  <!--
    Parameters:
      - type: type of the Realsense camera. If set to 'modular' the provided parameters define the custom pose of the processing board w.r.t. the depth module
      - cam_name: the unique name of the camera
      - robot_base_link: the name of the robot base link
      - base_offset_x: offset of the camera base link w.r.t. the robot base link along the X direction of the robot base link
      - base_offset_y: offset of the camera base link w.r.t. the robot base link along the Y direction of the robot base link
      - base_offset_z: offset of the camera base link w.r.t. the robot base link along the Z direction of the robot base link
      - base_roll, base_pitch, base_yaw: orientation of the camera base link w.r.t. the robot base link
      - baseline: distance between the optical axes of the left and right imagers
      - imu_offset_x: offset of the IMU sensor w.r.t. the left camera optical center along the X direction of the left camera frame LC
      - imu_offset_y: offset of the IMU sensor w.r.t. the left camera optical center along the Y direction of the left camera frame LC
      - imu_offset_z: offset of the IMU sensor w.r.t. the left camera optical center along the Z direction of the left camera frame LC
      - imu_roll, imu_pitch, imu_yaw: orientation of the IMU sensor w.r.t. the left camera frame LC

      NOTE: The X-axis of the left camera frame LC is along the camera optical axis
      NOTE: The Z-axis of the left camera optical frame LCO is along the camera optical axis
      NOTE: If 'type==modular' the provided parameters define the custom pose of the processing board w.r.t. the depth module.
            In this mode the camera must not publish any transforms.
            Otherwise, only the pose of the camera base link w.r.t. the robot base link is defined. The remaining static transforms are published by the
            Realsense camera w.r.t. the defined camera base link. This is tested with D455 camera and librealsense 2.55.1
      NOTE: All length units have to be specified in meters
      NOTE: All angular units have to be specified in radians
  -->
  <xacro:macro name="rs_camera" params="type=standard cam_name=rsd455 robot_base_link=base_link base_offset_x=0 base_offset_y=0 base_offset_z=0 
  base_roll=0 base_pitch=0 base_yaw=0 baseline=0 imu_offset_x=0 imu_offset_y=0 imu_offset_z=0 imu_roll=0 imu_pitch=0 imu_yaw=0">

    <!-- Define the connection between the camera and the robot base link -->
    <link name="${cam_name}_link" />
    <joint name="${cam_name}_link_joint" type="fixed">
      <parent link="${robot_base_link}"/>
      <child link="${cam_name}_link"/>
      <origin xyz="${base_offset_x} ${base_offset_y} ${base_offset_z}" rpy="${base_roll} ${base_pitch} ${base_yaw}" />
    </joint>

    <xacro:if value="${type == 'modular'}">

      <!-- Define the connection between the camera and the IMU accelerometer frame -->
      <link name="${cam_name}_accel_frame" />
      <joint name="${cam_name}_accel_frame_joint" type="fixed">
        <parent link="${cam_name}_link"/>
        <child link="${cam_name}_accel_frame"/>
        <origin xyz="${imu_offset_x} ${imu_offset_y} ${imu_offset_z}" rpy="${imu_roll} ${imu_pitch} ${imu_yaw}" />
      </joint>

      <link name="${cam_name}_accel_optical_frame" />
      <joint name="${cam_name}_accel_optical_frame_joint" type="fixed">
        <parent link="${cam_name}_accel_frame"/>
        <child link="${cam_name}_accel_optical_frame"/>
        <origin xyz="0 0 0" rpy="-${M_PI/2} 0.0 -${M_PI/2}" />
      </joint>

      <!-- Define the connection between the camera and the IMU gyro frame -->
      <link name="${cam_name}_gyro_frame" />
      <joint name="${cam_name}_gyro_frame_joint" type="fixed">
        <parent link="${cam_name}_link"/>
        <child link="${cam_name}_gyro_frame"/>
        <origin xyz="${imu_offset_x} ${imu_offset_y} ${imu_offset_z}" rpy="${imu_roll} ${imu_pitch} ${imu_yaw}" />
      </joint>

      <link name="${cam_name}_gyro_optical_frame" />
      <joint name="${cam_name}_gyro_optical_frame_joint" type="fixed">
        <parent link="${cam_name}_gyro_frame"/>
        <child link="${cam_name}_gyro_optical_frame"/>
        <origin xyz="0 0 0" rpy="-${M_PI/2} 0.0 -${M_PI/2}" />
      </joint>

      <!-- Define the connection between the camera and the left imager -->
      <link name="${cam_name}_aligned_depth_to_infra1_frame" />
      <joint name="${cam_name}_aligned_depth_to_infra1_frame_joint" type="fixed">
        <parent link="${cam_name}_link"/>
        <child link="${cam_name}_aligned_depth_to_infra1_frame"/>
        <origin xyz="0 0 0" rpy="0 0 0" />
      </joint>

      <link name="${cam_name}_infra1_optical_frame" />
      <joint name="${cam_name}_infra1_optical_frame_joint" type="fixed">
        <parent link="${cam_name}_aligned_depth_to_infra1_frame"/>
        <child link="${cam_name}_infra1_optical_frame"/>
        <origin xyz="0 0 0" rpy="-${M_PI/2} 0.0 -${M_PI/2}" />
      </joint>

      <link name="${cam_name}_infra1_frame" />
      <joint name="${cam_name}_infra1_frame_joint" type="fixed">
        <parent link="${cam_name}_link"/>
        <child link="${cam_name}_infra1_frame"/>
        <origin xyz="0 0 0" rpy="0 0 0" />
      </joint>

      <!-- Define the connection between the camera and the right imager -->
      <link name="${cam_name}_infra2_frame" />
      <joint name="${cam_name}_infra2_frame_joint" type="fixed">
        <parent link="${cam_name}_link"/>
        <child link="${cam_name}_infra2_frame"/>
        <origin xyz="0 -${baseline} 0" rpy="0 0 0" />
      </joint>

      <link name="${cam_name}_infra2_optical_frame" />
      <joint name="${cam_name}_infra2_optical_frame_joint" type="fixed">
        <parent link="${cam_name}_infra2_frame"/>
        <child link="${cam_name}_infra2_optical_frame"/>
        <origin xyz="0 0 0" rpy="-${M_PI/2} 0.0 -${M_PI/2}" />
      </joint>

      <!-- Define the connection between the camera and the RGB sensor -->
      <link name="${cam_name}_color_frame" />
      <joint name="${cam_name}_color_frame_joint" type="fixed">
        <parent link="${cam_name}_link"/>
        <child link="${cam_name}_color_frame"/>
        <origin xyz="-0.000269208 -0.0591719 -0.00000156038" rpy="0 0 0" />
      </joint>

      <link name="${cam_name}_color_optical_frame" />
      <joint name="${cam_name}_color_optical_frame_joint" type="fixed">
        <parent link="${cam_name}_color_frame"/>
        <child link="${cam_name}_color_optical_frame"/>
        <origin xyz="0 0 0" rpy="-${M_PI/2} 0.0 -${M_PI/2}" />
      </joint>

      <!-- Define the connection between the camera and the depth frames -->
      <link name="${cam_name}_depth_frame" />
      <joint name="${cam_name}_depth_frame_joint" type="fixed">
        <parent link="${cam_name}_link"/>
        <child link="${cam_name}_depth_frame"/>
        <origin xyz="0 0 0" rpy="0 0 0" />
      </joint>

      <link name="${cam_name}_depth_optical_frame" />
      <joint name="${cam_name}_depth_optical_frame_joint" type="fixed">
        <parent link="${cam_name}_depth_frame"/>
        <child link="${cam_name}_depth_optical_frame"/>
        <origin xyz="0 0 0" rpy="-${M_PI/2} 0.0 -${M_PI/2}" />
      </joint>

    </xacro:if>
    
  </xacro:macro>
</robot>
