rtabmap:
  ros__parameters:
    # Basic configuration
    frame_id: "base_link"                 # TF frame for RTAB-Map base reference
    map_frame_id: "map"                   # TF frame for published map
    odom_frame_id: "odom"                 # TF frame for odometry
    use_sim_time: false                   # Use simulation time
    database_path: "./data/rtabmap.db"    # Path to RTAB-Map database file

    # Subscription settings
    subscribe_depth: true                 # Subscribe to depth images
    subscribe_rgb: true                   # Subscribe to RGB/infrared images
    subscribe_rgbd: false                 # Subscribe to combined RGBD messages
    subscribe_scan_cloud: false           # Subscribe to 3D laser scan
    subscribe_imu: true                   # Subscribe to IMU messages
    imu_frame_id: "cam_front_imu_optical_frame"  # TF frame for IMU
    
    # Synchronization parameters
    approx_sync: true                     # Use approximate time synchronization
    approx_sync_max_interval: 0.02        # Max time difference between messages (seconds)
    queue_size: 15                        # Size of the message queue
    qos: 1                                # Quality of Service (1=reliable)

    # Update rates for mapping
    Rtabmap/DetectionRate: "3.0"          # Detection rate for loop closure
    RGBD/AngularUpdate: "0.03"            # Min angle change to update map (rad)
    RGBD/LinearUpdate: "0.04"             # Min distance change to update map (m)
    
    # Loop closure parameters
    RGBD/NeighborLinkRefining: "true"     # Refine neighbor links with visual information
    RGBD/ProximityBySpace: "true"         # Detect nearby nodes using metric positions
    RGBD/OptimizeFromGraphEnd: "true"     # Optimize graph from the latest node
    RGBD/LoopClosureReextractFeatures: "true"  # Re-extract features at loop closure
    RGBD/MaxLocalRetrieved: "15"          # Max local maps to retrieve in working memory
    RGBD/LocalRadius: "5.0"               # Radius in meters to filter local loop closure hypotheses
    
    # Optimization parameters
    RGBD/OptimizeMaxError: "0.6"          # Max optimization error (m)
    RGBD/OptimizeIterations: "100"        # Max iterations for graph optimization
    
    # Grid/map parameters
    RGBD/CreateOccupancyGrid: "true"      # Create 2D occupancy grid map
    Grid/CellSize: "0.1"                  # Size of occupancy grid cells (m)
    Grid/RangeMin: "0.2"                  # Min sensor range to use for grid (m)
    Grid/RangeMax: "12.0"                 # Max sensor range to use for grid (m)
    Grid/3D: "true"                       # Enable 3D mapping
    Grid/DepthDecimation: "2"             # Decimation of depth images for grid creation
    Grid/FlatObstacleDetected: "true"     # Detect flat obstacles for mapping
    Grid/MaxGroundHeight: "0.1"           # Max height of ground plane (m)
    Grid/MaxObstacleHeight: "3.0"         # Max height of obstacles (m)
    Grid/NoiseFilteringMinNeighbors: "3"  # Min cluster size for noise filtering
    Grid/RayTracing: "true"               # Use ray tracing for grid construction
    
    # Memory management
    Mem/ImagePreDecimation: "2"           # Input image decimation for processing
    Mem/ImagePostDecimation: "2"          # Result image decimation for storage
    Mem/STMSize: "20"                     # Short-term memory size (nodes)
    Mem/BinDataKept: "false"              # Keep binary data in memory
    Mem/NotLinkedNodesKept: "true"       # Keep nodes not linked to the graph
    Mem/RehearsalSimilarity: "0.45"       # Similarity threshold for rehearsal
    
    # Feature extraction
    Kp/DetectorStrategy: "6"              # Feature detector (0=SURF, 6=FAST, 8=ORB)
    Kp/MaxFeatures: "800"                 # Max features per image
    Kp/ROI: "0 0 0 0"                     # Region of interest (x,y,width,height)
    
    # Visual odometry parameters
    Vis/MinInliers: "10"                  # Min inliers for valid transformation
    Vis/MaxDepth: "100.0"                  # Max depth for feature points (m)
    Vis/MinDepth: "0.2"                   # Min depth for feature points (m)
    Vis/EstimationType: "1"               # Motion estimation (0=3D->3D, 1=3D->2D PnP)
    Vis/GPU: "true"                       # Use GPU for visual processing
    
    # Additional parameters
    Reg/Strategy: "0"                     # Registration strategy (0=Visual, 1=ICP, 2=Visual+ICP)
    Grid/FromDepth: "true"                # Create grid map from depth images
    Optimizer/Slam2D: "false"             # Perform 2D SLAM (false = 3D)
    Rtabmap/PublishStatsAsync: "true"     # Publish statistics asynchronously
    Rtabmap/TimeThr: "650"                # Max optimization time in ms
    Rtabmap/MemoryThr: "0"                # Memory threshold