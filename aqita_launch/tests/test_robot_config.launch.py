# TODO: License description

# Import launch tools
import os, sys
import isaac_ros_launch_utils as lu
from ament_index_python.packages import get_package_share_directory
from launch import logging, LaunchDescription, LaunchContext, Action
from launch.actions import DeclareLaunchArgument, OpaqueFunction, TimerAction, ExecuteProcess
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import ComposableNodeContainer, Node
from aqita_config.base_classes import OdomType, OdomSource
from aqita_config.slam.rtabmap.rtabmap_config import RtabMapPoseEstimator
from aqita_config.robot.xoss_3rs.xoss3rs_this_robot import Xoss3RsThisRobot
from aqita_config.robot.xoss_3rs.xoss3rs_config import create_robot_stereo, create_robot_rgbd, Xoss3RsRobotConfiguration

# Launch parameter names
LAUNCH_PARAMETERS = {
    'this_robot': 'this_robot',
    'enable_front_cam': 'enable_front_camera',
    'enable_left_cam': 'enable_left_camera',
    'enable_right_cam': 'enable_right_camera',
    'use_rgbd_odometry': 'use_rgbd_odometry',
    'use_isaac_ros_odometry': 'use_isaac_ros_odometry',
    'simulation_mode': 'simulation_mode',
    'to_rosbag': 'to_rosbag',
    'from_rosbag': 'from_rosbag'
}

def get_rosbag_record_action(robot_config: Xoss3RsRobotConfiguration, odom_type: OdomType) -> Action | None:

    topics_to_record = [
        '/tf_static',
        robot_config.imu_topic
    ]

    for cam in robot_config.get_stereo_camera_list():
        if odom_type == OdomType.STEREO:
            topics_to_record.append(cam.left_image_topic)
            topics_to_record.append(cam.right_image_topic)
            topics_to_record.append(cam.left_cam_info_topic)
            topics_to_record.append(cam.right_cam_info_topic)
        elif odom_type == OdomType.RGBD:
            topics_to_record.append(cam.rgb_image_topic)
            topics_to_record.append(cam.depth_image_topic)
            topics_to_record.append(cam.rgb_cam_info_topic)
        else:
            return None

    bag_record = ExecuteProcess(
        cmd=["ros2", "bag", "record", " ".join(topics_to_record)],
        shell=True,
        output='screen'
    )

    return bag_record

def launch_function(context: LaunchContext):

    logger = logging.get_logger('launch')
    logger.info('Starting RTAB-Map testing pipeline ...')

    # Create the launch configuration substitutions
    this_robot = LaunchConfiguration(LAUNCH_PARAMETERS['this_robot']).perform(context)
    enable_front_camera = LaunchConfiguration(LAUNCH_PARAMETERS['enable_front_cam']).perform(context).lower() == 'true'
    enable_left_camera = LaunchConfiguration(LAUNCH_PARAMETERS['enable_left_cam']).perform(context).lower() == 'true'
    enable_right_camera = LaunchConfiguration(LAUNCH_PARAMETERS['enable_right_cam']).perform(context).lower() == 'true'
    use_rgbd_odometry = LaunchConfiguration(LAUNCH_PARAMETERS['use_rgbd_odometry'])
    use_isaac_ros_odometry = LaunchConfiguration(LAUNCH_PARAMETERS['use_isaac_ros_odometry'])
    simulation_mode = LaunchConfiguration(LAUNCH_PARAMETERS['simulation_mode']).perform(context).lower() == 'true'
    to_rosbag = LaunchConfiguration(LAUNCH_PARAMETERS['to_rosbag']).perform(context)
    from_rosbag = LaunchConfiguration(LAUNCH_PARAMETERS['from_rosbag']).perform(context)

    # Define the names of the system components
    shared_container_name = 'rtabmap_shared_container'
    log_level = 'info'

    # Create a shared container to hold composable nodes for speed-ups through intra process communication
    shared_container = ComposableNodeContainer(
        name=shared_container_name,
        namespace='',
        package='rclcpp_components',
        executable='component_container_mt',
        output='screen',
        arguments=['--ros-args', '--log-level', log_level],
    )

    # Create the robot configuration
    if use_rgbd_odometry.perform(context).lower() == 'false':
        # Using stereo odometry
        aqita_monitor_camera_mode = 'stereo'
        odom_type = OdomType.STEREO
        if use_isaac_ros_odometry.perform(context).lower() == 'true':
            odom_src = OdomSource.ISAAC_ROS
        else:
            odom_src = OdomSource.RTAB_MAP

        robot_configuration = create_robot_stereo(
            logger=logger,
            this_robot=Xoss3RsThisRobot[this_robot],
            enable_front_camera=enable_front_camera,
            enable_left_camera=enable_left_camera,
            enable_right_camera=enable_right_camera,
            shared_container_name=shared_container_name,
            simulation_mode=simulation_mode,
            camera_launch_delay=10.0)
    else:
        # Using RGBD odometry
        aqita_monitor_camera_mode = 'rgbd'
        odom_type = OdomType.RGBD
        odom_src = OdomSource.RTAB_MAP
        robot_configuration = create_robot_rgbd(
            logger=logger,
            this_robot=Xoss3RsThisRobot[this_robot],
            enable_front_camera=enable_front_camera,
            enable_left_camera=enable_left_camera,
            enable_right_camera=enable_right_camera,
            depth_module_profile='640x480x30',
            shared_container_name=shared_container_name,
            simulation_mode=simulation_mode,
            camera_launch_delay=10.0)

    # Create the RTAB-Map pose estimator
    if simulation_mode:
        param_set = 'sim'
    else:
        if from_rosbag.lower() == 'true':
            param_set = 'rosbag'
        else:
            param_set = 'deploy'

    logger.info(f'Loading {param_set} parameter set ...')
    pkg_share_dir = get_package_share_directory('aqita_config')
    custom_sync_params_filepath = os.path.join(pkg_share_dir, 'robot/xoss_3rs/params', param_set, 'xoss3rs_cam_sync_params.yaml')
    custom_odom_params_filepath = os.path.join(pkg_share_dir, 'robot/xoss_3rs/params', param_set, 'xoss3rs_odom_params.yaml')
    custom_slam_params_filepath = os.path.join(pkg_share_dir, 'robot/xoss_3rs/params', param_set, 'xoss3rs_slam_params.yaml')
    pose_estimator = RtabMapPoseEstimator(
        logger=logger,
        odom_src=odom_src,
        odom_type=odom_type,
        robot_config=robot_configuration,
        shared_container_name=shared_container_name,
        use_imu=True,
        load_map=False,
        map_filepath='',
        custom_sync_params_filepath=custom_sync_params_filepath,
        custom_odom_params_filepath=custom_odom_params_filepath,
        custom_slam_params_filepath=custom_slam_params_filepath)

    # Create the monitor node
    camera_names = []
    for cam in robot_configuration.get_stereo_camera_list():
        camera_names.append(cam.camera_name)
    
    aqita_monitor_cameras = ','.join(camera_names)
    monitoring_launch = lu.include(
        'aqita_monitor', 
        'launch/monitoring.launch.py',
        launch_arguments={
            'camera_mode': aqita_monitor_camera_mode,
            'cameras': aqita_monitor_cameras,
            'base_frame': robot_configuration.robot_base_link,
            'odom_topic': pose_estimator.odom_topic
        }
    )

    # Create the launch actions
    launch_actions = []
    launch_actions.append(TimerAction(period=20.0, actions=[monitoring_launch]))
    launch_actions.append(TimerAction(period=3.0, actions=[shared_container]))
    if not simulation_mode:
        launch_actions.append(robot_configuration.get_rsp_node())

    if from_rosbag.lower() == 'false':
        launch_actions.extend(robot_configuration.get_launch_actions())
    else:
        if to_rosbag.lower() == 'true':
            logger.error('Cannot set both "to_rosbag" and "from_rosbag" equal to "True". Aborting launch!')
            sys.exit()

    if to_rosbag.lower() == 'true':
        bag_record = get_rosbag_record_action(robot_config=robot_configuration, odom_type=odom_type)

        if bag_record is None:
            logger.error('Failed to generate bag record action. Aborting launch!')
            sys.exit()

        launch_actions.append(TimerAction(period=robot_configuration.launch_completed_time, actions=[bag_record]))
    else:
        launch_actions.extend(pose_estimator.get_launch_actions())

    logger.info('Starting nodes ...')
    return launch_actions

def generate_launch_description():

    # Declare the launch arguments
    robot_choices = []
    for robot_id in Xoss3RsThisRobot:
        robot_choices.append(robot_id.name)

    launch_args = [
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['this_robot'],
            default_value=Xoss3RsThisRobot.PLD.name,
            description='Robot ID',
            choices=robot_choices
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['enable_front_cam'],
            default_value='True',
            description='Whether to enable the front camera'
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['enable_left_cam'],
            default_value='True',
            description='Whether to enable the left camera'
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['enable_right_cam'],
            default_value='True',
            description='Whether to enable the right camera',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['use_rgbd_odometry'],
            default_value='False',
            description='Whether to start RTAB-Map in RGBD mode'
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['use_isaac_ros_odometry'],
            default_value='True',
            description='Whether to use the Isaac-ROS VSLAM odometry instead of the default RTAB-Map odometry'
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['simulation_mode'],
            default_value='False',
            description='Whether to start IsaacSim simulation mode'
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['to_rosbag'],
            default_value='False',
            description='Whether to record image and IMU streams to rosbag file. If true odom and slam nodes will not launch!'
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['from_rosbag'],
            default_value='False',
            description='Whether the input topics are coming from a recorded rosbag file. If true the camera nodes will not launch!'
        )
    ]

    return LaunchDescription(launch_args + [OpaqueFunction(function=launch_function)])
