# TODO: Licence description

# NOTE: Launch file for testing and experimenting with the RTAB-Map VSLAM algorithm in the Isaac-Sim simulation environment

# Import launch tools
import os, yaml
import isaac_ros_launch_utils as lu
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription, LaunchContext, logging
from launch.actions import DeclareLaunchArgument, OpaqueFunction, TimerAction
from launch.substitutions import LaunchConfiguration
from launch.conditions import IfCondition, UnlessCondition
from launch_ros.actions import Node, LoadComposableNodes, ComposableNodeContainer
from launch_ros.descriptions import ComposableNode

# Import tools from the aqita initialization module
from aqita_init.topics import get_realsense_camera_topics
from aqita_init.frames import get_realsense_camera_frames
from aqita_init.node_arguments import nvblox_launch_arguments

# Launch parameter names
LAUNCH_PARAMETERS = {
    'enable_left_cam': 'enable_left_camera',
    'enable_right_cam': 'enable_right_camera',
    'use_isaac_ros_odometry': 'use_isaac_ros_odometry',
    'run_navigation': 'run_navigation',
}


def launch_function(context: LaunchContext, *args, **kwargs):

    logger = logging.get_logger('launch')
    logger.info('Starting RTAB-Map testing pipeline for Isaac-Sim...')
    launch_actions = []

    # Create the launch configuration substitutions
    enable_left_camera = LaunchConfiguration(LAUNCH_PARAMETERS['enable_left_cam']).perform(context)
    enable_right_camera = LaunchConfiguration(LAUNCH_PARAMETERS['enable_right_cam']).perform(context)
    use_isaac_ros_odometry = LaunchConfiguration(LAUNCH_PARAMETERS['use_isaac_ros_odometry'])
    run_navigation = LaunchConfiguration(LAUNCH_PARAMETERS['run_navigation'])

    # Define the names of the system components
    shared_container_name = 'rtabmap_shared_container'
    log_level = 'info'
    robot_base_link = 'aqita'
    front_camera_name = 'cam_front'
    left_camera_name = 'cam_left'
    right_camera_name = 'cam_right'
    global_odometry_topic = 'aqita_map_position'
    rtabmap_imu_topic = get_realsense_camera_topics(front_camera_name).imu_topic
    rtabmap_imu_frame_id = get_realsense_camera_frames(front_camera_name).imu_optical_frame

    # Initialize the number of stereo cameras
    num_stereo_cameras = 1
    left_cam_enabled = False
    right_cam_enabled = False

    if enable_left_camera.lower() == 'true':
        num_stereo_cameras += 1
        left_cam_enabled = True
    else:
        logger.info('Left camera is disabled')

    if enable_right_camera.lower() == 'true':
        num_stereo_cameras += 1
        right_cam_enabled = True
    else:
        logger.info('Right camera is disabled')

    camera_names = front_camera_name
    if left_cam_enabled:
        camera_names += ',' + left_camera_name

    if right_cam_enabled:
        camera_names += ',' + right_camera_name

    # Create the RTAB-MAP remappings
    rtabmap_rgbd_cameras = 0 if num_stereo_cameras > 1 else 1
    rtabmap_remappings = [('imu', rtabmap_imu_topic)]
    if num_stereo_cameras == 1:
        rtabmap_remappings.append(('rgbd_image', 'rgbd_image0'))

    # NOTE: If using only one camera the rtabmap odom and slam nodes will use the synced messages from that camera.
    # If using multiple cameras the rtabmap odom and slam nodes will use the synced messages from all cameras which are published as a
    # single rtabmap_msgs/RGBDImages message. To make the odom and slam nodes subscribe to this message we have to set
    # subscribe_rgbd=true and rgbd_cameras=0. Checkout http://wiki.ros.org/rtabmap_odom and http://wiki.ros.org/rtabmap_slam for more info

    # Define RTAB-Map node parameters
    rtabmap_vslam_hz = 25

    rtabmap_camera_sync_parameters = {
        'use_sim_time': True,
        'topic_queue_size': 10,
        'sync_queue_size': 2,
        'approx_sync_max_interval': 0.0,
        'approx_sync': True,
        'qos': 0,
        'qos_camera_info': 0,
    }  # This sets the synchronization parameters of the messages associated with one camera

    rtabmap_rgbd_sync_parameters = {
        'use_sim_time': True,
        'topic_queue_size': 10,
        'sync_queue_size': 2,
        'approx_sync_max_interval': 0.0,
        'approx_sync': True,
        'qos': 0,
    }  # This sets the synchronization parameters between the different cameras messages

    rtabmap_common_overwrites = {
        'use_sim_time': True,
        'frame_id': robot_base_link,
        'odom_frame_id': 'odom',
        'publish_tf': True,
        'initial_pose': '0 0 0 0 0 0',
        'subscribe_rgb': False,
        'subscribe_depth': False,
        'subscribe_rgbd': True,
        'imu_frame_id': rtabmap_imu_frame_id,
        'rgbd_cameras': rtabmap_rgbd_cameras,
        'Vis/EstimationType': '0',
        'Vis/GPU': 'False',
        'ORB/Gpu': 'False',
        'Vis/CorFlowGpu': 'False',
    }  # This sets the parameter overwrites which are common for the odom and slam nodes

    rtabmap_odom_param_overwrites = {
        'topic_queue_size': 10,
        'sync_queue_size': 2,
        'wait_imu_to_init': False,
        'publish_null_when_lost': True,
        'approx_sync_max_interval': 1.0,
        'approx_sync': False,
        'Vis/MaxFeatures': '1500',
        'g2o/Optimizer': 'true',
    }  # This sets the parameter overwrites which are specific for the odom node

    rtabmap_slam_param_overwrites = {
        'topic_queue_size': 10,
        'sync_queue_size': 50,
        'map_frame_id': 'map',
        'database_path': '',
        'tf_delay': 1.0 / rtabmap_vslam_hz,
        'approx_sync_max_interval': 0.5,
        'approx_sync': True,
        'Rtabmap/DetectionRate': '1.0',
        'RGBD/LocalRadius': '5.0',
        'Mem/BinDataKept': 'True',
        'RGBD/MaxLoopClosureDistance': '1.0',
        'VhEp/MatchCountMin': '8',
        'RGBD/OptimizeFromGraphEnd': 'false',
    }  # This sets the parameter overwrites which are specific for the slam node

    # Create a shared container to hold composable nodes for speed-ups through intra process communication
    shared_container = ComposableNodeContainer(
        name=shared_container_name,
        namespace='',
        package='rclcpp_components',
        executable='component_container_mt',
        output='screen',
        arguments=['--ros-args', '--log-level', log_level],
    )

    # Create the stereo synchronization nodes launch description
    right_cam_rgbd_image_enum = 'rgbd_image2' if left_cam_enabled else 'rgbd_image1'
    rtabmap_camera_sync_nodes = [
        ComposableNode(
            name='front_camera_sync_node',
            package='rtabmap_sync',
            plugin='rtabmap_sync::StereoSync',
            parameters=[rtabmap_camera_sync_parameters],
            remappings=[
                ('left/image_rect', get_realsense_camera_topics(front_camera_name).left_img_topic),
                ('right/image_rect', get_realsense_camera_topics(front_camera_name).right_img_topic),
                ('left/camera_info', get_realsense_camera_topics(front_camera_name).left_cam_info_topic),
                ('right/camera_info', get_realsense_camera_topics(front_camera_name).right_cam_info_topic),
                ('rgbd_image', 'rgbd_image0'),
                ('rgbd_image/compressed', 'rgbd_image0/compressed'),
            ],
        )
    ]

    if enable_left_camera.lower() == 'true':
        rtabmap_camera_sync_nodes.append(
            ComposableNode(
                name='left_camera_sync_node',
                package='rtabmap_sync',
                plugin='rtabmap_sync::StereoSync',
                parameters=[rtabmap_camera_sync_parameters],
                remappings=[
                    ('left/image_rect', get_realsense_camera_topics(left_camera_name).left_img_topic),
                    ('right/image_rect', get_realsense_camera_topics(left_camera_name).right_img_topic),
                    ('left/camera_info', get_realsense_camera_topics(left_camera_name).left_cam_info_topic),
                    ('right/camera_info', get_realsense_camera_topics(left_camera_name).right_cam_info_topic),
                    ('rgbd_image', 'rgbd_image1'),
                    ('rgbd_image/compressed', 'rgbd_image1/compressed'),
                ],
            )
        )

    if enable_right_camera.lower() == 'true':
        rtabmap_camera_sync_nodes.append(
            ComposableNode(
                name='right_camera_sync_node',
                package='rtabmap_sync',
                plugin='rtabmap_sync::StereoSync',
                parameters=[rtabmap_camera_sync_parameters],
                remappings=[
                    ('left/image_rect', get_realsense_camera_topics(right_camera_name).left_img_topic),
                    ('right/image_rect', get_realsense_camera_topics(right_camera_name).right_img_topic),
                    ('left/camera_info', get_realsense_camera_topics(right_camera_name).left_cam_info_topic),
                    ('right/camera_info', get_realsense_camera_topics(right_camera_name).right_cam_info_topic),
                    ('rgbd_image', right_cam_rgbd_image_enum),
                    ('rgbd_image/compressed', right_cam_rgbd_image_enum + '/compressed'),
                ],
            )
        )

    if num_stereo_cameras > 1:
        rtabmap_camera_sync_nodes.append(
            ComposableNode(
                name='rgbd_images_sync_node',
                package='rtabmap_sync',
                plugin='rtabmap_sync::RGBDXSync',
                parameters=[rtabmap_rgbd_sync_parameters, {'rgbd_cameras': num_stereo_cameras}],
            )
        )

    rtabmap_camera_sync_node = LoadComposableNodes(
        target_container=shared_container_name, composable_node_descriptions=rtabmap_camera_sync_nodes
    )

    # Create the RTAB-MAP stereo odometry launch description
    rtabmap_odom_parameters = {}
    odom_config_file_path = os.path.join(
        get_package_share_directory('aqita_launch'), 'config', 'rtab', 'rtab_odom.yaml'
    )
    if os.path.exists(odom_config_file_path):
        try:
            with open(odom_config_file_path, 'r') as f:
                loaded_params = yaml.safe_load(f)
                if loaded_params and 'visual_odometry' in loaded_params:
                    rtabmap_odom_parameters = loaded_params['visual_odometry']['ros__parameters']
                    logger.info(f"Loaded parameters from file: {odom_config_file_path}")
                else:
                    logger.warning(f"Config file does not contain visual_odometry parameters")
        except Exception as e:
            logger.error(f"Failed to load parameters from file: {e}")
    else:
        logger.error(f"Config file not found: {odom_config_file_path}")
        return []

    rtabmap_odom_node = LoadComposableNodes(
        target_container=shared_container_name,
        condition=UnlessCondition(use_isaac_ros_odometry),
        composable_node_descriptions=[
            ComposableNode(
                name='rtabmap_odom_node',
                package='rtabmap_odom',
                plugin='rtabmap_odom::StereoOdometry',
                parameters=[rtabmap_odom_parameters, rtabmap_common_overwrites, rtabmap_odom_param_overwrites],
                remappings=rtabmap_remappings,
            )
        ],
    )

    # Create the Isaac-ROS multicam stereo odometry launch description
    isaac_ros_multicam_odom_node = lu.include(
        'aqita_launch',
        'launch/slam/isaac_ros_vslam.launch.py',
        condition=IfCondition(use_isaac_ros_odometry),
        launch_arguments={
            'output_topics_prefix': 'multicam',
            'node_name': 'vslam_multicam_odom_node',
            'robot_base_link': robot_base_link,
            'vslam_map': 'map',
            'vslam_odom': 'odom',
            'camera_names': camera_names,
            'imu_frame': rtabmap_imu_frame_id,
            'imu_topic': rtabmap_imu_topic,
            'enable_imu_fusion': 'True',
            'publish_map_to_odom_tf': 'False',
            'publish_odom_to_base_tf': 'True',
            'attach_to_shared_component_container': 'True',
            'component_container_name': shared_container_name,
            'log_level': log_level,
        },
    )

    # Create the RTAB-MAP VSLAM launch description
    rtabmap_slam_parameters = {}
    slam_config_file_path = os.path.join(
        get_package_share_directory('aqita_launch'), 'config', 'rtab', 'rtab_core.yaml'
    )
    if os.path.exists(slam_config_file_path):
        try:
            with open(slam_config_file_path, 'r') as f:
                loaded_params = yaml.safe_load(f)
                if loaded_params and 'rtabmap' in loaded_params:
                    rtabmap_slam_parameters = loaded_params['rtabmap']['ros__parameters']
                    logger.info(f"Loaded parameters from file: {slam_config_file_path}")
                else:
                    logger.warning(f"Config file does not contain rtabmap parameters")
        except Exception as e:
            logger.error(f"Failed to load parameters from file: {e}")
    else:
        logger.error(f"Config file not found: {slam_config_file_path}")
        return []

    if use_isaac_ros_odometry.perform(context).lower() == "true":
        rtabmap_remappings.append(('odom', '/multicam_visual_slam/tracking/odometry'))
        logger.info('RTAB-MAP VSLAM node configured to use ISAAC-ROS multicam odometry')
    else:
        rtabmap_slam_param_overwrites.update({'subscribe_odom_info': True})
        logger.info('RTAB-MAP VSLAM node configured to use RTAB-MAP multicam odometry')

    rtabmap_vslam_node = LoadComposableNodes(
        target_container=shared_container_name,
        composable_node_descriptions=[
            ComposableNode(
                name='rtabmap_vslam_node',
                package='rtabmap_slam',
                plugin='rtabmap_slam::CoreWrapper',
                parameters=[rtabmap_slam_parameters, rtabmap_common_overwrites, rtabmap_slam_param_overwrites],
                remappings=rtabmap_remappings,
            )
        ],
    )

    # Create the base link odometry publisher node
    baselink_odometry_publisher_node = LoadComposableNodes(
        target_container=shared_container_name,
        composable_node_descriptions=[
            ComposableNode(
                name='baselink_odometry_publisher_node',
                package='aqita_topic_tools',
                plugin='aqita::ros2::topic_tools::BaselinkOdometryPublisher',
                parameters=[
                    {
                        'parent_frame': 'map',
                        'robot_base_link': robot_base_link,
                        'topic_name': global_odometry_topic,
                        'tf_rate': rtabmap_vslam_hz,
                    }
                ],
            )
        ],
    )

    # Create the navigation launch description
    navigation_launch = lu.include(
        'nav2_aqita_bringup',
        'tests/test_isaac_sim_navigation.launch.py',
        condition=IfCondition(run_navigation),
        launch_arguments={
            nvblox_launch_arguments.camera_names: camera_names,
            'robot_base_link': robot_base_link,
            nvblox_launch_arguments.global_odometry_topic: global_odometry_topic,
            'publish_map_to_odom_tf': 'False',
            'log_level': log_level,
            'launch_demo': 'True',
            'attach_to_shared_component_container': 'False',
            'component_container_name': 'nav_container',
        },
    )

    monitoring_launch = lu.include('aqita_monitor', 'launch/monitoring.launch.py')

    # Create the visualization launch description
    rtabmap_vis_node = Node(
        package='rtabmap_viz',
        executable='rtabmap_viz',
        output='screen',
        parameters=[rtabmap_common_overwrites, rtabmap_slam_param_overwrites],
        remappings=rtabmap_remappings,
    )

    # Compose the launch actions
    launch_actions.append(TimerAction(period=3.0, actions=[shared_container]))
    launch_actions.append(TimerAction(period=5.0, actions=[rtabmap_camera_sync_node]))
    launch_actions.append(TimerAction(period=8.0, actions=[rtabmap_odom_node]))
    launch_actions.append(TimerAction(period=8.0, actions=[isaac_ros_multicam_odom_node]))
    launch_actions.append(TimerAction(period=11.0, actions=[rtabmap_vslam_node]))
    launch_actions.append(TimerAction(period=15.0, actions=[baselink_odometry_publisher_node]))
    launch_actions.append(TimerAction(period=17.0, actions=[rtabmap_vis_node]))
    launch_actions.append(TimerAction(period=20.0, actions=[navigation_launch]))
    launch_actions.append(TimerAction(period=20.0, actions=[monitoring_launch]))

    logger.info('Starting nodes...')
    return launch_actions


def generate_launch_description():

    # Declare the launch arguments
    launch_args = [
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['enable_left_cam'], default_value='True', description='Whether to enable the left camera'
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['enable_right_cam'],
            default_value='True',
            description='Whether to enable the right camera',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['run_navigation'],
            default_value='False',
            description='Whether to start the navigation nodes',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['use_isaac_ros_odometry'],
            default_value='False',
            description='Whether to use the Isaac-ROS VSLAM odometry instead of the default RTAB-Map odometry',
        ),
    ]

    return LaunchDescription(launch_args + [OpaqueFunction(function=launch_function)])
