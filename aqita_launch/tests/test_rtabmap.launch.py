# TODO: License description

# NOTE: Launch file for testing and experimenting with the RTAB-Map VSLAM algorithm

# Import launch tools
import os, sys, subprocess, yaml
import isaac_ros_launch_utils as lu
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription, LaunchContext, logging
from launch.actions import DeclareLaunchArgument, OpaqueFunction, TimerAction
from launch.substitutions import LaunchConfiguration, Command
from launch.conditions import IfCondition, UnlessCondition
from launch_ros.actions import Node, LoadComposableN<PERSON>, ComposableNodeContainer
from launch_ros.descriptions import ParameterValue, ComposableNode

# Import tools from the aqita initialization module
from aqita_init.topics import get_realsense_camera_topics

# Launch parameter names
LAUNCH_PARAMETERS = {
    'front_cam_model': 'front_camera_model',
    'left_cam_model': 'left_camera_model',
    'right_cam_model': 'right_camera_model',
    'front_cam_serial_no': 'front_camera_serial_no',
    'left_cam_serial_no': 'left_camera_serial_no',
    'right_cam_serial_no': 'right_camera_serial_no',
    'enable_left_cam': 'enable_left_camera',
    'enable_right_cam': 'enable_right_camera',
    'use_composition_for_cam_nodes': 'use_composition_for_cam_nodes',
    'use_intra_process_comms_for_cam_nodes': 'use_intra_process_comms_for_cam_nodes',
    'use_rgbd_odometry': 'use_rgbd_odometry',
    'use_isaac_ros_odometry': 'use_isaac_ros_odometry'
}

# IMU options topic names
IMU_OPTIONS = {'FRONT': '/front/imu', 'LEFT': '/left/imu', 'RIGHT': '/right/imu'}

# Odom strategy options
ODOM_STRATEGY = {
    'F2M': '0',
    'F2F': '1',
    'Fovis': '2',
    'viso2': '3',
    'DVO-SLAM': '4',
    'ORB_SLAM2': '5',
    'OKVIS': '6',
    'LOAM': '7',
    'MSCKF_VIO': '8',
    'VINS-Fusion': '9',
    'OpenVINS': '10',
    'FLOAM': '11',
    'Open3D': '12',
    'Default': '0'
}

# Feature type options
FEATURE_TYPE = {
    'SURF': '0',
    'SIFT': '1',
    'ORB': '2',
    'FAST/FREAK': '3',
    'FAST/BRIEF': '4',
    'GFTT/FREAK': '5',
    'GFTT/BRIEF': '6',
    'BRISK': '7',
    'GFTT/ORB': '8',
    'KAZE': '9',
    'ORB-OCTREE': '10',
    'SuperPoint': '11',
    'SURF/FREAK': '12',
    'GFTT/DAISY': '13',
    'SURF/DAISY': '14',
    'PyDetector': '15',
    'Default': '6'
}

def launch_function(context: LaunchContext, *args, **kwargs):

    logger = logging.get_logger('launch')
    logger.info('Starting RTAB-Map testing pipeline...')
    package_dir = get_package_share_directory('aqita_launch')
    launch_actions = []

    # Create the launch configuration substitutions
    front_camera_model = LaunchConfiguration(LAUNCH_PARAMETERS['front_cam_model']).perform(context)
    left_camera_model = LaunchConfiguration(LAUNCH_PARAMETERS['left_cam_model']).perform(context)
    right_camera_model = LaunchConfiguration(LAUNCH_PARAMETERS['right_cam_model']).perform(context)
    front_camera_serial_no = LaunchConfiguration(LAUNCH_PARAMETERS['front_cam_serial_no']).perform(context)
    left_camera_serial_no = LaunchConfiguration(LAUNCH_PARAMETERS['left_cam_serial_no']).perform(context)
    right_camera_serial_no = LaunchConfiguration(LAUNCH_PARAMETERS['right_cam_serial_no']).perform(context)
    enable_left_camera = LaunchConfiguration(LAUNCH_PARAMETERS['enable_left_cam'])
    enable_right_camera = LaunchConfiguration(LAUNCH_PARAMETERS['enable_right_cam'])
    use_composition_for_cam_nodes = LaunchConfiguration(LAUNCH_PARAMETERS['use_composition_for_cam_nodes']).perform(context)
    use_intra_process_comms_for_cam_nodes = LaunchConfiguration(LAUNCH_PARAMETERS['use_intra_process_comms_for_cam_nodes']).perform(context)
    use_rgbd_odometry = LaunchConfiguration(LAUNCH_PARAMETERS['use_rgbd_odometry']).perform(context)
    use_isaac_ros_odometry = LaunchConfiguration(LAUNCH_PARAMETERS['use_isaac_ros_odometry'])

    logger.info(f'Front camera model set to {front_camera_model}')
    logger.info(f'Left camera model set to {left_camera_model}')
    logger.info(f'Right camera model set to {right_camera_model}')

    # Define the names of the system components
    shared_container_name = 'rtabmap_shared_container'
    log_level = 'info'
    robot_base_link = 'aqita'
    front_camera_name = 'cam_front'
    left_camera_name = 'cam_left'
    right_camera_name = 'cam_right'
    rtabmap_imu_topic = IMU_OPTIONS['FRONT']

    # Initialize the odometry type related parameters
    if use_rgbd_odometry.lower() == 'true' and use_isaac_ros_odometry.perform(context).lower() == 'false':
        # We are using RGBD odometry
        cam_emitter_enabled = 'true'
        enable_cam_depth_stream = 'true'
        enable_cam_color_stream = 'true'
        enable_cam_infra_stream = 'false'
        rtabmap_sync_type = 'RGBDSync'
        rtabmap_odom_type = 'RGBDOdometry'
        front_camera_sync_remappings = [
            ('rgb/image',       get_realsense_camera_topics(front_camera_name).rgb_img_topic),
            ('depth/image',     get_realsense_camera_topics(front_camera_name).depth_img_topic),
            ('rgb/camera_info', get_realsense_camera_topics(front_camera_name).rgb_cam_info_topic)]
        left_camera_sync_remappings = [
            ('rgb/image',       get_realsense_camera_topics(left_camera_name).rgb_img_topic),
            ('depth/image',     get_realsense_camera_topics(left_camera_name).depth_img_topic),
            ('rgb/camera_info', get_realsense_camera_topics(left_camera_name).rgb_cam_info_topic)]  
        right_camera_sync_remappings = [
            ('rgb/image',       get_realsense_camera_topics(right_camera_name).rgb_img_topic),
            ('depth/image',     get_realsense_camera_topics(right_camera_name).depth_img_topic),
            ('rgb/camera_info', get_realsense_camera_topics(right_camera_name).rgb_cam_info_topic)]
        
        logger.info('Using RGBD odometry')
    else:
        # We are using stereo odometry
        cam_emitter_enabled = 'false'
        enable_cam_depth_stream = 'false'
        enable_cam_color_stream = 'false'
        enable_cam_infra_stream = 'true'
        rtabmap_sync_type = 'StereoSync'
        rtabmap_odom_type = 'StereoOdometry'
        front_camera_sync_remappings = [
            ('left/image_rect',     get_realsense_camera_topics(front_camera_name).left_img_topic),
            ('right/image_rect',    get_realsense_camera_topics(front_camera_name).right_img_topic),
            ('left/camera_info',    get_realsense_camera_topics(front_camera_name).left_cam_info_topic),
            ('right/camera_info',   get_realsense_camera_topics(front_camera_name).right_cam_info_topic)]
        left_camera_sync_remappings = [
            ('left/image_rect',     get_realsense_camera_topics(left_camera_name).left_img_topic),
            ('right/image_rect',    get_realsense_camera_topics(left_camera_name).right_img_topic),
            ('left/camera_info',    get_realsense_camera_topics(left_camera_name).left_cam_info_topic),
            ('right/camera_info',   get_realsense_camera_topics(left_camera_name).right_cam_info_topic)]
        right_camera_sync_remappings = [
            ('left/image_rect',     get_realsense_camera_topics(right_camera_name).left_img_topic),
            ('right/image_rect',    get_realsense_camera_topics(right_camera_name).right_img_topic),
            ('left/camera_info',    get_realsense_camera_topics(right_camera_name).left_cam_info_topic),
            ('right/camera_info',   get_realsense_camera_topics(right_camera_name).right_cam_info_topic)]
        
        if use_rgbd_odometry.lower() == 'true':
            logger.warning('Odometry type overwritten to stereo since Isaac-ROS odometry option is requested!')
        else:
            logger.info('Using stereo odometry')

    # Initialize the number of stereo cameras
    num_stereo_cameras = 1
    left_cam_enabled = False
    right_cam_enabled = False

    if enable_left_camera.perform(context).lower() == 'true':
        num_stereo_cameras += 1
        left_cam_enabled = True
    else:
        logger.info('Left camera is disabled')

    if enable_right_camera.perform(context).lower() == 'true':
        num_stereo_cameras += 1
        right_cam_enabled = True
    else:
        logger.info('Right camera is disabled')

    # Initialize the depth module stream profile
    if enable_cam_depth_stream.lower() == 'true':
        if num_stereo_cameras > 2:
            depth_module_profile = '848x480x30'
        else:
            depth_module_profile = '848x480x60'
    else:
        depth_module_profile = '848x480x60'

    logger.info(f'Depth module stream profile set to {depth_module_profile}')

    # Initialize the imu frame id modifier node
    # NOTE: The RealSense cameras publish the imu messages with frame_id = camera_imu_optical_frame. Since this frame does not exist
    # we have to republish the imu messages with correct frame id which is the gyro optical frame associated with the respective camera.
    # This is to be done only for the imu which is actually used by the algorithms
    if rtabmap_imu_topic == IMU_OPTIONS['FRONT']:
        imu_original_topic = front_camera_name+'/imu'
        rtabmap_imu_frame_id = front_camera_name+'_gyro_optical_frame'
    elif rtabmap_imu_topic == IMU_OPTIONS['LEFT']:
        imu_original_topic = left_camera_name+'/imu'
        rtabmap_imu_frame_id = left_camera_name+'_gyro_optical_frame'
    else:
        imu_original_topic = right_camera_name+'/imu'
        rtabmap_imu_frame_id = right_camera_name+'_gyro_optical_frame'

    if imu_original_topic == rtabmap_imu_topic:
        logger.error('Error initializing the IMU frame id modifier node. The original and new topic names must be different. Aborting launch!')
        sys.exit()

    # Create the RTAB-MAP remappings
    # NOTE: If using only one camera the rtabmap odom and slam nodes will use the synced messages from that camera.
    # If using multiple cameras the rtabmap odom and slam nodes will use the synced messages from all cameras which are published as a
    # single rtabmap_msgs/RGBDImages message. To make the odom and slam nodes subscribe to this message we have to set
    # subscribe_rgbd=true and rgbd_cameras=0. Checkout http://wiki.ros.org/rtabmap_odom and http://wiki.ros.org/rtabmap_slam for more info
    rtabmap_rgbd_cameras = 0 if num_stereo_cameras > 1 else 1
    rtabmap_remappings = [('imu', rtabmap_imu_topic)]
    if num_stereo_cameras == 1:
        rtabmap_remappings.append(('rgbd_image', 'rgbd_image0'))

    # Define RTAB-Map node parameters
    rtabmap_vslam_hz = 25
    rtabmap_database_path = './data/rtabmap.db'
    rgb_module_profile = depth_module_profile

    rtabmap_camera_sync_parameters = {
        'topic_queue_size': 10,
        'sync_queue_size': 2,
        'approx_sync_max_interval': 0.0,
        'approx_sync': False,
        'qos': 0,
        'qos_camera_info': 0,
    } # This sets the synchronization parameters of the messages associated with one camera

    rtabmap_rgbd_sync_parameters = {
        'topic_queue_size': 10,
        'sync_queue_size': 2,
        'approx_sync_max_interval': 0.0,
        'approx_sync': True,
        'qos': 0
    } # This sets the synchronization parameters between the different cameras messages

    rtabmap_common_overwrites = {
        'frame_id': robot_base_link,
        'odom_frame_id': 'odom',
        'publish_tf': True,
        'initial_pose': '0 0 0 0 0 0',
        'subscribe_rgb': False,
        'subscribe_depth': False,
        'subscribe_rgbd': True,
        'imu_frame_id': rtabmap_imu_frame_id,
        'rgbd_cameras': rtabmap_rgbd_cameras,
        'Vis/EstimationType': '0',      # Motion estimation approach: 0:3D->3D, 1:3D->2D (PnP), 2:2D->2D (Epipolar Geometry)
        #'Vis/InlierDistance': '0.1',    # for Vis/EstimationType = 0
        #'Vis/RefineIterations': '5',    # for Vis/EstimationType = 0
        #'Vis/FeatureType': FEATURE_TYPE['Default'],
        'ORB/Gpu': 'False',
        'Vis/CorFlowGpu': 'False'
        #'FAST/Gpu': 'true',
        #'GFTT/Gpu': 'true',
        #'SIFT/Gpu': 'true',
        #'SURF/GpuVersion': 'true',
        #'Stereo/Gpu': 'false',
        #'Vis/CorFlowGpu': 'false',
        #'Optimizer/Strategy': '2',      # Graph optimization strategy: 0=TORO, 1=g2o, 2=GTSAM and 3=Ceres
        #'Optimizer/Robust': 'false',
        #'g2o/Optimizer': '0',           # 0=Levenberg 1=GaussNewton
        #'g2o/Solver': '0',              # 0=csparse 1=pcg 2=cholmod 3=Eigen
        #'Vis/CorType': '0'              # Correspondences computation approach: 0=Features Matching, 1=Optical Flow
    } # This sets the parameter overwrites which are common for the odom and slam nodes

    rtabmap_odom_param_overwrites = {
        'topic_queue_size': 10,
        'sync_queue_size': 2,
        'wait_imu_to_init': False,
        'publish_null_when_lost': True,
        'approx_sync_max_interval': 0.0,
        'approx_sync': False,
        'Odom/Strategy': ODOM_STRATEGY['Default'],
        'Odom/ResetCountdown': '0',
        'Vis/MaxFeatures': '1500',      # Default = 1000
        'Vis/GridCols': '1',
        'Vis/GridRows': '1',
    } # This sets the parameter overwrites which are specific for the odom node

    rtabmap_slam_param_overwrites = {
        'topic_queue_size': 10,
        'sync_queue_size': 50,
        'map_frame_id': 'map',
        'database_path': rtabmap_database_path,
        'tf_delay': 1.0 / rtabmap_vslam_hz,
        'approx_sync_max_interval': 0.07,
        'approx_sync': True,
        'Rtabmap/DetectionRate': '1.0',
        'RGBD/LocalRadius': '5.0',
        'Mem/BinDataKept': 'True',
        'RGBD/MaxLoopClosureDistance': '1.0',
        'VhEp/MatchCountMin': '8',
        'RGBD/OptimizeFromGraphEnd': 'false',
        'Rtabmap/CreateIntermediateNodes': 'false', # Only used when Rtabmap/DetectionRate>0
    } # This sets the parameter overwrites which are specific for the slam node

    # Check if the cameras are connected
    logger.info('Checking camera connections...')
    front_camera_detected = False
    left_camera_detected = False
    right_camera_detected = False

    while True:
        rs_connected_devices = subprocess.run(['rs-fw-update', '-l'], stdout=subprocess.PIPE, universal_newlines=True).stdout.split('\n')
        front_camera_info = [line for line in rs_connected_devices if line.find(front_camera_serial_no) > -1]
        left_camera_info = [line for line in rs_connected_devices if line.find(left_camera_serial_no) > -1]
        right_camera_info = [line for line in rs_connected_devices if line.find(right_camera_serial_no) > -1]

        if len(front_camera_info) > 0:
            logger.info('Front camera detected')
            logger.info(f'\t{front_camera_info[0]}')
            front_camera_detected = True
        else:
            logger.error(f'Front camera with serial no. {front_camera_serial_no} not detected')

        if left_cam_enabled:
            if len(left_camera_info) > 0:
                logger.info('Left camera detected')
                logger.info(f'\t{left_camera_info[0]}')
                left_camera_detected = True
            else:
                logger.error(f'Left camera with serial no. {left_camera_serial_no} not detected')

        if right_cam_enabled:
            if len(right_camera_info) > 0:
                logger.info('Right camera detected')
                logger.info(f'\t{right_camera_info[0]}')
                right_camera_detected = True
            else:
                logger.error(f'Right camera with serial no. {right_camera_serial_no} not detected')

        if (
            front_camera_detected
            and (left_camera_detected or not left_cam_enabled)
            and (right_camera_detected or not right_cam_enabled)
        ):
            break
        else:
            logger.error('Error detecting the required camera devices. Aborting launch!')
            sys.exit()

    # Initialize the camera names
    camera_names = front_camera_name
    if left_cam_enabled:
        camera_names += (','+left_camera_name)

    if right_cam_enabled:
        camera_names += (','+right_camera_name)

    # Create a shared container to hold composable nodes for speed-ups through intra process communication
    shared_container = ComposableNodeContainer(
        name=shared_container_name,
        namespace='',
        package='rclcpp_components',
        executable='component_container_mt',
        output='screen',
        arguments=['--ros-args', '--log-level', log_level],
    )

    # Create the robot state publisher node
    aqita_rsp_multicam_node = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='aqita_rsp_multicam',
        output='screen',
        parameters=[
            {
                'robot_description': ParameterValue(
                    Command(
                        [
                            'xacro ',
                            os.path.join(package_dir, 'urdf', 'xoss3-rs.urdf.xacro'),
                            ' ',
                            'robot_base_link:=',
                            robot_base_link,
                            ' ',
                            'front_camera_name:=',
                            front_camera_name,
                            ' ',
                            'left_camera_name:=',
                            left_camera_name,
                            ' ',
                            'right_camera_name:=',
                            right_camera_name,
                        ]
                    ),
                    value_type=str,
                )
            }
        ],
    )

    # Initialize the camera nodes composition-related parameters
    if use_composition_for_cam_nodes.lower() == 'true':
        front_cam_container_name = shared_container_name
        left_cam_container_name = shared_container_name
        right_cam_container_name = shared_container_name
    else:
        front_cam_container_name = 'front_cam_node_container'
        left_cam_container_name = 'left_cam_node_container'
        right_cam_container_name = 'right_cam_node_container'

        if use_intra_process_comms_for_cam_nodes.lower() == 'true':
            use_intra_process_comms_for_cam_nodes = 'False'
            logger.info('Intraprocess communication is not available without using composition for the camera nodes and will be disabled')

    # Create the front camera launch description
    front_camera_launch = lu.include(
        'aqita_launch', 'launch/sensors/'+front_camera_model+'.launch.py',
        launch_arguments={
            'camera_name': front_camera_name,
            'camera_serial_no': front_camera_serial_no,
            'publish_tf': 'False',
            'enable_depth': enable_cam_depth_stream,
            'enable_color': enable_cam_color_stream,
            'enable_infra1': enable_cam_infra_stream,
            'enable_infra2': enable_cam_infra_stream,
            'enable_emitter': cam_emitter_enabled,
            'depth_module_profile': depth_module_profile,
            'rgb_camera_profile': rgb_module_profile,
            'use_intra_process_comms': use_intra_process_comms_for_cam_nodes,
            'attach_to_shared_component_container': use_composition_for_cam_nodes,
            'component_container_name': front_cam_container_name
        },
    )

    # Create the left camera launch description
    left_camera_launch = lu.include(
        'aqita_launch', 'launch/sensors/'+left_camera_model+'.launch.py',
        condition=IfCondition(enable_left_camera),
        launch_arguments={
            'camera_name': left_camera_name,
            'camera_serial_no': left_camera_serial_no,
            'publish_tf': 'False',
            'enable_depth': enable_cam_depth_stream,
            'enable_color': enable_cam_color_stream,
            'enable_infra1': enable_cam_infra_stream,
            'enable_infra2': enable_cam_infra_stream,
            'enable_emitter': cam_emitter_enabled,
            'depth_module_profile': depth_module_profile,
            'rgb_camera_profile': rgb_module_profile,
            'use_intra_process_comms': use_intra_process_comms_for_cam_nodes,
            'attach_to_shared_component_container': use_composition_for_cam_nodes,
            'component_container_name': left_cam_container_name
        }
    )

    # Create the right camera launch description
    right_camera_launch = lu.include(
        'aqita_launch', 'launch/sensors/'+right_camera_model+'.launch.py',
        condition=IfCondition(enable_right_camera),
        launch_arguments={
            'camera_name': right_camera_name,
            'camera_serial_no': right_camera_serial_no,
            'publish_tf': 'False',
            'enable_depth': enable_cam_depth_stream,
            'enable_color': enable_cam_color_stream,
            'enable_infra1': enable_cam_infra_stream,
            'enable_infra2': enable_cam_infra_stream,
            'enable_emitter': cam_emitter_enabled,
            'depth_module_profile': depth_module_profile,
            'rgb_camera_profile': rgb_module_profile,
            'use_intra_process_comms': use_intra_process_comms_for_cam_nodes,
            'attach_to_shared_component_container': use_composition_for_cam_nodes,
            'component_container_name': right_cam_container_name
        }
    )

    # Create the imu frame id modifier node launch description
    imu_frame_id_modifier_node = LoadComposableNodes(
        target_container=shared_container_name,
        composable_node_descriptions = [
            ComposableNode(
                name='cam_imu_data_modifier',
                package='aqita_topic_tools',
                plugin='aqita::ros2::topic_tools::ImuFrameIdModifier',
                parameters=[{
                    'original_topic': imu_original_topic,
                    'new_topic': rtabmap_imu_topic,
                    'target_frame_id': rtabmap_imu_frame_id}]
            )
        ]
    )

    # Create the stereo synchronization nodes launch description
    right_cam_rgbd_image_enum = 'rgbd_image2' if left_cam_enabled else 'rgbd_image1'
    front_camera_sync_remappings.extend([('rgbd_image', 'rgbd_image0'),
                                         ('rgbd_image/compressed', 'rgbd_image0/compressed')])
    rtabmap_camera_sync_nodes = [
        ComposableNode(
            name='front_camera_sync_node',
            package='rtabmap_sync',
            plugin='rtabmap_sync::'+rtabmap_sync_type,
            parameters=[rtabmap_camera_sync_parameters],
            remappings=front_camera_sync_remappings
        )
    ]

    if enable_left_camera.perform(context).lower() == 'true':
        left_camera_sync_remappings.extend([('rgbd_image', 'rgbd_image1'),
                                            ('rgbd_image/compressed', 'rgbd_image1/compressed')])
        rtabmap_camera_sync_nodes.append(
            ComposableNode(
                name='left_camera_sync_node',
                package='rtabmap_sync',
                plugin='rtabmap_sync::'+rtabmap_sync_type,
                parameters=[rtabmap_camera_sync_parameters],
                remappings=left_camera_sync_remappings
            )
        )

    if enable_right_camera.perform(context).lower() == 'true':
        right_camera_sync_remappings.extend([('rgbd_image', right_cam_rgbd_image_enum),
                                             ('rgbd_image/compressed', right_cam_rgbd_image_enum + '/compressed')])
        rtabmap_camera_sync_nodes.append(
            ComposableNode(
                name='right_camera_sync_node',
                package='rtabmap_sync',
                plugin='rtabmap_sync::'+rtabmap_sync_type,
                parameters=[rtabmap_camera_sync_parameters],
                remappings=right_camera_sync_remappings
            )
        )

    # For a multi-camera setup we have to synchronize the different camera streams
    if num_stereo_cameras > 1:
        rtabmap_camera_sync_nodes.append(
            ComposableNode(
                name='rgbd_images_sync_node',
                package='rtabmap_sync',
                plugin='rtabmap_sync::RGBDXSync',
                parameters=[rtabmap_rgbd_sync_parameters,
                            {'rgbd_cameras': num_stereo_cameras}],
            )
        )

    rtabmap_camera_sync_node = LoadComposableNodes(
        target_container=shared_container_name,
        composable_node_descriptions=rtabmap_camera_sync_nodes
    )

    # Create the RTAB-MAP stereo odometry launch description
    rtabmap_odom_parameters = {}
    odom_config_file_path = os.path.join(get_package_share_directory('aqita_launch'), 'config', 'rtab', 'rtab_odom.yaml')
    if os.path.exists(odom_config_file_path):
        try:
            with open(odom_config_file_path, 'r') as f:
                loaded_params = yaml.safe_load(f)
                if loaded_params and 'visual_odometry' in loaded_params:
                    rtabmap_odom_parameters = loaded_params['visual_odometry']['ros__parameters']
                    logger.info(f"Loaded parameters from file: {odom_config_file_path}")
                else:
                    logger.warning(f"Config file does not contain visual_odometry parameters")
        except Exception as e:
            logger.error(f"Failed to load parameters from file: {e}")
    else:
        logger.error(f"Config file not found: {odom_config_file_path}")
        sys.exit()

    rtabmap_odom_node = LoadComposableNodes(
        target_container=shared_container_name,
        condition=UnlessCondition(use_isaac_ros_odometry),
        composable_node_descriptions=[
            ComposableNode(
                name='rtabmap_odom_node',
                package='rtabmap_odom',
                plugin='rtabmap_odom::'+rtabmap_odom_type,
                parameters=[rtabmap_odom_parameters,
                            rtabmap_common_overwrites,
                            rtabmap_odom_param_overwrites],
                remappings=rtabmap_remappings
            )
        ]
    )

    # Create the Isaac-ROS multicam stereo odometry launch description
    isaac_ros_multicam_odom_node = lu.include(
        'aqita_launch', 'launch/slam/isaac_ros_vslam.launch.py',
        condition=IfCondition(use_isaac_ros_odometry),
        launch_arguments={
            'output_topics_prefix': 'multicam',
            'node_name': 'vslam_multicam_odom_node',
            'robot_base_link': robot_base_link,
            'vslam_map': 'map',
            'vslam_odom': 'odom',
            'camera_names': camera_names,
            'imu_frame': rtabmap_imu_frame_id,
            'imu_topic': rtabmap_imu_topic,
            'enable_imu_fusion': 'True',
            'publish_map_to_odom_tf': 'False',
            'publish_odom_to_base_tf': 'True',
            'attach_to_shared_component_container': 'True',
            'component_container_name': shared_container_name,
            'log_level': log_level
        }
    )

    # Create the RTAB-MAP VSLAM launch description
    rtabmap_slam_parameters = {}
    slam_config_file_path = os.path.join(get_package_share_directory('aqita_launch'), 'config', 'rtab', 'rtab_core.yaml')
    if os.path.exists(slam_config_file_path):
        try:
            with open(slam_config_file_path, 'r') as f:
                loaded_params = yaml.safe_load(f)
                if loaded_params and 'rtabmap' in loaded_params:
                    rtabmap_slam_parameters = loaded_params['rtabmap']['ros__parameters']
                    logger.info(f"Loaded parameters from file: {slam_config_file_path}")
                else:
                    logger.warning(f"Config file does not contain rtabmap parameters")
        except Exception as e:
            logger.error(f"Failed to load parameters from file: {e}")
    else:
        logger.error(f"Config file not found: {slam_config_file_path}")
        sys.exit()

    if use_isaac_ros_odometry.perform(context).lower() == 'true':
        rtabmap_remappings.append(('odom', '/multicam_visual_slam/tracking/odometry'))
        logger.info('RTAB-MAP VSLAM node configured to use ISAAC-ROS multicam odometry')
    else:
        rtabmap_slam_param_overwrites.update({'subscribe_odom_info': True})
        logger.info('RTAB-MAP VSLAM node configured to use RTAB-MAP multicam odometry')

    rtabmap_vslam_node = LoadComposableNodes(
        target_container=shared_container_name,
        composable_node_descriptions=[
            ComposableNode(
                name='rtabmap_vslam_node',
                package='rtabmap_slam',
                plugin='rtabmap_slam::CoreWrapper',
                parameters=[rtabmap_slam_parameters,
                            rtabmap_common_overwrites,
                            rtabmap_slam_param_overwrites],
                remappings=rtabmap_remappings
            )
        ]
    )

    # Create the visualization launch description
    rtabmap_vis_node = Node(
        package='rtabmap_viz',
        executable='rtabmap_viz',
        output='screen',
        parameters=[rtabmap_common_overwrites,
                    rtabmap_slam_param_overwrites],
        remappings=rtabmap_remappings
    )

    # Compose the launch actions
    launch_actions.append(aqita_rsp_multicam_node)
    launch_actions.append(TimerAction(period=3.0, actions=[shared_container]))
    launch_actions.append(TimerAction(period=5.0, actions=[right_camera_launch]))
    launch_actions.append(TimerAction(period=15.0, actions=[front_camera_launch]))
    launch_actions.append(TimerAction(period=25.0, actions=[left_camera_launch]))
    launch_actions.append(TimerAction(period=30.0, actions=[imu_frame_id_modifier_node]))
    launch_actions.append(TimerAction(period=35.0, actions=[rtabmap_camera_sync_node]))
    launch_actions.append(TimerAction(period=40.0, actions=[isaac_ros_multicam_odom_node]))
    launch_actions.append(TimerAction(period=40.0, actions=[rtabmap_odom_node]))
    launch_actions.append(TimerAction(period=50.0, actions=[rtabmap_vslam_node]))
    launch_actions.append(TimerAction(period=55.0, actions=[rtabmap_vis_node]))

    logging.get_logger('launch').info('Starting nodes...')
    return launch_actions


def generate_launch_description():

    # Declare the launch arguments
    launch_args = [
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['front_cam_model'],
            default_value='rsd455',
            description='Model of the onboard front camera',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['left_cam_model'],
            default_value='rsd455',
            description='Model of the onboard left camera'
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['right_cam_model'],
            default_value='rsd455',
            description='Model of the onboard right camera',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['front_cam_serial_no'],
            default_value='231122301680',
            description='Serial number of the onboard front camera (if applicable for camera id)',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['left_cam_serial_no'],
            default_value='231122301961',
            description='Serial number of the onboard left camera (if applicable for camera id)',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['right_cam_serial_no'],
            default_value='231122300977',
            description='Serial number of the onboard right camera (if applicable for camera id)',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['enable_left_cam'],
            default_value='True',
            description='Whether to enable the left camera'
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['enable_right_cam'],
            default_value='True',
            description='Whether to enable the right camera',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['use_composition_for_cam_nodes'],
            default_value='True',
            description='Whether to start the camera nodes in a shared nodes container for possible speed-ups',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['use_intra_process_comms_for_cam_nodes'],
            default_value='False',
            description='Whether to enable intraprocess communication for the camera nodes (requires composition)',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['use_rgbd_odometry'],
            default_value='False',
            description='Whether to start RTAB-Map in RGBD mode'
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['use_isaac_ros_odometry'],
            default_value='False',
            description='Whether to use the Isaac-ROS VSLAM odometry instead of the default RTAB-Map odometry'
        )
    ]

    return LaunchDescription(launch_args + [OpaqueFunction(function=launch_function)])
