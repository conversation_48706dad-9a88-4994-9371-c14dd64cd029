#!/usr/bin/env python3
"""Transform publishers for the RTAB-Map system."""

import os
from launch import LaunchDescription, logging
from launch.actions import DeclareLaunchArgument, OpaqueFunction
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node


def launch_setup(context):
    use_sim_time = LaunchConfiguration('use_sim_time').perform(context)
    robot_base_link = LaunchConfiguration('robot_base_link').perform(context)

    use_sim_time_param = use_sim_time.lower() == 'true'

    logger = logging.get_logger('launch')
    logger.info(f'Robot base link: {robot_base_link}')
    logger.info(f'Use sim time: {use_sim_time_param}')

    if not robot_base_link:
        logger.warn('Robot base link is empty, using default "base_link"')
        robot_base_link = 'base_link'

    map_to_base_publisher = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='map_to_base_publisher',
        arguments=['0', '0', '0', '0', '0', '0', 'map', robot_base_link],
        parameters=[{'use_sim_time': use_sim_time_param}],
    )

    return [map_to_base_publisher]


def generate_launch_description():
    launch_args = [
        DeclareLaunchArgument('use_sim_time', default_value='false', description='Use simulation time'),
        DeclareLaunchArgument('robot_base_link', default_value='aqita', description='Name of the robot base link'),
    ]

    ld = LaunchDescription(launch_args)
    ld.add_action(OpaqueFunction(function=launch_setup))

    return ld
