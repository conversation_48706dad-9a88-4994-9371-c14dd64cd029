#!/usr/bin/env python3
import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription, logging
from launch.actions import DeclareLaunchArgument, OpaqueFunction
from launch.substitutions import LaunchConfiguration, Command
from launch_ros.actions import Node
from launch_ros.parameter_descriptions import ParameterValue


def launch_setup(context):
    use_sim_time = LaunchConfiguration('use_sim_time').perform(context)
    robot_base_link = LaunchConfiguration('robot_base_link').perform(context)
    front_camera_name = LaunchConfiguration('front_camera_name').perform(context)

    use_sim_time_param = use_sim_time.lower() == 'true'

    logger = logging.get_logger('launch')
    logger.info(f'Robot base link: {robot_base_link}')
    logger.info(f'Front camera name: {front_camera_name}')
    logger.info(f'Use sim time: {use_sim_time_param}')

    package_dir = get_package_share_directory('aqita_launch')

    aqita_rsp_node = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='aqita_rsp',
        output='screen',
        parameters=[
            {
                'robot_description': ParameterValue(
                    Command(
                        [
                            'xacro ',
                            os.path.join(package_dir, 'urdf', 'xoss3-rs.urdf.xacro'),
                            ' ',
                            'robot_base_link:=',
                            robot_base_link,
                            ' ',
                            'front_camera_name:=',
                            front_camera_name,
                            ' ',
                            'left_camera_name:=left_D455_cam',
                            ' ',
                            'right_camera_name:=right_D455_cam',
                        ]
                    ),
                    value_type=str,
                ),
                'use_sim_time': use_sim_time_param,
            }
        ],
    )

    return [aqita_rsp_node]


def generate_launch_description():
    launch_args = [
        DeclareLaunchArgument('use_sim_time', default_value='false', description='Use simulation time'),
        DeclareLaunchArgument('robot_base_link', default_value='aqita', description='Name of the robot base link'),
        DeclareLaunchArgument('front_camera_name', default_value='cam_front', description='Name of the front camera'),
    ]

    ld = LaunchDescription(launch_args)
    ld.add_action(OpaqueFunction(function=launch_setup))

    return ld
