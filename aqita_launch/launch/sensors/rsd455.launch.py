# TODO: Licence description

# NOTE: Launch file for starting a Realsense D455 camera node

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription, logging
from launch.actions import OpaqueFunction, DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import LoadComposableNodes, Node
from launch_ros.descriptions import ComposableNode
from launch.conditions import UnlessCondition

LAUNCH_PARAMETERS = {
    'cam_name': 'camera_name',
    'cam_serial_no': 'camera_serial_no',
    'publish_tf': 'publish_tf',
    'enable_depth': 'enable_depth',
    'enable_color': 'enable_color',
    'enable_infra1': 'enable_infra1',
    'enable_infra2': 'enable_infra2',
    'enable_emitter': 'enable_emitter',
    'depth_profile': 'depth_module_profile',
    'rgb_profile': 'rgb_camera_profile',
    'use_intra_process_comms': 'use_intra_process_comms',
    'attach_to_shared_component_container': 'attach_to_shared_component_container',
    'component_container_name': 'component_container_name',
}

def launch_function(context):

    # Define the configuration file, containing the camera parameters
    config_file = os.path.join(get_package_share_directory('aqita_launch'), 'config', 'sensors', 'rsd455_config.yaml')

    # Create the launch configuration substitutions
    camera_name = LaunchConfiguration(LAUNCH_PARAMETERS['cam_name']).perform(context)
    camera_serial_no = LaunchConfiguration(LAUNCH_PARAMETERS['cam_serial_no']).perform(context)
    publish_tf = LaunchConfiguration(LAUNCH_PARAMETERS['publish_tf']).perform(context)
    enable_depth = LaunchConfiguration(LAUNCH_PARAMETERS['enable_depth']).perform(context)
    enable_color = LaunchConfiguration(LAUNCH_PARAMETERS['enable_color']).perform(context)
    enable_infra1 = LaunchConfiguration(LAUNCH_PARAMETERS['enable_infra1']).perform(context)
    enable_infra2 = LaunchConfiguration(LAUNCH_PARAMETERS['enable_infra2']).perform(context)
    enable_emitter = LaunchConfiguration(LAUNCH_PARAMETERS['enable_emitter']).perform(context)
    depth_module_profile = LaunchConfiguration(LAUNCH_PARAMETERS['depth_profile']).perform(context)
    rgb_camera_profile = LaunchConfiguration(LAUNCH_PARAMETERS['rgb_profile']).perform(context)
    use_intra_process_comms = LaunchConfiguration(LAUNCH_PARAMETERS['use_intra_process_comms']).perform(context).lower() == 'true'
    attach_to_shared_component_container = LaunchConfiguration(LAUNCH_PARAMETERS['attach_to_shared_component_container'])
    component_container_name = LaunchConfiguration(LAUNCH_PARAMETERS['component_container_name']).perform(context)

    # Check for valid serial number
    if not camera_serial_no.isdigit():
        logging.get_logger('launch').error(f'Invalid serial number specified for camera {camera_name}. Aborting node launch!')
        return

    # If we do not attach to a shared component container we have to create our own container
    rsd455_container = Node(
        name=component_container_name,
        package='rclcpp_components',
        executable='component_container_mt',
        output='screen',
        condition=UnlessCondition(attach_to_shared_component_container),
    )

    # Create the camera node
    # NOTE: The topics published by the Realsense camera will start with the provided namespace value.
    # That's why the namespace field is linked to the camera_name variable, which has to be unique for each
    # camera in a multicam system
    emitter_enabled = 1 if enable_emitter.lower() == 'true' else 0
    param_overwrites = {
        'camera_name': camera_name,
        'serial_no': camera_serial_no,
        'publish_tf': publish_tf.lower() == 'true',
        'enable_infra1': enable_infra1.lower() == 'true',
        'enable_infra2': enable_infra2.lower() == 'true',
        'enable_depth': enable_depth.lower() == 'true',
        'enable_color': enable_color.lower() == 'true',
        'depth_module.profile': depth_module_profile,
        'depth_module.emitter_enabled': emitter_enabled,
        'rgb_camera.profile': rgb_camera_profile,
    }

    if use_intra_process_comms:
        param_overwrites.update(
            {
                'accel_info_qos': 'DEFAULT',
                'accel_qos': 'DEFAULT',
                'color_info_qos': 'DEFAULT',
                'color_qos': 'DEFAULT',
                'depth_info_qos': 'DEFAULT',
                'depth_qos': 'DEFAULT',
                'gyro_info_qos': 'DEFAULT',
                'gyro_qos': 'DEFAULT',
                'infra1_info_qos': 'DEFAULT',
                'infra1_qos': 'DEFAULT',
                'infra2_info_qos': 'DEFAULT',
                'infra2_qos': 'DEFAULT',
                'infra_info_qos': 'DEFAULT',
                'infra_qos': 'DEFAULT',
            }
        )

    rs_cam_node = ComposableNode(
        package='realsense2_camera',
        namespace=camera_name,
        name='rsd455',
        plugin='realsense2_camera::RealSenseNodeFactory',
        parameters=[config_file, param_overwrites],
        extra_arguments=[{'use_intra_process_comms': use_intra_process_comms}],
    )

    # Create the launch action
    load_composable_nodes = LoadComposableNodes(
        target_container=component_container_name,
        composable_node_descriptions=[rs_cam_node]
    )

    return [rsd455_container, load_composable_nodes]

def generate_launch_description():

    # Declare the launch arguments
    launch_args = [
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['cam_name'],
            default_value='rsd455',
            description='Unique name of the specific realsense camera',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['cam_serial_no'],
            default_value='',
            description='Unique serial number of the target camera to launch',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['publish_tf'],
            default_value='False',
            description='Whether the camera to publish static/dynamic transforms',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['enable_depth'],
            default_value='False',
            description='Whether the camera to output a depth map of the scene',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['enable_color'],
            default_value='False',
            description='Whether the camera to output a RGB image',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['enable_infra1'],
            default_value='True',
            description='Whether to enable the infra stream from the left sensor'
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['enable_infra2'],
            default_value='True',
            description='Whether to enable the infra stream from the right sensor'
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['enable_emitter'],
            default_value='False',
            description='Whether to enable the emitter for improved depth'
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['depth_profile'],
            default_value='848x480x60',
            description='Profile for the infrared and depth images in the format "WxHxFPS"',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['rgb_profile'],
            default_value='848x480x60',
            description='Profile for the RGB image in the format "WxHxFPS"',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['use_intra_process_comms'],
            default_value='False',
            description='Whether to enable intraprocess communication for the node. This will override the QoS profile to DEFAULT',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['attach_to_shared_component_container'],
            default_value='False',
            description='Whether to attach the node to a shared component container',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['component_container_name'],
            default_value='rsd455_container',
            description='Name of the container for the node',
        )
    ]

    return LaunchDescription(launch_args + [OpaqueFunction(function=launch_function)])
