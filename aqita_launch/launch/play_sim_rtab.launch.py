#!/usr/bin/env python3
# RTAB-Map launch file for simulation environment

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription, logging
from launch.actions import DeclareLaunchArgument, OpaqueFunction, TimerAction, IncludeLaunchDescription
from launch.substitutions import LaunchConfiguration
from launch.launch_description_sources import PythonLaunchDescriptionSource

# Launch parameter names
LAUNCH_PARAMETERS = {
    'camera_serial': 'camera_serial',
    'use_sim_time': 'use_sim_time',
    'database_path': 'database_path',
    'new_map': 'new_map',
}


def launch_function(context):
    camera_serial = LaunchConfiguration(LAUNCH_PARAMETERS['camera_serial']).perform(context)
    use_sim_time = LaunchConfiguration(LAUNCH_PARAMETERS['use_sim_time']).perform(context)
    database_path = LaunchConfiguration(LAUNCH_PARAMETERS['database_path']).perform(context)
    new_map = LaunchConfiguration(LAUNCH_PARAMETERS['new_map']).perform(context)

    package_dir = get_package_share_directory('aqita_launch')
    launch_dir = os.path.join(package_dir, 'launch')
    slam_dir = os.path.join(launch_dir, 'slam')
    launch_actions = []

    use_sim_time_param = use_sim_time.lower() == 'true'
    new_map_param = new_map.lower() == 'true'

    logging.get_logger('launch').info(f'Camera serial: {camera_serial}')
    logging.get_logger('launch').info(f'Use sim time: {use_sim_time_param}')
    logging.get_logger('launch').info(f'Database path: {database_path}')
    logging.get_logger('launch').info(f'Create new map: {new_map_param}')

    robot_base_link = 'aqita'
    front_camera_name = 'cam_front'
    container_name = 'slam_container'
    log_level = 'info'

    slam_container_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(os.path.join(slam_dir, 'slam_container.launch.py')),
        launch_arguments={'container_name': container_name, 'log_level': log_level}.items(),
    )

    rtabmap_odom_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(os.path.join(slam_dir, 'rtabmap_odom.launch.py')),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'robot_base_link': robot_base_link,
            'front_camera_name': front_camera_name,
            'container_name': container_name,
            'sim_mode': 'true',
        }.items(),
    )

    rtabmap_core_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(os.path.join(slam_dir, 'rtabmap_core.launch.py')),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'robot_base_link': robot_base_link,
            'front_camera_name': front_camera_name,
            'container_name': container_name,
            'database_path': database_path,
            'sim_mode': 'true',
            'new_map': new_map,
        }.items(),
    )

    launch_actions.append(slam_container_launch)
    launch_actions.append(TimerAction(period=2.0, actions=[rtabmap_odom_launch]))
    launch_actions.append(TimerAction(period=3.0, actions=[rtabmap_core_launch]))

    return launch_actions


def generate_launch_description():
    launch_args = [
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['camera_serial'],
            default_value='235422300097',
            description='Serial number of the RealSense camera',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['use_sim_time'], default_value='true', description='Use simulation time'
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['database_path'],
            default_value='./data/rtabmap_sim.db',
            description='Path to the RTAB-Map database file',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['new_map'],
            default_value='false',
            description='Whether to start with a new map (true) or use existing map (false)',
        ),
    ]

    return LaunchDescription(launch_args + [OpaqueFunction(function=launch_function)])
