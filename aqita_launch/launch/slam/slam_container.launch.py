#!/usr/bin/env python3
# SLAM container launch file

from launch import LaunchDescription, logging
from launch.actions import DeclareLaunchArgument, OpaqueFunction
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import ComposableNodeContainer


def launch_setup(context):
    container_name = LaunchConfiguration('container_name').perform(context)
    log_level = LaunchConfiguration('log_level').perform(context)

    logger = logging.get_logger('launch')
    logger.info(f'Container name: {container_name}')
    logger.info(f'Log level: {log_level}')

    container = ComposableNodeContainer(
        name=container_name,
        namespace='',
        package='rclcpp_components',
        executable='component_container_mt',
        output='screen',
        arguments=['--ros-args', '--log-level', log_level],
    )

    return [container]


def generate_launch_description():
    launch_args = [
        DeclareLaunchArgument(
            'container_name', default_value='slam_container', description='Name of the component container'
        ),
        DeclareLaunchArgument('log_level', default_value='info', description='ROS log level for the container'),
    ]

    ld = LaunchDescription(launch_args)
    ld.add_action(OpaqueFunction(function=launch_setup))

    return ld
