#!/usr/bin/env python3
# RTAB-Map odometry component launch file

import os
from launch import LaunchDescription, logging
from launch.actions import DeclareLaunchArgument, OpaqueFunction
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import LoadComposableNodes
from launch_ros.descriptions import ComposableNode
from ament_index_python.packages import get_package_share_directory
import yaml


def launch_setup(context):
    # Get launch configuration parameters
    use_sim_time = LaunchConfiguration('use_sim_time').perform(context)
    robot_base_link = LaunchConfiguration('robot_base_link').perform(context)
    front_camera_name = LaunchConfiguration('front_camera_name').perform(context)
    container_name = LaunchConfiguration('container_name').perform(context)
    sim_mode = LaunchConfiguration('sim_mode').perform(context)
    package_name = LaunchConfiguration('package_name').perform(context)

    # Convert string parameters to appropriate types
    use_sim_time_param = use_sim_time.lower() == 'true'
    sim_mode_param = sim_mode.lower() == 'true'

    # Log important configuration information
    logger = logging.get_logger('launch')
    logger.info(f'Front camera name: {front_camera_name}')
    logger.info(f'Use sim time: {use_sim_time_param}')
    logger.info(f'Container name: {container_name}')
    logger.info(f'Simulation mode: {sim_mode_param}')
    logger.info(f'Package name: {package_name}')

    # Determine the configuration file path
    config_file_path = os.path.join(get_package_share_directory(package_name), 'config', 'rtab', 'rtab_odom.yaml')

    # Standard remappings for camera and IMU
    remappings = [
        ('rgb/image', f'/{front_camera_name}/infra1/image_rect_raw'),
        ('depth/image', f'/{front_camera_name}/depth/image_rect_raw'),
        ('rgb/camera_info', f'/{front_camera_name}/infra1/camera_info'),
        ('depth/camera_info', f'/{front_camera_name}/depth/camera_info'),
        ('imu', f'/{front_camera_name}/imu'),
    ]

    # Load parameters from YAML file
    parameters = {}

    if os.path.exists(config_file_path):
        try:
            with open(config_file_path, 'r') as f:
                loaded_params = yaml.safe_load(f)
                if loaded_params and 'visual_odometry' in loaded_params:
                    parameters = loaded_params['visual_odometry']['ros__parameters']
                    logger.info(f"Loaded parameters from file: {config_file_path}")
                else:
                    logger.warning(f"Config file does not contain visual_odometry parameters")
        except Exception as e:
            logger.error(f"Failed to load parameters from file: {e}")
    else:
        logger.error(f"Config file not found: {config_file_path}")
        return []

    # Override critical parameters from launch arguments
    parameters['frame_id'] = robot_base_link
    parameters['use_sim_time'] = use_sim_time_param

    # Adjust parameters for simulation mode
    if sim_mode_param:
        parameters['Vis/GPU'] = 'false'  # Use CPU in simulation
        parameters['ORB/Gpu'] = 'false'  # Disable GPU for ORB in simulation
        parameters['Vis/CorFlowGpu'] = 'false'  # Disable GPU for optical flow
        parameters['approx_sync_max_interval'] = 1.0  # More relaxed sync for simulation
        parameters['Vis/MaxFeatures'] = '1500'  # Fewer features for CPU processing

    # Create the odometry component
    odom_components = LoadComposableNodes(
        target_container=container_name,
        composable_node_descriptions=[
            ComposableNode(
                package='rtabmap_odom',
                plugin='rtabmap_odom::RGBDOdometry',
                name='visual_odometry',
                parameters=[parameters],
                remappings=remappings,
                extra_arguments=[{'use_intra_process_comms': True}],
            )
        ],
    )

    return [odom_components]


def generate_launch_description():
    # Define launch arguments
    launch_args = [
        DeclareLaunchArgument('use_sim_time', default_value='false', description='Use simulation time'),
        DeclareLaunchArgument('robot_base_link', default_value='base_link', description='Name of the robot base link'),
        DeclareLaunchArgument('front_camera_name', default_value='cam_front', description='Name of the front camera'),
        DeclareLaunchArgument(
            'container_name', default_value='slam_container', description='Name of the component container'
        ),
        DeclareLaunchArgument(
            'sim_mode', default_value='false', description='Whether to use simulation-specific parameters'
        ),
        DeclareLaunchArgument(
            'package_name', default_value='aqita_launch', description='Name of the package containing config files'
        ),
    ]

    # Create and return the launch description
    ld = LaunchDescription(launch_args)
    ld.add_action(OpaqueFunction(function=launch_setup))

    return ld
