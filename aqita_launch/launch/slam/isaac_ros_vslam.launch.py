# TODO: Licence description

# NOTE: Launch file for starting an Isaac-ROS VSLAM node

# Import launch tools
import os, sys
import isaac_ros_launch_utils as lu
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription, LaunchContext, logging
from launch_ros.actions import LoadComposableNodes
from launch_ros.descriptions import ComposableNode
from launch.actions import DeclareLaunchArgument, OpaqueFunction
from launch.conditions import UnlessCondition
from launch.substitutions import LaunchConfiguration

# Import tools from the aqita initialization module
from aqita_init.topics import get_realsense_camera_topics
from aqita_init.frames import get_realsense_camera_frames

# Launch parameter names
LAUNCH_PARAMETERS = {
    'output_topics_prefix': 'output_topics_prefix',
    'node_name': 'node_name',
    'robot_base_link': 'robot_base_link',
    'vslam_map': 'vslam_map',
    'vslam_odom': 'vslam_odom',
    'camera_names': 'camera_names',
    'imu_frame': 'imu_frame',
    'imu_topic': 'imu_topic',
    'enable_imu_fusion': 'enable_imu_fusion',
    'publish_map_to_odom_tf': 'publish_map_to_odom_tf',
    'publish_odom_to_base_tf': 'publish_odom_to_base_tf',
    'attach_to_shared_component_container': 'attach_to_shared_component_container',
    'component_container_name': 'component_container_name',
    'log_level': 'log_level'
}

def launch_function(context: LaunchContext, *args, **kwargs):

    logger = logging.get_logger('launch')
    logger.info('Starting Isaac-ROS VSLAM node...')

    # Define the configuration file, containing the VSLAM parameters
    config_file = os.path.join(get_package_share_directory('aqita_launch'), 'config', 'rtab', 'isaac_ros_vslam.yaml')

    # Create the launch configuration substitutions
    output_topics_prefix = LaunchConfiguration(LAUNCH_PARAMETERS['output_topics_prefix']).perform(context)
    node_name = LaunchConfiguration(LAUNCH_PARAMETERS['node_name']).perform(context)
    robot_base_link = LaunchConfiguration(LAUNCH_PARAMETERS['robot_base_link']).perform(context)
    vslam_map = LaunchConfiguration(LAUNCH_PARAMETERS['vslam_map']).perform(context)
    vslam_odom = LaunchConfiguration(LAUNCH_PARAMETERS['vslam_odom']).perform(context)
    camera_names = LaunchConfiguration(LAUNCH_PARAMETERS['camera_names']).perform(context).strip().split(',')
    imu_frame = LaunchConfiguration(LAUNCH_PARAMETERS['imu_frame']).perform(context)
    imu_topic = LaunchConfiguration(LAUNCH_PARAMETERS['imu_topic']).perform(context)
    enable_imu_fusion = LaunchConfiguration(LAUNCH_PARAMETERS['enable_imu_fusion']).perform(context)
    publish_map_to_odom_tf = LaunchConfiguration(LAUNCH_PARAMETERS['publish_map_to_odom_tf']).perform(context)
    publish_odom_to_base_tf = LaunchConfiguration(LAUNCH_PARAMETERS['publish_odom_to_base_tf']).perform(context)
    attach_to_shared_component_container = LaunchConfiguration(LAUNCH_PARAMETERS['attach_to_shared_component_container'])
    component_container_name = LaunchConfiguration(LAUNCH_PARAMETERS['component_container_name'])
    log_level = LaunchConfiguration(LAUNCH_PARAMETERS['log_level']).perform(context)
    num_cameras = len(camera_names)

    if num_cameras == 0 or num_cameras > 4:
        logger.error('Invalid number of cameras specified when launching the VSLAM node. Aborting launch!')
        sys.exit()
    else:
        logger.info(f'Node configured with {num_cameras} number of cameras with names: {camera_names}')

    # Create a shared container to hold composable nodes for speed-ups through intra process communication
    vslam_container = lu.component_container(
        component_container_name,
        container_type='multithreaded',
        log_level=log_level,
        condition=UnlessCondition(attach_to_shared_component_container)
    )

    # Set the VSLAM node parameters and remappings
    cam_index = 0
    remappings = []
    camera_optical_frames = []

    for i, name in enumerate(camera_names):
        cam_name = name.strip()
        camera_optical_frames.append(get_realsense_camera_frames(cam_name).left_cam_optical_frame)
        camera_optical_frames.append(get_realsense_camera_frames(cam_name).right_cam_optical_frame)
        remappings.append(('visual_slam/image_'         +str(cam_index),    get_realsense_camera_topics(cam_name).left_img_topic))
        remappings.append(('visual_slam/camera_info_'   +str(cam_index),    get_realsense_camera_topics(cam_name).left_cam_info_topic))
        remappings.append(('visual_slam/image_'         +str(cam_index+1),  get_realsense_camera_topics(cam_name).right_img_topic))
        remappings.append(('visual_slam/camera_info_'   +str(cam_index+1),  get_realsense_camera_topics(cam_name).right_cam_info_topic))

        logger.info(f'Camera with name {cam_name} added to VSLAM launch description with optical frames and remappings:')
        logger.info(f'\t {camera_optical_frames[cam_index]}')
        logger.info(f'\t {camera_optical_frames[cam_index+1]}')
        for j in range(len(remappings)-4,len(remappings)):
            logger.info(f'\t {remappings[j]}')

        cam_index += 2

    remappings.append(('visual_slam/imu',imu_topic))
    remappings.append(('visual_slam/tracking/odometry', output_topics_prefix+'_visual_slam/tracking/odometry'))
    remappings.append(('visual_slam/tracking/vo_pose_covariance', output_topics_prefix+'_visual_slam/tracking/vo_pose_covariance'))
    remappings.append(('visual_slam/tracking/vo_pose', output_topics_prefix+'_visual_slam/tracking/vo_pose'))
    remappings.append(('visual_slam/tracking/slam_path', output_topics_prefix+'_visual_slam/tracking/slam_path'))
    remappings.append(('visual_slam/tracking/vo_path', output_topics_prefix+'_visual_slam/tracking/vo_path'))
    remappings.append(('visual_slam/status', output_topics_prefix+'_visual_slam/status'))
    remappings.append(('visual_slam/vis/gravity', output_topics_prefix+'_visual_slam/vis/gravity'))
    remappings.append(('visual_slam/vis/landmarks_cloud', output_topics_prefix+'_visual_slam/vis/landmarks_cloud'))
    remappings.append(('visual_slam/vis/localizer', output_topics_prefix+'_visual_slam/vis/localizer'))
    remappings.append(('visual_slam/vis/localizer_loop_closure_cloud', output_topics_prefix+'_visual_slam/vis/localizer_loop_closure_cloud'))
    remappings.append(('visual_slam/vis/localizer_map_cloud', output_topics_prefix+'_visual_slam/vis/localizer_map_cloud'))
    remappings.append(('visual_slam/vis/localizer_observations_cloud', output_topics_prefix+'_visual_slam/vis/localizer_observations_cloud'))
    remappings.append(('visual_slam/vis/loop_closure_cloud', output_topics_prefix+'_visual_slam/vis/loop_closure_cloud'))
    remappings.append(('visual_slam/vis/observations_cloud', output_topics_prefix+'_visual_slam/vis/observations_cloud'))
    remappings.append(('visual_slam/vis/pose_graph_edges', output_topics_prefix+'_visual_slam/vis/pose_graph_edges'))
    remappings.append(('visual_slam/vis/pose_graph_edges2', output_topics_prefix+'_visual_slam/vis/pose_graph_edges2'))
    remappings.append(('visual_slam/vis/pose_graph_nodes', output_topics_prefix+'_visual_slam/vis/pose_graph_nodes'))
    remappings.append(('visual_slam/vis/slam_odometry', output_topics_prefix+'_visual_slam/vis/slam_odometry'))
    remappings.append(('visual_slam/vis/velocity', output_topics_prefix+'_visual_slam/vis/velocity'))
    remappings.append(('visual_slam/initial_pose', output_topics_prefix+'_visual_slam/initial_pose'))
    remappings.append(('visual_slam/trigger_hint', output_topics_prefix+'_visual_slam/trigger_hint'))
    remappings.append(('visual_slam/reset', output_topics_prefix+'_visual_slam/reset'))
    remappings.append(('visual_slam/get_all_poses', output_topics_prefix+'_visual_slam/get_all_poses'))
    remappings.append(('visual_slam/set_slam_pose', output_topics_prefix+'_visual_slam/set_slam_pose'))
    remappings.append(('visual_slam/save_map', output_topics_prefix+'_visual_slam/save_map'))
    remappings.append(('visual_slam/load_map', output_topics_prefix+'_visual_slam/load_map'))
    remappings.append(('visual_slam/localize_in_map', output_topics_prefix+'_visual_slam/localize_in_map'))

    # Create the VSLAM node
    vslam_node = ComposableNode(
        name=node_name,
        package='isaac_ros_visual_slam',
        plugin='nvidia::isaac_ros::visual_slam::VisualSlamNode',
        parameters=[
            config_file,
            # Parameter overwrites
            {
                "num_cameras": len(camera_optical_frames),
                "map_frame": vslam_map,
                "odom_frame": vslam_odom,
                "base_frame": robot_base_link,
                "imu_frame": imu_frame,
                "camera_optical_frames": camera_optical_frames,
                "enable_imu_fusion": (enable_imu_fusion.lower() == 'true'),
                "publish_map_to_odom_tf": (publish_map_to_odom_tf.lower() == 'true'),
                "publish_odom_to_base_tf": (publish_odom_to_base_tf.lower() == 'true'),
                "enable_localization_n_mapping": (publish_map_to_odom_tf.lower() == 'true')
            }
        ],
        remappings=remappings
    )

    # Create the launch action
    load_composable_nodes = LoadComposableNodes(target_container=component_container_name, composable_node_descriptions=[vslam_node])
    return [vslam_container, load_composable_nodes]

def generate_launch_description():

    # Declare the launch arguments
    launch_args = [
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['output_topics_prefix'],
            default_value='',
            description='Prefix to attach to the VSLAM output topics'),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['node_name'],
            default_value='vslam_node',
            description='Name of the created VSLAM node'),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['robot_base_link'],
            default_value='base_link',
            description='Name of the robot base link'),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['vslam_map'],
            default_value='map',
            description='Name of the map frame'),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['vslam_odom'],
            default_value='odom',
            description='Name of the odom frame'),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['camera_names'],
            default_value='cam_front, cam_left, cam_right',
            description='Names of the cameras to use as a comma separated string. Up to four cameras is supported'),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['imu_frame'],
            default_value='imu_frame',
            description='Name of the IMU frame if IMU fusion is enabled'),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['imu_topic'],
            default_value='imu_topic',
            description='Name of the IMU topic if IMU fusion is enabled'),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['enable_imu_fusion'],
            default_value='True',
            description='Whether to enable the IMU fusion for the node'),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['publish_map_to_odom_tf'],
            default_value='True',
            description='Whether the VSLAM node to publish the map to odom transformation'),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['publish_odom_to_base_tf'],
            default_value='True',
            description='Whether the VSLAM node to publish the odom to robot_base_link transformation'),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['attach_to_shared_component_container'],
            default_value='False',
            description='Whether to run the VSLAM node into an external container for possible speed-ups'),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['component_container_name'],
            default_value='vslam_containter',
            description='Name of the external component container in which to run the VSLAM node'),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['log_level'],
            default_value='info',
            description='Desired log level',
            choices=['debug', 'info', 'warn'])
    ]

    return LaunchDescription(launch_args + [OpaqueFunction(function=launch_function)])
    
