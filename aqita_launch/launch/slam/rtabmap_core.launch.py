#!/usr/bin/env python3
# RTAB-Map core component launch file

import os
from launch import LaunchDescription, logging
from launch.actions import DeclareLaunchArgument, OpaqueFunction
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import LoadComposableNodes
from launch_ros.descriptions import ComposableNode
from ament_index_python.packages import get_package_share_directory
import yaml


def launch_setup(context):
    use_sim_time = LaunchConfiguration('use_sim_time').perform(context)
    robot_base_link = LaunchConfiguration('robot_base_link').perform(context)
    front_camera_name = LaunchConfiguration('front_camera_name').perform(context)
    container_name = LaunchConfiguration('container_name').perform(context)
    database_path = LaunchConfiguration('database_path').perform(context)
    sim_mode = LaunchConfiguration('sim_mode').perform(context)
    package_name = LaunchConfiguration('package_name').perform(context)
    new_map = LaunchConfiguration('new_map').perform(context)

    use_sim_time_param = use_sim_time.lower() == 'true'
    sim_mode_param = sim_mode.lower() == 'true'
    new_map_param = new_map.lower() == 'true'

    logger = logging.get_logger('launch')
    logger.info(f'Front camera name: {front_camera_name}')
    logger.info(f'Database path: {database_path}')
    logger.info(f'Use sim time: {use_sim_time_param}')
    logger.info(f'Container name: {container_name}')
    logger.info(f'Simulation mode: {sim_mode_param}')
    logger.info(f'Package name: {package_name}')
    logger.info(f'Create new map: {new_map_param}')

    config_file_path = os.path.join(get_package_share_directory(package_name), 'config', 'rtab', 'rtab_core.yaml')

    remappings = [
        ('rgb/image', f'/{front_camera_name}/infra1/image_rect_raw'),
        ('depth/image', f'/{front_camera_name}/depth/image_rect_raw'),
        ('rgb/camera_info', f'/{front_camera_name}/infra1/camera_info'),
        ('depth/camera_info', f'/{front_camera_name}/depth/camera_info'),
        ('odom', 'odom'),
        ('imu', f'/{front_camera_name}/imu'),
    ]

    parameters = {}

    if os.path.exists(config_file_path):
        try:
            with open(config_file_path, 'r') as f:
                loaded_params = yaml.safe_load(f)
                if loaded_params and 'rtabmap' in loaded_params:
                    parameters = loaded_params['rtabmap']['ros__parameters']
                    logger.info(f"Loaded parameters from file: {config_file_path}")
                else:
                    logger.warning(f"Config file does not contain rtabmap parameters")
        except Exception as e:
            logger.error(f"Failed to load parameters from file: {e}")
    else:
        logger.error(f"Config file not found: {config_file_path}")
        return []

    # Override critical parameters from launch arguments
    parameters['frame_id'] = robot_base_link
    parameters['use_sim_time'] = use_sim_time_param
    if database_path:
        parameters['database_path'] = database_path

    parameters['Rtabmap/DeleteDatabaseOnStart'] = 'true' if new_map_param else 'false'

    # Adjust parameters for simulation mode
    if sim_mode_param:
        parameters['Vis/GPU'] = 'false'  # Use CPU in simulation
        parameters['ORB/Gpu'] = 'false'  # Disable GPU for ORB in simulation
        parameters['Vis/CorFlowGpu'] = 'false'  # Disable GPU for optical flow
        parameters['approx_sync_max_interval'] = 0.5  # More relaxed sync for simulation

    intra_process = not sim_mode_param

    core_components = LoadComposableNodes(
        target_container=container_name,
        composable_node_descriptions=[
            ComposableNode(
                package='rtabmap_slam',
                plugin='rtabmap_slam::CoreWrapper',
                name='rtabmap',
                parameters=[parameters],
                remappings=remappings,
                extra_arguments=[{'use_intra_process_comms': intra_process}],
            )
        ],
    )

    return [core_components]


def generate_launch_description():
    launch_args = [
        DeclareLaunchArgument('use_sim_time', default_value='false', description='Use simulation time'),
        DeclareLaunchArgument('robot_base_link', default_value='base_link', description='Name of the robot base link'),
        DeclareLaunchArgument('front_camera_name', default_value='cam_front', description='Name of the front camera'),
        DeclareLaunchArgument(
            'container_name', default_value='slam_container', description='Name of the component container'
        ),
        DeclareLaunchArgument(
            'database_path', default_value='./data/rtabmap_drone.db', description='Path to the RTAB-Map database file'
        ),
        DeclareLaunchArgument(
            'sim_mode', default_value='false', description='Whether to use simulation-specific parameters'
        ),
        DeclareLaunchArgument(
            'package_name', default_value='aqita_launch', description='Name of the package containing config files'
        ),
        DeclareLaunchArgument(
            'new_map',
            default_value='false',
            description='Whether to start with a new map (true) or use existing map (false)',
        ),
    ]

    ld = LaunchDescription(launch_args)
    ld.add_action(OpaqueFunction(function=launch_setup))

    return ld
