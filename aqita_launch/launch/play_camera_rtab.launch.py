# RTAB-Map launch file for RealSense D455 camera

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription, logging
from launch.actions import DeclareLaunchArgument, OpaqueFunction, TimerAction, IncludeLaunchDescription
from launch.substitutions import LaunchConfiguration
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.actions import Node

# Launch parameter names
LAUNCH_PARAMETERS = {
    'camera_serial': 'camera_serial',
    'use_sim_time': 'use_sim_time',
    'database_path': 'database_path',
    'enable_rviz': 'enable_rviz',
}


def launch_function(context):
    camera_serial = LaunchConfiguration(LAUNCH_PARAMETERS['camera_serial']).perform(context)
    use_sim_time = LaunchConfiguration(LAUNCH_PARAMETERS['use_sim_time']).perform(context)
    database_path = LaunchConfiguration(LAUNCH_PARAMETERS['database_path']).perform(context)
    enable_rviz = LaunchConfiguration(LAUNCH_PARAMETERS['enable_rviz']).perform(context)

    package_dir = get_package_share_directory('aqita_launch')
    launch_dir = os.path.join(package_dir, 'launch')
    slam_dir = os.path.join(launch_dir, 'slam')
    launch_actions = []

    use_sim_time_param = use_sim_time.lower() == 'true'
    enable_rviz_param = enable_rviz.lower() == 'true'

    logging.get_logger('launch').info(f'Camera serial: {camera_serial}')
    logging.get_logger('launch').info(f'Use sim time: {use_sim_time_param}')
    logging.get_logger('launch').info(f'Database path: {database_path}')
    logging.get_logger('launch').info(f'Enable RViz: {enable_rviz_param}')

    robot_base_link = 'aqita'
    front_camera_name = 'cam_front'
    container_name = 'slam_container'
    log_level = 'info'

    robot_state_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(os.path.join(launch_dir, 'robot', 'robot_state.launch.py')),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'robot_base_link': robot_base_link,
            'front_camera_name': front_camera_name,
        }.items(),
    )
    launch_actions.append(robot_state_launch)

    transforms_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(os.path.join(launch_dir, 'robot', 'transforms.launch.py')),
        launch_arguments={'use_sim_time': use_sim_time, 'robot_base_link': robot_base_link}.items(),
    )
    launch_actions.append(transforms_launch)

    rviz_config_path = os.path.join(package_dir, 'rviz', 'realsense_rtabmap.rviz')
    logging.get_logger('launch').info(f'Using RViz config at: {rviz_config_path}')

    # Include SLAM container launch
    slam_container_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(os.path.join(slam_dir, 'slam_container.launch.py')),
        launch_arguments={'container_name': container_name, 'log_level': log_level}.items(),
    )
    launch_actions.append(slam_container_launch)

    realsense_camera_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(os.path.join(launch_dir, 'realsense_camera.launch.py')),
        launch_arguments={
            'camera_name': front_camera_name,
            'camera_serial': camera_serial,
            'use_sim_time': use_sim_time,
            'container_name': container_name,
            'enable_depth': 'true',
            'enable_color': 'true',
            'depth_profile': '848x480x60',
            'rgb_profile': '848x480x60',
            'enable_imu': 'true',
        }.items(),
    )

    imu_filter_node = Node(
        package='imu_filter_madgwick',
        executable='imu_filter_madgwick_node',
        name='imu_filter_madgwick',
        parameters=[{'use_mag': False, 'publish_tf': False, 'world_frame': 'enu', 'use_sim_time': use_sim_time_param}],
        remappings=[('/imu/data_raw', f'/{front_camera_name}/imu'), ('/imu/data', '/imu/data')],
        output='screen',
    )

    rtabmap_odom_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(os.path.join(slam_dir, 'rtabmap_odom.launch.py')),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'front_camera_name': front_camera_name,
            'container_name': container_name,
            'sim_mode': 'false',
        }.items(),
    )

    rtabmap_core_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(os.path.join(slam_dir, 'rtabmap_core.launch.py')),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'front_camera_name': front_camera_name,
            'container_name': container_name,
            'database_path': database_path,
            'sim_mode': 'false',
        }.items(),
    )

    if enable_rviz_param:
        rviz_node = Node(
            package='rviz2',
            executable='rviz2',
            name='rviz2',
            arguments=['-d', rviz_config_path],
            parameters=[{'use_sim_time': use_sim_time_param}],
            condition=IfCondition(enable_rviz),
        )
        launch_actions.append(rviz_node)

    # Add components to the launch description with appropriate timing
    launch_actions.append(TimerAction(period=2.0, actions=[realsense_camera_launch]))
    launch_actions.append(TimerAction(period=4.0, actions=[imu_filter_node]))
    launch_actions.append(TimerAction(period=5.0, actions=[rtabmap_odom_launch]))
    launch_actions.append(TimerAction(period=6.0, actions=[rtabmap_core_launch]))

    return launch_actions


def generate_launch_description():
    launch_args = [
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['camera_serial'],
            default_value='235422300097',
            description='Serial number of the RealSense camera',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['use_sim_time'], default_value='false', description='Use simulation time'
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['database_path'],
            default_value='./data/rtabmap_live.db',
            description='Path to the RTAB-Map database file',
        ),
        DeclareLaunchArgument(
            LAUNCH_PARAMETERS['enable_rviz'], default_value='false', description='Enable RViz visualization'
        ),
    ]

    return LaunchDescription(launch_args + [OpaqueFunction(function=launch_function)])
