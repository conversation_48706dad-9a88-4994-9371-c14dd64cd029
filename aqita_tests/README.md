# aqita_tests

## Overview

**aqita_tests** is a ROS2 package designed for **testing various software components** of the AQITA system.

The package includes:

- **Example service server nodes** that expose services for integration testing.
- **Example service client nodes** that send requests and process responses.
- **Example message publisher nodes** that send component status messages.

This helps developers ensure that service definitions and communication work correctly before integrating them into production nodes.

---

## Package Contents

- `src/aqita_interfaces`
  - `test_component_status_pub.cpp`  
    Example component status publisher node for testing the interface `ComponentStatus`.
  - `test_mission_start_trigger_client.cpp`  
    Example service client node that sends periodic requests for testing the interface `MissionStartTrigger`.
  - `test_mission_start_trigger_server.cpp`  
    Example service server node that provides the service for testing the interface `MissionStartTrigger`.

---

## Usage

After building and sourcing, you can test various interfaces, for example:

- ### Test component status publisher node
```bash
ros2 run aqita_tests test_component_status_pub
```

- ### Test component service and client nodes
Terminal 1:
```bash
ros2 run aqita_tests test_mission_start_trigger_server
```
Terminal 2:
```bash
ros2 run aqita_tests test_mission_start_trigger_client
```

---

## Dependencies

This package depends on:

- `rclcpp`
- `aqita_interfaces`

