
# TODO: License declaration

import sys
from launch import LaunchDescription, logging
from launch.actions import OpaqueFunction, DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node

def launch_function(context):

    logger = logging.get_logger('launch')
    logger.info("Launching component status publisher test node ...")

    # create the launch configuration substitutions
    node_name = LaunchConfiguration('node_name').perform(context)
    status_topic = LaunchConfiguration('status_topic').perform(context)

    try:
        pub_dt_ms = int(LaunchConfiguration('pub_dt_ms').perform(context))
        pub_deadline_ms = int(LaunchConfiguration('pub_deadline_ms').perform(context))
    except:
        logger.error("Error parsing the publication time parameters. Aborting launch!")
        sys.exit()

    # create the status publisher node
    comp_status_publisher_node = Node(
        name=node_name,
        package='aqita_tests',
        executable='test_component_status_pub',
        output='screen',
        parameters=[
            {
                'status_topic': status_topic,
                'pub_dt_ms': pub_dt_ms,
                'pub_deadline_ms': pub_deadline_ms
            }
        ]
    )

    return [comp_status_publisher_node]

def generate_launch_description():

    # declare the launch arguments
    launch_args = [
        DeclareLaunchArgument(
            'node_name',
            default_value='component_status_node',
            description='Name of the publisher node'
        ),
        DeclareLaunchArgument(
            'status_topic',
            default_value='/component/status',
            description='Name of the topic on which the status message will be published'
        ),
        DeclareLaunchArgument(
            'pub_dt_ms',
            default_value='1000',
            description='Status message publication interval in milliseconds'
        ),
        DeclareLaunchArgument(
            'pub_deadline_ms',
            default_value='2000',
            description='Publication deadline in milliseconds'
        ),
    ]

    return LaunchDescription(launch_args + [OpaqueFunction(function=launch_function)])

