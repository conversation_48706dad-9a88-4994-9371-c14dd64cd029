
# TODO: License description

cmake_minimum_required(VERSION 3.8)
project(aqita_tests LANGUAGES C CXX)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(aqita_interfaces REQUIRED)

# create executables
add_executable(test_component_status_pub src/aqita_interfaces/test_component_status_pub.cpp)
ament_target_dependencies(test_component_status_pub rclcpp aqita_interfaces)

add_executable(test_mission_start_trigger_server src/aqita_interfaces/test_mission_start_trigger_server.cpp)
ament_target_dependencies(test_mission_start_trigger_server rclcpp aqita_interfaces)

add_executable(test_mission_start_trigger_client src/aqita_interfaces/test_mission_start_trigger_client.cpp)
ament_target_dependencies(test_mission_start_trigger_client rclcpp aqita_interfaces)

# install directories
install(DIRECTORY launch
  DESTINATION share/${PROJECT_NAME})

# install targets
install(TARGETS
  test_component_status_pub
  test_mission_start_trigger_server
  test_mission_start_trigger_client
  DESTINATION lib/${PROJECT_NAME})

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
