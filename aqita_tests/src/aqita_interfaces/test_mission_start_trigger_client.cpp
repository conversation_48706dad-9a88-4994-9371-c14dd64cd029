#include "rclcpp/rclcpp.hpp"
#include "aqita_interfaces/srv/mission_start_trigger.hpp"
#include <memory>
#include <chrono>

using namespace std::chrono_literals;

/**
 * @brief Class for testing the MissionStartTrigger service client
*/
class TestMissionStartTriggerClient : public rclcpp::Node
{
public:
  // constructor
  explicit TestMissionStartTriggerClient() : Node("test_mission_start_trigger_client")
  {
    RCLCPP_INFO(get_logger(), "Starting MissionStartTrigger client testing node...");
    std::string service_name = "test_mission_start_trigger";

    // create the service client
    client_ = this->create_client<aqita_interfaces::srv::MissionStartTrigger>(service_name);

    // wait for service to become available
    RCLCPP_INFO(get_logger(), "Waiting for service %s...", service_name.c_str());
    while (!client_->wait_for_service(1s))
    {
      RCLCPP_INFO(get_logger(), "\tservice not available, waiting again...");

      if (!rclcpp::ok())
      {
        RCLCPP_ERROR(get_logger(), "Interrupted while waiting for the service. Terminating node ...");
        return;
      }
    }

    service_detected_ = true;
    RCLCPP_INFO(get_logger(), "Service %s detected!", service_name.c_str());

    // create a service request timer
    timer_ = this->create_wall_timer(std::chrono::seconds(1), std::bind(&TestMissionStartTriggerClient::service_call, this));

    RCLCPP_INFO(get_logger(), "Node running...");
  }

  bool get_service_detected()
  {
    return service_detected_;
  }

private:
  // call service at regular intervals
  void service_call()
  {
    RCLCPP_INFO(get_logger(), "Calling service ...");
    auto request = std::make_shared<aqita_interfaces::srv::MissionStartTrigger::Request>();
    request->mission_id = "exploration";
    request->drone_id = "aqita";
    auto future = client_->async_send_request(request, std::bind(&TestMissionStartTriggerClient::response_callback, this, std::placeholders::_1));
  }

  // process service response
  void response_callback(rclcpp::Client<aqita_interfaces::srv::MissionStartTrigger>::SharedFuture future)
  {
    auto response = future.get();
    RCLCPP_INFO(get_logger(), "Received service response: success=%s", response->trigger_success ? "true" : "false");
  }

  bool service_detected_ = false;
  rclcpp::Client<aqita_interfaces::srv::MissionStartTrigger>::SharedPtr client_;
  rclcpp::TimerBase::SharedPtr timer_;
};

int main(int argc, char * argv[])
{
  rclcpp::init(argc, argv);
  auto service_client = std::make_shared<TestMissionStartTriggerClient>();

  if ( service_client->get_service_detected() )
  {
    rclcpp::spin(service_client);
    rclcpp::shutdown();
  }

  return 0;
}
