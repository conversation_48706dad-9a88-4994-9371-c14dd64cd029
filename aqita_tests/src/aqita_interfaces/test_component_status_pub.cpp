#include "rclcpp/rclcpp.hpp"
#include "aqita_interfaces/msg/component_status.hpp"
#include <memory>

/**
 * @brief Class for testing the ComponentStatus message
 * @param status_topic name of the topic on which the status message will be published
 * @param pub_dt_ms status message publication interval in milliseconds
 * @param pub_deadline_ms publication deadline in milliseconds
*/
class TestCompStatusPublisher : public rclcpp::Node
{
public:
  //constructor
  explicit TestCompStatusPublisher() : Node("test_comp_status_publisher")
  {
    // declare parameters
    std::string status_topic = declare_parameter<std::string>("status_topic", "/component/status");
    int64_t pub_dt_ms = declare_parameter<int64_t>("pub_dt_ms", 1000);
    int64_t pub_deadline_ms = declare_parameter<int64_t>("pub_deadline_ms", 2000);

    RCLCPP_INFO(get_logger(), "Starting ComponentStatus publisher testing node on topic %s with pub interval %ld ms ...", status_topic.c_str(), pub_dt_ms);

    // create the message publisher
    rclcpp::QoS pub_qos = rclcpp::QoS(rclcpp::KeepLast(10)).reliable().durability_volatile().deadline(rclcpp::Duration(static_cast<int32_t>(pub_deadline_ms/1000.0f),0));
    publisher_ = this->create_publisher<aqita_interfaces::msg::ComponentStatus>(status_topic, pub_qos);

    // create publishing timer
    timer_ = this->create_wall_timer(std::chrono::milliseconds(pub_dt_ms), std::bind(&TestCompStatusPublisher::publish_status, this));

    RCLCPP_INFO(get_logger(), "Node running...");
  }

private:
  // publish status message at regular intervals
  void publish_status()
  {
    auto msg = aqita_interfaces::msg::ComponentStatus();
    rclcpp::Time timestamp = this->get_clock()->now();

    // create the component message
    msg.stamp = timestamp;
    msg.state = aqita_interfaces::msg::ComponentStatus::OK;
    msg.description = "Status message";

    // publish message
    publisher_->publish(msg);
  }

  rclcpp::Publisher<aqita_interfaces::msg::ComponentStatus>::SharedPtr publisher_;
  rclcpp::TimerBase::SharedPtr timer_;
};

int main(int argc, char * argv[])
{
  rclcpp::init(argc, argv);
  rclcpp::spin(std::make_shared<TestCompStatusPublisher>());
  rclcpp::shutdown();
  return 0;
}
