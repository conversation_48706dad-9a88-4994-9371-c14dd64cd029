#include "rclcpp/rclcpp.hpp"
#include "aqita_interfaces/srv/mission_start_trigger.hpp"
#include <memory>

/**
 * @brief Class for testing the MissionStartTrigger service server
*/
class TestMissionStartTriggerServer : public rclcpp::Node
{
public:
  // constructor
  explicit TestMissionStartTriggerServer() : Node("test_mission_start_trigger_server")
  {
    RCLCPP_INFO(get_logger(), "Starting MissionStartTrigger server testing node...");
    std::string service_name = "test_mission_start_trigger";

    // create the service using std::bind
    //mission_trigger_service_ = this->create_service<aqita_interfaces::srv::MissionStartTrigger>("test_mission_start_trigger",
    //  std::bind(&TestMissionStartTriggerServer::mission_start_trigger, this, std::placeholders::_1, std::placeholders::_2));

    // create the service using a lambda function
    mission_trigger_service_ = this->create_service<aqita_interfaces::srv::MissionStartTrigger>(
      service_name,
      [this](const std::shared_ptr<aqita_interfaces::srv::MissionStartTrigger::Request> request,
        std::shared_ptr<aqita_interfaces::srv::MissionStartTrigger::Response> response)
        {
          this->mission_start_trigger(request, response);
        }
      );

    RCLCPP_INFO(get_logger(), "Node running...");
  }

private:
  // process service request
  void mission_start_trigger(const std::shared_ptr<aqita_interfaces::srv::MissionStartTrigger::Request> request, 
                                   std::shared_ptr<aqita_interfaces::srv::MissionStartTrigger::Response> response)
  {
    std::string mission_id = request->mission_id;
    std::string drone_id = request->drone_id;
    RCLCPP_INFO(get_logger(), "Received mission start trigger request (mission id: %s, drone id: %s)", mission_id.c_str(), drone_id.c_str());
    response->trigger_success = trigger_success_;
    trigger_success_ = !trigger_success_;
  }

  bool trigger_success_ = true;
  rclcpp::Service<aqita_interfaces::srv::MissionStartTrigger>::SharedPtr mission_trigger_service_;
};

int main(int argc, char * argv[])
{
  rclcpp::init(argc, argv);
  rclcpp::spin(std::make_shared<TestMissionStartTriggerServer>());
  rclcpp::shutdown();
  return 0;
}
