
# TODO: License description

cmake_minimum_required(VERSION 3.8)
project(aqita_state_machine LANGUAGES C CXX)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclcpp_action REQUIRED)
find_package(aqita_interfaces REQUIRED)

# include directories
include_directories(
  include
)

# create executables
add_executable(aqita_state_machine_node src/aqita_sm.cpp)

# link libraries
ament_target_dependencies(aqita_state_machine_node rclcpp rclcpp_action aqita_interfaces)

# install targets
install(TARGETS
  aqita_state_machine_node
  DESTINATION lib/${PROJECT_NAME}
)

# install config files
#install(
#  DIRECTORY config/
#  DESTINATION share/${PROJECT_NAME}/config
#)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
