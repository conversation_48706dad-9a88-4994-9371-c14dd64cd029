
// TODO: License description

#include "aqita_state_machine/aqita_sm.h"

// component status watchdog
void AqitaStateMachine::component_status_watchdog()
{
    // get the current time
    auto now = get_clock()->now();

    for ( auto & comp : components_ )
    {
        if ( comp.is_alive() )
        {
            rclcpp::Duration status_dt = now - comp.get_last_status_timestamp();

            if ( status_dt.seconds() > 5.0 )
            {
                comp.set_dead();
                RCLCPP_INFO(get_logger(), "Did not receive status updates for component %s since %.4f seconds. Component is now DEAD!", comp.name.c_str(), status_dt.seconds());
            }
        }
    }
}

// main loop callback function
void AqitaStateMachine::main_loop()
{
    if ( new_state_ != state_ )
    {
        // process new state change request
        switch (new_state_)
        {
        case SystemStates::ROBOT_STARTING:
            // send a PSYS start command from Jetson to PM, upon which the PM should switch on the power to the ESC processor and the FC
            // this should result in regular ESC status messages
            new_state_ = SystemStates::PSYS_STARTING;
            RCLCPP_INFO(get_logger(), "Starting propulsion system ...");
            break;
        case SystemStates::PSYS_STARTED:
            // send a slam component launch command - this should result in regular slam component status messages
            new_state_ = SystemStates::SLAM_STARTING;
            RCLCPP_INFO(get_logger(), "Starting SLAM ...");
            break;
        case SystemStates::SLAM_STARTED:
            // send a mavros component launch command - this should result in regular flight controller status messages
            new_state_ = SystemStates::MAVROS_STARTING;
            RCLCPP_INFO(get_logger(), "Starting MAVROS ...");
            break;
        case SystemStates::MAVROS_STARTED:
            // send a inference component launch command - this should result in regular inference status messages
            new_state_ = SystemStates::INFER_STARTING;
            RCLCPP_INFO(get_logger(), "Starting inference system ...");
            break;
        case SystemStates::INFER_STARTED:
            // send a mission controller start command - this should result in regular navigation status messages
            new_state_ = SystemStates::MISSION_CTRL_STARTING;
            RCLCPP_INFO(get_logger(), "Starting mission controller ...");
            break;
        case SystemStates::MISSION_CTRL_STARTED:
            // initiate take-off command
            new_state_ = SystemStates::TAKE_OFF;
            RCLCPP_INFO(get_logger(), "All systems started successfully! Initiating take-off ...");
            break;
        case SystemStates::AIRBORNE:
            // send mission start request to mission controller - use action server client
            _mc_client.send_cmd_mc(START);
            new_state_ = SystemStates::MISSION_STARTING;
            RCLCPP_INFO(get_logger(), "Mission starting ...");
            break;
        case SystemStates::MISSION_STARTED:
            break;
        default:
            break;
        }

        state_ = new_state_;
        return;
    }

    switch (state_)
    {
    case SystemStates::PSYS_STARTING:
        // start the ESC component
        get_component(AqitaSystemComponents::ESC).start_service("aqita_psys.service");

        // check the ESC component status
        if ( get_component(AqitaSystemComponents::ESC).get_state() == ComponentState::OK )
        {
            new_state_ = SystemStates::PSYS_STARTED;
            RCLCPP_INFO(get_logger(), "Propulsion system started successfully!");
        }
        break;
    case SystemStates::SLAM_STARTING:
        // start the SLAM component
        get_component(AqitaSystemComponents::SLAM).start_service("aqita_slam.service");

        // check the SLAM status
        if ( get_component(AqitaSystemComponents::SLAM).get_state() == ComponentState::OK )
        {
            new_state_ = SystemStates::SLAM_STARTED;
            RCLCPP_INFO(get_logger(), "SLAM started successfully!");
        }
        break;
    case SystemStates::MAVROS_STARTING:
        // start the MAVROS component
        get_component(AqitaSystemComponents::FC).start_service("aqita_mavros.service");

        // check the FC status
        if ( get_component(AqitaSystemComponents::FC).get_state() == ComponentState::OK )
        {
            new_state_ = SystemStates::MAVROS_STARTED;
            RCLCPP_INFO(get_logger(), "MAVROS started successfully!");
        }
        break;
    case SystemStates::INFER_STARTING:
        // start the inference component
        get_component(AqitaSystemComponents::INFER).start_service("aqita_infer.service");

        // check the inference component status
        if ( get_component(AqitaSystemComponents::INFER).get_state() == ComponentState::OK )
        {
            new_state_ = SystemStates::INFER_STARTED;
            RCLCPP_INFO(get_logger(), "Inference system started successfully!");
        }
        break;
    case SystemStates::MISSION_CTRL_STARTING:
        // start the mission controller component
        get_component(AqitaSystemComponents::NAV).start_service("aqita_nav.service");

        // check the navigation component state
        if ( get_component(AqitaSystemComponents::NAV).get_state() == ComponentState::OK )
        {
            new_state_ = SystemStates::MISSION_CTRL_STARTED;
            RCLCPP_INFO(get_logger(), "Mission controller started successfully!");
        }
        break;
    case SystemStates::TAKE_OFF:
        // check the take-off complete condition
        new_state_ = SystemStates::AIRBORNE;
        RCLCPP_INFO(get_logger(), "Take-off completed successfully!");
        break;
    case SystemStates::MISSION_STARTING:
        // check the mission starting completed condition
        new_state_ = SystemStates::MISSION_STARTED;
        RCLCPP_INFO(get_logger(), "Mission started successfully!");
        break;
    case SystemStates::MISSION_STARTED:
        // mission started - mission feedback should be received through the action server connection with the mission controller
        // mission completed event should be triggered from the action server connection
        RCLCPP_INFO(get_logger(), "Mission running...");
        break;
    default:
        break;
    }
}

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);
    rclcpp::NodeOptions options;
    options.parameter_overrides({
        rclcpp::Parameter("component.cloud.status_topic",   "/cloud/status"),
        rclcpp::Parameter("component.esc.status_topic",     "/esc/status"),
        rclcpp::Parameter("component.slam.status_topic",    "/slam/status"),
        rclcpp::Parameter("component.fc.status_topic",      "/fc/status"),
        rclcpp::Parameter("component.infer.status_topic",   "/infer/status"),
        rclcpp::Parameter("component.nav.status_topic",     "/nav/status")
    });

    auto aqita_sm_node = std::make_shared<AqitaStateMachine>(options);
    aqita_sm_node->init_mc_client();
    rclcpp::spin(aqita_sm_node);
    rclcpp::shutdown();
    return 0;
}
