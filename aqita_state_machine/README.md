# aqita_state_machine

The package contains the `aqita_state_machine` component source code. This component is used for monitoring and switching between system states. 

## Dependencies

The package depends on the following additional ROS2 packages:
- aqita_interfaces
- aqita_services (this is not actually a ROS2 package. It contains component launch services)

## Build

To build the package first obtain and build the dependencies, and then run the following command in a terminal inside of a running docker container, if such is used

```bash
colcon build --symlink-install --packages-select aqita_state_machine
```

## Testing
To test if the package is build properly run:

```bash
ros2 run aqita_state_machine aqita_state_machine_node
```

The node runs a service server for initiating a mission start. To trigger the mission start process run the following command in another terminal:

```bash
ros2 service call /mission_start_request aqita_interfaces/srv/MissionStartTrigger "{mission_id: 'exploration', drone_id: 'aqita'}"
```

This should trigger the mission start logic, which should be evident in the terminal logs.

To stop all components, started by the state machine, execute the following command on the host:

```bash
sudo systemctl stop aqita*
```
