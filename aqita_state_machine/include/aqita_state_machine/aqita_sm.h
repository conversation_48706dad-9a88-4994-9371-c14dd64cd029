
// TODO: License description

#ifndef __AQITA_STATE_MACHINE_H
#define __AQITA_STATE_MACHINE_H

#include "rclcpp/rclcpp.hpp"
#include "aqita_interfaces/msg/component_status.hpp"
#include "aqita_interfaces/srv/mission_start_trigger.hpp"
#include "aqita_state_machine/mc_client.h"
#include <memory>
#include <shared_mutex>

// comment out the next line to build the code without verbose log messages
//#define BUILD_DEBUG

using shared_mutex = std::shared_timed_mutex;
using ComponentStatus = aqita_interfaces::msg::ComponentStatus;
using MissionStartTrigger = aqita_interfaces::srv::MissionStartTrigger;

/**
 * @brief Enum defining the system bring-up states
*/
enum class SystemStates
{
    USER_NOT_INITIALIZED=0,
    USER_INITIALIZED,
    IDLE,
    ROBOT_STARTING,
    PSYS_STARTING,
    PSYS_STARTED,
    SLAM_STARTING,
    SLAM_STARTED,
    MAVROS_STARTING,
    <PERSON>VR<PERSON>_STARTED,
    INFER_STARTING,
    INFER_STARTED,
    MISSION_CTRL_STARTING,
    MISSION_CTRL_STARTED,
    TAKE_OFF,
    AIRBORNE,
    MISSION_STARTING,
    MISSION_STARTED,
    MISSION_COMPLETED,
    LAND,
    FS_MISSION_PAUSE,
    FS_MISSION_ABORT,
    FS_EMERGENCY_LAND,
    NUM_STATES
};

/**
 * @brief Enum defining the different component states as specified in the ComponentStatus message type
*/
enum class ComponentState
{
    OK=0,
    DEGRADED,
    ERROR
};

/**
 * @brief Enum defining the expected system components by the aqita state machine
 * @note The aqita state machine will subscribe to the status messages of these components and use the
 * latest component states when determining whether the system can be (or should) be switched from one state to another.
 * @note **To add a new component add a new entry before the last NUM_COMPONENTS field, map it to a AqitaStateMachine node
 * parameter within the AqitaStateMachine class definition and add an entry in the "init_components" class method**.
*/
enum class AqitaSystemComponents
{
    USER_NOTIFY=0,  // User notify component
    NETWORK,        // Network watchdog component
    CLOUD,          // Cloud component
    ESC,            // ESC component
    FC,             // Flight controller component
    SLAM,           // SLAM component
    NAV,            // Navigation component
    INFER,          // Inference component
    NUM_COMPONENTS
};

/**
 * @brief Aqita state machine node class definition
 * @note The aqita state machine is responsible for monitoring components status and switching between system states
 * @param "mission_start_trigger_service_name" name of the service on which the node provides a mission start trigger interface
 * @param "component.user_notify.status_topic" user notify component status topic
 * @param "component.network.status_topic" network watchdog component status topic
 * @param "component.cloud.status_topic" cloud component status topic
 * @param "component.esc.status_topic" esc component status topic
 * @param "component.fc.status_topic" flight controller component status topic
 * @param "component.slam.status_topic" slam component status topic
 * @param "component.nav.status_topic" navigation component status topic
 * @param "component.infer.status_topic" inference component status topic
*/
class AqitaStateMachine : public rclcpp::Node
{
public:
    struct Component
    {
        // component name property
        std::string name;

        // component service started property
        bool service_started;

        // component alive property
        bool alive;

        // component status property
        ComponentStatus status;

        // mutex for read/write control
        mutable shared_mutex mutex;

        // returns the alive status of a component
        bool is_alive() const { std::shared_lock<shared_mutex> lock(mutex); return alive; }

        // returns the state of a component
        ComponentState get_state() const { std::shared_lock<shared_mutex> lock(mutex); return static_cast<ComponentState>(status.state); }

        // returns the timestamp of the latest component status topic
        rclcpp::Time get_last_status_timestamp() const { std::shared_lock<shared_mutex> lock(mutex); return status.stamp; }

        // sets the component alive status to false
        void set_dead() { std::unique_lock<shared_mutex> lock(mutex); if ( alive ) { alive = false; } }

        // start the component service
        void start_service( std::string service_name )
        {
            std::unique_lock<shared_mutex> lock(mutex);

            if ( service_started )
                return;

            std::string dbus_cmd =
                "sudo dbus-send --system --print-reply --dest=org.freedesktop.systemd1 "
                "/org/freedesktop/systemd1 "
                "org.freedesktop.systemd1.Manager.StartUnit "
                "string:\"" + service_name + "\" "
                "string:\"replace\"";
            
            int ret = std::system(dbus_cmd.c_str());
            if (ret == 0)
                service_started = true;
        }
    };

    // constructor
    explicit AqitaStateMachine(const rclcpp::NodeOptions & options) : Node("aqita_state_machine", options)
    {
        RCLCPP_INFO(get_logger(), "Starting aqita state machine...");

        // define node specific parameters
        // TODO: declare these as configurable parameters
        int64_t main_loop_timestep_ms = 1000;       // main loop execution time interval in milliseconds
        int64_t comp_watchdog_timestep_ms = 3000;   // component watchdog loop execution time interval in milliseconds
        int32_t component_qos_deadline_s = 2;       // if a status message is not received within this time interval the component is considered "dead"
        size_t component_qos_depth = 10;            // number of status messages to keep in queue


        // initialize components
        init_components();

        // define qos settings for the component status messages subscriptions
        //rclcpp::QoS comp_sub_qos = rclcpp::QoS(rclcpp::KeepLast(component_qos_depth)).reliable().transient_local().deadline(rclcpp::Duration(component_qos_deadline_s,0));
        rclcpp::QoS comp_sub_qos = rclcpp::QoS(rclcpp::KeepLast(component_qos_depth)).reliable().durability_volatile().deadline(rclcpp::Duration(component_qos_deadline_s,0));

        // create a map from a component status topic parameter to a component name
        // NOTE: to add a new component map its status topic parameter to the respective new element in the AqitaSystemComponents enum
        std::map<std::string, AqitaSystemComponents> map_status_topic_param_to_component = {
            {"component.user_notify.status_topic",  AqitaSystemComponents::USER_NOTIFY},
            {"component.network.status_topic",      AqitaSystemComponents::NETWORK},
            {"component.cloud.status_topic",        AqitaSystemComponents::CLOUD},
            {"component.esc.status_topic",          AqitaSystemComponents::ESC},
            {"component.fc.status_topic",           AqitaSystemComponents::FC},
            {"component.slam.status_topic",         AqitaSystemComponents::SLAM},
            {"component.nav.status_topic",          AqitaSystemComponents::NAV},
            {"component.infer.status_topic",        AqitaSystemComponents::INFER}
        };

        // declare node parameters and create topic subscriptions
        std::string mission_start_trigger_service_name = declare_parameter<std::string>("mission_start_trigger_service_name", "mission_start_request");

        for (const auto & key_value : map_status_topic_param_to_component)
        {
            // get the component status topic name
            std::string topic_name = declare_parameter<std::string>(key_value.first, "");
            if ( topic_name.empty() ) {
                // skip unspecified components
                continue;
            }

            // build the map from a status topic to a component name
            map_status_topic_to_component_[topic_name] = key_value.second;

            // add a deadline callback to handle stale components
            rclcpp::SubscriptionOptions comp_sub_options;
            comp_sub_options.event_callbacks.deadline_callback = [this, topic_name](rclcpp::QOSDeadlineRequestedInfo & deadline_info)
            {
                this->component_status_deadline_cb(topic_name, deadline_info);
            };

            // create a subscription to the component status topic
            auto sub = create_subscription<ComponentStatus>(
                topic_name,
                comp_sub_qos,
                [this, topic_name](const ComponentStatus::SharedPtr msg)
                {
                    this->update_component_status(topic_name, msg);
                },
                comp_sub_options
            );
            component_status_subscriptions_.push_back(sub);
            RCLCPP_INFO(get_logger(), "\tSubscribed to status topic %s for component %s", topic_name.c_str(), components_[static_cast<size_t>(key_value.second)].name.c_str());
        }

        // create the mission start trigger service
        mission_start_trigger_service_ = create_service<MissionStartTrigger>(
            mission_start_trigger_service_name,
            std::bind(&AqitaStateMachine::mission_start_trigger, this, std::placeholders::_1, std::placeholders::_2)
        );
        RCLCPP_INFO(get_logger(), "Mission start trigger interface provided on service with name '%s'", mission_start_trigger_service_name.c_str());

        // create a main loop timer function
        main_loop_timer_ = create_wall_timer(std::chrono::milliseconds(main_loop_timestep_ms), std::bind(&AqitaStateMachine::main_loop, this));

        // create a component watchdog timer function
        component_watchdog_timer_ = create_wall_timer(std::chrono::milliseconds(comp_watchdog_timestep_ms), std::bind(&AqitaStateMachine::component_status_watchdog, this));

        // summarize node initialization
        RCLCPP_INFO(get_logger(), "Aqita state machine started successfully!");
    }

    void init_mc_client() {
        _mc_client.init(shared_from_this());
    }

private:
    // main loop callback function
    void main_loop();

    // component status watchdog
    void component_status_watchdog();

    // initialize the system components
    // NOTE: when adding new component add its id to name mapping within this function
    void init_components()
    {
        // each component state is set to an invalid value, which should prevent mission start requests until the respective component publishes a status message
        for ( int i = 0; i < static_cast<int>(AqitaSystemComponents::NUM_COMPONENTS); ++i )
        {
            components_[i].alive = false;
            components_[i].service_started = false;
            components_[i].status.state = ComponentStatus::ERROR;

            // set the component name
            switch (static_cast<AqitaSystemComponents>(i))
            {
            case AqitaSystemComponents::USER_NOTIFY:
                components_[i].name = "USER_NOTIFY";
                break;
            case AqitaSystemComponents::NETWORK:
                components_[i].name = "NETWORK";
                break;
            case AqitaSystemComponents::CLOUD:
                components_[i].name = "CLOUD";
                break;
            case AqitaSystemComponents::ESC:
                components_[i].name = "ESC";
                break;
            case AqitaSystemComponents::FC:
                components_[i].name = "FC";
                break;
            case AqitaSystemComponents::SLAM:
                components_[i].name = "SLAM";
                break;
            case AqitaSystemComponents::NAV:
                components_[i].name = "NAV";
                break;
            case AqitaSystemComponents::INFER:
                components_[i].name = "INFER";
                break;
            default:
                components_[i].name = "UNKNOWN";
            }
        }
    }

    // returns a reference to the specified component
    Component & get_component(AqitaSystemComponents comp_id) { return components_[static_cast<size_t>(comp_id)]; }

    // mission start trigger service callback function. The mission start process is initiated upon a mission start trigger request by a client
    void mission_start_trigger(const std::shared_ptr<MissionStartTrigger::Request> request, std::shared_ptr<MissionStartTrigger::Response> response)
    {
        RCLCPP_INFO(get_logger(), "Received mission start trigger request (mission id: %s, drone id: %s)", request->mission_id.c_str(), request->drone_id.c_str());
        response->trigger_success = true;

        if ( state_ == SystemStates::IDLE )
        {
            RCLCPP_INFO(get_logger(), "Starting robot ...");
            new_state_ = SystemStates::ROBOT_STARTING;
        }
    }

    // update the status of a system component
    void update_component_status(const std::string & status_topic, const ComponentStatus::SharedPtr msg)
    {
        size_t id = static_cast<size_t>(map_status_topic_to_component_[status_topic]);
        std::unique_lock<shared_mutex> lock(components_[id].mutex);
#ifdef BUILD_DEBUG
        RCLCPP_INFO(get_logger(), "Received component status on topic %s for component %s", status_topic.c_str(), components_[id].name.c_str());
#endif
        if ( !components_[id].alive )
        {
            components_[id].alive = true;
#ifndef BUILD_DEBUG
            RCLCPP_INFO(get_logger(), "Received component status on topic %s for component %s. Component is now ALIVE!", status_topic.c_str(), components_[id].name.c_str());
#endif
        }
        
        components_[id].status = *msg;
    }

    // handle status topic deadline missed event for a component
    void component_status_deadline_cb(const std::string & status_topic, rclcpp::QOSDeadlineRequestedInfo & deadline_info)
    {
        size_t id = static_cast<size_t>(map_status_topic_to_component_[status_topic]);
        if ( components_[id].is_alive() )
            RCLCPP_WARN(get_logger(), "Deadline missed on topic '%s' for component %s: total_count=%d", status_topic.c_str(), components_[id].name.c_str(), deadline_info.total_count);
    }

    // start a systemd unit
    // TODO: remove the function if not needed at a later stage
    bool start_systemd_unit(std::string unit_name)
    {
        std::string dbus_cmd =
            "sudo dbus-send --system --print-reply --dest=org.freedesktop.systemd1 "
            "/org/freedesktop/systemd1 "
            "org.freedesktop.systemd1.Manager.StartUnit "
            "string:\"" + unit_name + "\" "
            "string:\"replace\"";
        
        int ret = std::system(dbus_cmd.c_str());
        if (ret != 0)
        {
            RCLCPP_WARN(get_logger(), "Failed to start systemd unit: %s", unit_name.c_str());
            return false;
        }

        RCLCPP_INFO(get_logger(), "Systemd unit: %s started successfully", unit_name.c_str());
        return true;
        
        // the following code creates a system bus connection an attempts to start a systemd unit with the provided name
        // NOTE: the methods used require the installation of the following dbus c++ headers library "sudo apt install libsdbus-c++-dev",
        // which are included using "#include <sdbus-c++/sdbus-c++.h>"
        //try
        //{
        //    // connect to the system bus
        //    auto dbus_connection = sdbus::createSystemBusConnection();
        //
        //    // create a proxy to the systemd Manager object
        //    auto proxy = sdbus::createProxy(*dbus_connection, "org.freedesktop.systemd1", "/org/freedesktop/systemd1");
        //
        //    // call StartUnit method
        //    std::string jobPath;
        //    proxy->callMethod("StartUnit")
        //        .onInterface("org.freedesktop.systemd1.Manager")
        //        .withArguments(unit_name, "replace")
        //        .storeResultsTo(jobPath);
        //
        //    RCLCPP_INFO(get_logger(), "Started unit %s with job path %s", unit_name, jobPath);
        //}
        //catch(const sdbus::Error &e)
        //{
        //    RCLCPP_ERROR(get_logger(), "D-Bus Error: %s - %s", e.getName(), e.getMessage());
        //}
    }

    // component status topic to component name mapping
    std::unordered_map<std::string, AqitaSystemComponents> map_status_topic_to_component_;

    // component status subscriptions
    std::vector<rclcpp::Subscription<ComponentStatus>::SharedPtr> component_status_subscriptions_;

    // component status registry for storing the latest component status information
    std::array<Component, static_cast<size_t>(AqitaSystemComponents::NUM_COMPONENTS)> components_;

    // node services
    rclcpp::Service<MissionStartTrigger>::SharedPtr mission_start_trigger_service_;

    // main loop timer
    rclcpp::TimerBase::SharedPtr main_loop_timer_;

    // component watchdog timer
    rclcpp::TimerBase::SharedPtr component_watchdog_timer_;

    // system state
    SystemStates state_ = SystemStates::IDLE;
    SystemStates new_state_ = SystemStates::IDLE;

    // MC client
    MissionControllerClient _mc_client;

};

#endif // __AQITA_STATE_MACHINE_H
