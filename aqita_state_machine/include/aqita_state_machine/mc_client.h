#ifndef __MC_CLIENT_H
#define __MC_CLIENT_H


#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "aqita_interfaces/action/mission_control.hpp"

using MissionControl = aqita_interfaces::action::MissionControl;
constexpr uint8_t START = aqita_interfaces::action::MissionControl::Goal::START;
constexpr uint8_t PAUSE = aqita_interfaces::action::MissionControl::Goal::PAUSE;
constexpr uint8_t ABORT = aqita_interfaces::action::MissionControl::Goal::ABORT;

class MissionControllerClient
{
public:
  MissionControllerClient() {}

  void init(const rclcpp::Node::SharedPtr & parent)  
  {
    _node = parent;
    mc_client_ = rclcpp_action::create_client<MissionControl>(_node, "/mission_control");
  }

   /** @param _task START, PAUSE, ABORT */
  void send_cmd_mc(uint8_t _task) 
  {
    if (!mc_client_->wait_for_action_server(std::chrono::seconds(2))) {
      RCLCPP_WARN(logger_, "Action server not available");
      return;
    }

    auto goal_msg = MissionControl::Goal();
    RCLCPP_INFO(logger_,"State Machine send cmd: START to Mission Controller");
    goal_msg.mission_action = _task;

    auto options = rclcpp_action::Client<MissionControl>::SendGoalOptions();
    options.feedback_callback =
      std::bind(&MissionControllerClient::feedback_callback, this, std::placeholders::_1, std::placeholders::_2);
    options.result_callback =
      std::bind(&MissionControllerClient::result_callback, this, std::placeholders::_1);

   
    mc_client_->async_send_goal(goal_msg, options);
   
  }

private:
  rclcpp_action::Client<MissionControl>::SharedPtr mc_client_;
  rclcpp_action::ClientGoalHandle<MissionControl>::SharedPtr goal_handle_;
  rclcpp::Logger logger_{rclcpp::get_logger("MissionControllerClient")};
  rclcpp::Node::SharedPtr _node;


 

  void feedback_callback(
    rclcpp_action::ClientGoalHandle<MissionControl>::SharedPtr,
    const std::shared_ptr<const MissionControl::Feedback> feedback)
  {
    RCLCPP_INFO(logger_, "LAST ID: %d POSE:[%.3f,%.3f,%.3f]", feedback->progress,feedback->pose.position.x,feedback->pose.position.y,feedback->pose.position.z);
  }

  void result_callback(
    const rclcpp_action::ClientGoalHandle<MissionControl>::WrappedResult & result)
  {
    RCLCPP_INFO(logger_, "Result: %s", result.result->result_message.c_str());
  }

};

#endif // __MC_CLIENT_H

