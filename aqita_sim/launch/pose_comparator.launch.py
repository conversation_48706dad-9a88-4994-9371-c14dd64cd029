from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration


def generate_launch_description():
    # Sim TF Publisher parameters
    sim_odom_topic_arg = DeclareLaunchArgument(
        "sim_odom_topic",
        default_value="/sim/odom",
        description="Topic for simulation odometry",
    )

    transform_mode_arg = DeclareLaunchArgument(
        "transform_mode",
        default_value="custom_z90",
        description="Coordinate transformation mode to apply",
    )

    # Pose comparator parameters
    reference_frame_arg = DeclareLaunchArgument(
        "reference_frame", default_value="map", description="Common reference frame"
    )

    measured_frame_arg = DeclareLaunchArgument(
        "measured_frame", default_value="base_link", description="Frame from RTAB-Map"
    )

    reference_child_frame_arg = DeclareLaunchArgument(
        "reference_child_frame",
        default_value="sim/base_link",
        description="Ground truth frame from simulation",
    )

    sim_tf_publisher_node = Node(
        package="aqita_sim",
        executable="sim_tf_publisher_node",
        name="sim_tf_publisher",
        parameters=[
            {
                "sim_odom_topic": LaunchConfiguration("sim_odom_topic"),
                "parent_frame": LaunchConfiguration("reference_frame"),
                "child_frame": LaunchConfiguration("reference_child_frame"),
                "transform_mode": LaunchConfiguration("transform_mode"),
            }
        ],
        output="screen",
    )

    tf_pose_comparator_node = Node(
        package="aqita_sim",
        executable="tf_pose_comparator_node",
        name="tf_pose_comparator",
        parameters=[
            {
                "reference_frame": LaunchConfiguration("reference_frame"),
                "measured_frame": LaunchConfiguration("measured_frame"),
                "reference_child_frame": LaunchConfiguration("reference_child_frame"),
            }
        ],
        output="screen",
    )

    ld = LaunchDescription()

    ld.add_action(sim_odom_topic_arg)
    ld.add_action(transform_mode_arg)
    ld.add_action(reference_frame_arg)
    ld.add_action(measured_frame_arg)
    ld.add_action(reference_child_frame_arg)

    ld.add_action(sim_tf_publisher_node)
    ld.add_action(tf_pose_comparator_node)

    return ld
