from setuptools import setup, find_packages
import os
from glob import glob

package_name = 'aqita_sim'

setup(
    name=package_name,
    version='0.1.0',
    packages=find_packages(),
    data_files=[
        ('share/ament_index/resource_index/packages', ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        (os.path.join('share', package_name, 'launch'), glob('launch/*.py')),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='Petar Velev',
    maintainer_email='<EMAIL>',
    description='A ROS2 package for simulation analysis and validation tools',
    license='Apache License 2.0',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'pose_comparator_node = aqita_sim.pose_comparator_node:main',
            'tf_pose_comparator_node = aqita_sim.tf_pose_comparator_node:main',
            'sim_tf_publisher_node = aqita_sim.sim_tf_publisher_node:main',
            'drone_teleop = aqita_sim.aqita_teleop:main',
        ],
    },
)
