#!/usr/bin/env python3
"""
Analyze drift data from TF Pose Comparator
Usage: python3 analyze_drift.py <csv_file_path>
"""

import os
import sys
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.gridspec import GridSpec

def load_data(csv_path):
    """Load the CSV data into a pandas DataFrame"""
    if not os.path.exists(csv_path):
        print(f"Error: CSV file not found at {csv_path}")
        sys.exit(1)
    
    try:
        df = pd.read_csv(csv_path)
        # Set the first timestamp to 0 and convert all timestamps to seconds
        if 'timestamp' in df.columns:
            start_time = df['timestamp'].iloc[0]
            df['relative_time'] = df['timestamp'] - start_time
        return df
    except Exception as e:
        print(f"Error loading CSV file: {e}")
        sys.exit(1)

def plot_drift_metrics(df):
    """Plot position, orientation, and total drift over time"""
    plt.figure(figsize=(12, 10))
    
    # Use GridSpec for better control over subplot layout
    gs = GridSpec(4, 1, height_ratios=[2, 1, 1, 1])
    
    # Plot 1: Overall metrics
    ax1 = plt.subplot(gs[0])
    ax1.plot(df['relative_time'], df['position_drift'], 'b-', label='Position Drift (m)')
    ax1.plot(df['relative_time'], df['orientation_drift_deg'], 'g-', label='Orientation Drift (deg)')
    ax1.plot(df['relative_time'], df['total_drift'], 'r-', label='Total Drift')
    ax1.set_title('Drift Metrics Over Time')
    ax1.set_ylabel('Drift')
    ax1.legend()
    ax1.grid(True)
    
    # Plot 2: Position Drift
    ax2 = plt.subplot(gs[1], sharex=ax1)
    ax2.plot(df['relative_time'], df['position_drift'], 'b-')
    ax2.set_ylabel('Position Drift (m)')
    ax2.grid(True)
    
    # Plot 3: Orientation Drift
    ax3 = plt.subplot(gs[2], sharex=ax1)
    ax3.plot(df['relative_time'], df['orientation_drift_deg'], 'g-')
    ax3.set_ylabel('Orientation Drift (deg)')
    ax3.grid(True)
    
    # Plot 4: Total Drift
    ax4 = plt.subplot(gs[3], sharex=ax1)
    ax4.plot(df['relative_time'], df['total_drift'], 'r-')
    ax4.set_ylabel('Total Drift')
    ax4.set_xlabel('Time (seconds)')
    ax4.grid(True)
    
    plt.tight_layout()
    
    # Calculate statistics
    pos_max = df['position_drift'].max()
    pos_mean = df['position_drift'].mean()
    pos_std = df['position_drift'].std()
    
    ori_max = df['orientation_drift_deg'].max()
    ori_mean = df['orientation_drift_deg'].mean()
    ori_std = df['orientation_drift_deg'].std()
    
    # Add statistics as text
    stats_text = (
        f"Position Drift (m): Max={pos_max:.4f}, Mean={pos_mean:.4f}, StdDev={pos_std:.4f}\n"
        f"Orientation Drift (deg): Max={ori_max:.4f}, Mean={ori_mean:.4f}, StdDev={ori_std:.4f}"
    )
    plt.figtext(0.5, 0.01, stats_text, ha='center', bbox={'facecolor':'white', 'alpha':0.8, 'pad':5})
    
    # Save the plot
    base_path = os.path.splitext(os.path.basename(csv_path))[0]
    plot_path = f"{base_path}_drift_analysis.png"
    plt.savefig(plot_path, dpi=150, bbox_inches='tight')
    print(f"Saved plot to {plot_path}")
    
    return plot_path

def plot_trajectories(df):
    """Plot reference and measured trajectories"""
    plt.figure(figsize=(10, 10))
    
    # Plot reference trajectory (blue)
    plt.plot(df['ref_pos_x'], df['ref_pos_y'], 'b-', label='Reference Trajectory')
    plt.plot(df['ref_pos_x'].iloc[0], df['ref_pos_y'].iloc[0], 'bo', markersize=8)  # Start point
    plt.plot(df['ref_pos_x'].iloc[-1], df['ref_pos_y'].iloc[-1], 'b*', markersize=10)  # End point
    
    # Plot measured trajectory (red)
    plt.plot(df['meas_pos_x'], df['meas_pos_y'], 'r-', label='Measured Trajectory')
    plt.plot(df['meas_pos_x'].iloc[0], df['meas_pos_y'].iloc[0], 'ro', markersize=8)  # Start point
    plt.plot(df['meas_pos_x'].iloc[-1], df['meas_pos_y'].iloc[-1], 'r*', markersize=10)  # End point
    
    plt.title('XY Trajectories')
    plt.xlabel('X Position (m)')
    plt.ylabel('Y Position (m)')
    plt.legend()
    plt.grid(True)
    plt.axis('equal')  # Equal scaling for X and Y
    
    # Save the plot
    base_path = os.path.splitext(os.path.basename(csv_path))[0]
    plot_path = f"{base_path}_trajectories.png"
    plt.savefig(plot_path, dpi=150, bbox_inches='tight')
    print(f"Saved plot to {plot_path}")
    
    return plot_path

def generate_drift_report(df, csv_path):
    """Generate a statistical report on the drift data"""
    duration = df['relative_time'].iloc[-1] - df['relative_time'].iloc[0]
    num_points = len(df)
    
    # Position drift statistics
    pos_mean = df['position_drift'].mean()
    pos_median = df['position_drift'].median()
    pos_max = df['position_drift'].max()
    pos_min = df['position_drift'].min()
    pos_std = df['position_drift'].std()
    
    # Orientation drift statistics
    ori_mean = df['orientation_drift_deg'].mean()
    ori_median = df['orientation_drift_deg'].median()
    ori_max = df['orientation_drift_deg'].max()
    ori_min = df['orientation_drift_deg'].min()
    ori_std = df['orientation_drift_deg'].std()
    
    # Total drift statistics
    total_mean = df['total_drift'].mean()
    total_median = df['total_drift'].median()
    total_max = df['total_drift'].max()
    total_min = df['total_drift'].min()
    total_std = df['total_drift'].std()
    
    # Calculate trajectory length
    def calculate_path_length(x, y, z):
        points = np.column_stack([x, y, z])
        # Calculate distances between consecutive points
        distances = np.sqrt(np.sum(np.diff(points, axis=0)**2, axis=1))
        return np.sum(distances)
    
    ref_path_length = calculate_path_length(df['ref_pos_x'], df['ref_pos_y'], df['ref_pos_z'])
    meas_path_length = calculate_path_length(df['meas_pos_x'], df['meas_pos_y'], df['meas_pos_z'])
    
    # Calculate distance between start and end points
    ref_start_point = np.array([df['ref_pos_x'].iloc[0], df['ref_pos_y'].iloc[0], df['ref_pos_z'].iloc[0]])
    ref_end_point = np.array([df['ref_pos_x'].iloc[-1], df['ref_pos_y'].iloc[-1], df['ref_pos_z'].iloc[-1]])
    ref_direct_distance = np.sqrt(np.sum((ref_end_point - ref_start_point)**2))
    
    meas_start_point = np.array([df['meas_pos_x'].iloc[0], df['meas_pos_y'].iloc[0], df['meas_pos_z'].iloc[0]])
    meas_end_point = np.array([df['meas_pos_x'].iloc[-1], df['meas_pos_y'].iloc[-1], df['meas_pos_z'].iloc[-1]])
    meas_direct_distance = np.sqrt(np.sum((meas_end_point - meas_start_point)**2))
    
    # Calculate final drift at end point
    final_pos_drift = df['position_drift'].iloc[-1]
    final_ori_drift = df['orientation_drift_deg'].iloc[-1]
    
    # Calculate drift relative to path length (in percentage)
    relative_drift = (final_pos_drift / ref_path_length) * 100 if ref_path_length > 0 else float('nan')
    
    # Create report
    report = [
        f"Drift Analysis Report for: {os.path.basename(csv_path)}",
        f"{'=' * 80}",
        f"Total duration: {duration:.2f} seconds",
        f"Number of data points: {num_points}",
        f"",
        f"Trajectory Statistics:",
        f"  Reference path length: {ref_path_length:.4f} m",
        f"  Measured path length: {meas_path_length:.4f} m",
        f"  Reference direct distance (start to end): {ref_direct_distance:.4f} m",
        f"  Measured direct distance (start to end): {meas_direct_distance:.4f} m",
        f"",
        f"Position Drift Statistics (meters):",
        f"  Mean: {pos_mean:.4f}",
        f"  Median: {pos_median:.4f}",
        f"  Max: {pos_max:.4f}",
        f"  Min: {pos_min:.4f}",
        f"  StdDev: {pos_std:.4f}",
        f"",
        f"Orientation Drift Statistics (degrees):",
        f"  Mean: {ori_mean:.4f}",
        f"  Median: {ori_median:.4f}",
        f"  Max: {ori_max:.4f}",
        f"  Min: {ori_min:.4f}",
        f"  StdDev: {ori_std:.4f}",
        f"",
        f"Total Drift Statistics:",
        f"  Mean: {total_mean:.4f}",
        f"  Median: {total_median:.4f}",
        f"  Max: {total_max:.4f}",
        f"  Min: {total_min:.4f}",
        f"  StdDev: {total_std:.4f}",
        f"",
        f"Final Drift Values:",
        f"  Position drift at end: {final_pos_drift:.4f} m",
        f"  Orientation drift at end: {final_ori_drift:.4f} degrees",
        f"  Relative drift (% of path length): {relative_drift:.2f}%",
    ]
    
    # Save report to text file
    base_path = os.path.splitext(os.path.basename(csv_path))[0]
    report_path = f"{base_path}_report.txt"
    with open(report_path, 'w') as f:
        f.write('\n'.join(report))
    
    print(f"Saved report to {report_path}")
    print('\n'.join(report))
    
    return report_path

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python3 analyze_drift.py <csv_file_path>")
        sys.exit(1)
    
    csv_path = sys.argv[1]
    print(f"Analyzing drift data from: {csv_path}")
    
    # Load data
    df = load_data(csv_path)
    print(f"Loaded {len(df)} data points")
    
    # Plot drift metrics
    drift_plot = plot_drift_metrics(df)
    
    # Plot trajectories
    trajectory_plot = plot_trajectories(df)
    
    # Generate report
    report_path = generate_drift_report(df, csv_path)
    
    print(f"\nAnalysis complete. Generated files:")
    print(f"  - {drift_plot}")
    print(f"  - {trajectory_plot}")
    print(f"  - {report_path}")
    
    # Show plots (if running in interactive mode)
    plt.show()
