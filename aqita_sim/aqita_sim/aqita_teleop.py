#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
import sys
import select
import termios
import tty
import time

msg = """
Custom Drone Control
---------------------------
Controls:
   q       w      e
     a     s     d
     
w/a/s/d : move forward/left/backward/right
q/e : rotate left/right
space : move up
ctrl : move down
s (when hovering) : start moving backward
s (when moving) : stop all movement (hover)

Press key once to start moving, again to increase speed by 20%.
Press opposite direction to stop and cancel previous direction.

CTRL-C to quit
"""

# Define constants in one place
BASE_HOVER_SPEED = 0.164
BASE_MOVEMENT_SPEED = 0.4
SPEED_INCREMENT = 0.8


class DroneController(Node):
    def __init__(self):
        super().__init__("drone_controller")

        self.declare_parameter("cmd_vel_topic", "/cmd_vel_aqita")
        cmd_vel_topic = (
            self.get_parameter("cmd_vel_topic").get_parameter_value().string_value
        )

        self.velocity_publisher = self.create_publisher(Twist, cmd_vel_topic, 10)

        self.linear_x = 0.0
        self.linear_y = 0.0
        self.linear_z = BASE_HOVER_SPEED
        self.angular_z = 0.0

        self.is_moving_forward = False
        self.is_moving_backward = False
        self.is_moving_left = False
        self.is_moving_right = False
        self.is_rotating_left = False
        self.is_rotating_right = False
        self.is_ascending = False
        self.is_descending = False

        self.get_logger().info(
            "Drone controller initialized. Publishing to: %s" % cmd_vel_topic
        )
        self.get_logger().info(msg)

        # Clear the input buffer at startup
        termios.tcflush(sys.stdin, termios.TCIOFLUSH)

    def send_velocity_command(self):
        twist = Twist()
        twist.linear.x = self.linear_x
        twist.linear.y = self.linear_y
        twist.linear.z = self.linear_z
        twist.angular.z = self.angular_z
        self.velocity_publisher.publish(twist)

    def process_key(self, key):
        # Forward (w)
        if key == "w":
            if self.is_moving_backward:
                self.is_moving_backward = False
                self.linear_x = 0.0
            elif self.is_moving_forward:
                self.linear_x += BASE_MOVEMENT_SPEED * SPEED_INCREMENT
            else:
                self.is_moving_forward = True
                self.linear_x = BASE_MOVEMENT_SPEED

        # Backward (s)
        elif key == "s":
            if (
                self.is_moving_forward
                or self.is_moving_backward
                or self.is_moving_left
                or self.is_moving_right
                or self.is_rotating_left
                or self.is_rotating_right
            ):
                self.is_moving_forward = False
                self.is_moving_backward = False
                self.is_moving_left = False
                self.is_moving_right = False
                self.is_rotating_left = False
                self.is_rotating_right = False
                self.linear_x = 0.0
                self.linear_y = 0.0
                self.angular_z = 0.0
            else:
                self.is_moving_backward = True
                self.linear_x = -BASE_MOVEMENT_SPEED

        # Left (a)
        elif key == "a":
            if self.is_moving_right:
                self.is_moving_right = False
                self.linear_y = 0.0
            elif self.is_moving_left:
                self.linear_y += BASE_MOVEMENT_SPEED * SPEED_INCREMENT
            else:
                self.is_moving_left = True
                self.linear_y = BASE_MOVEMENT_SPEED

        # Right (d)
        elif key == "d":
            if self.is_moving_left:
                self.is_moving_left = False
                self.linear_y = 0.0
            elif self.is_moving_right:
                self.linear_y -= BASE_MOVEMENT_SPEED * SPEED_INCREMENT
            else:
                self.is_moving_right = True
                self.linear_y = -BASE_MOVEMENT_SPEED

        # Rotate Left (q)
        elif key == "q":
            if self.is_rotating_right:
                self.is_rotating_right = False
                self.angular_z = 0.0
            elif self.is_rotating_left:
                self.angular_z += BASE_MOVEMENT_SPEED * SPEED_INCREMENT
            else:
                self.is_rotating_left = True
                self.angular_z = BASE_MOVEMENT_SPEED

        # Rotate Right (e)
        elif key == "e":
            if self.is_rotating_left:
                self.is_rotating_left = False
                self.angular_z = 0.0
            elif self.is_rotating_right:
                self.angular_z -= BASE_MOVEMENT_SPEED * SPEED_INCREMENT
            else:
                self.is_rotating_right = True
                self.angular_z = -BASE_MOVEMENT_SPEED

        # Up (space)
        elif key == " ":
            if self.is_descending:
                self.is_descending = False
                self.linear_z = BASE_HOVER_SPEED  # Reset to base hovering
            elif self.is_ascending:
                self.linear_z += BASE_MOVEMENT_SPEED * SPEED_INCREMENT
            else:
                self.is_ascending = True
                self.linear_z = BASE_HOVER_SPEED + BASE_MOVEMENT_SPEED

        # Down (x)
        elif key == "x":
            if self.is_ascending:
                self.is_ascending = False
                self.linear_z = BASE_HOVER_SPEED  # Reset to base hovering
            elif self.is_descending:
                self.linear_z -= BASE_MOVEMENT_SPEED * SPEED_INCREMENT
            else:
                self.is_descending = True
                self.linear_z = BASE_HOVER_SPEED - BASE_MOVEMENT_SPEED


def main():
    rclpy.init()
    drone_controller = DroneController()

    try:
        settings = termios.tcgetattr(sys.stdin)
        old_settings = termios.tcgetattr(sys.stdin)

        tty.setraw(sys.stdin.fileno())
        termios.tcsetattr(sys.stdin, termios.TCSADRAIN, settings)

        tty.setcbreak(sys.stdin.fileno())

        # Loop at 50Hz
        rate = 0.02

        while rclpy.ok():
            if select.select([sys.stdin], [], [], 0)[0]:
                key = sys.stdin.read(1)

                if key == "\x03":  # CTRL-C
                    break

                print(f"Key pressed: {repr(key)}")
                drone_controller.process_key(key)

                termios.tcflush(sys.stdin, termios.TCIOFLUSH)


            drone_controller.send_velocity_command()
            time.sleep(rate)

    except Exception as e:
        print(f"Error: {e}")
        import traceback

        traceback.print_exc()

    finally:
        try:
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, old_settings)
        except Exception as e:
            print(f"Error restoring terminal: {e}")

        drone_controller.linear_x = 0.0
        drone_controller.linear_y = 0.0
        drone_controller.linear_z = BASE_HOVER_SPEED
        drone_controller.angular_z = 0.0
        drone_controller.send_velocity_command()

        drone_controller.destroy_node()
        rclpy.shutdown()


if __name__ == "__main__":
    main()
