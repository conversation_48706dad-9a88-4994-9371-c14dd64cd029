#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from nav_msgs.msg import Odometry
from tf2_ros import TransformBroadcaster
from geometry_msgs.msg import TransformStamped
import numpy as np
import math
import transforms3d


class SimTFPublisher(Node):
    def __init__(self):
        super().__init__("sim_tf_publisher")

        # Parameters
        self.declare_parameter("sim_odom_topic", "/sim/odom")
        self.declare_parameter("parent_frame", "map")
        self.declare_parameter("child_frame", "sim/base_link")

        self.sim_odom_topic = self.get_parameter("sim_odom_topic").value
        self.parent_frame = self.get_parameter("parent_frame").value
        self.child_frame = self.get_parameter("child_frame").value

        # Set up transform broadcaster
        self.tf_broadcaster = TransformBroadcaster(self)

        # Subscribe to odometry
        self.odom_sub = self.create_subscription(
            Odometry, self.sim_odom_topic, self.odom_callback, 10
        )

        self.get_logger().info(
            f"Publishing transforms: {self.parent_frame} -> {self.child_frame}"
        )
        self.get_logger().info(f"Using odometry from: {self.sim_odom_topic}")

    def odom_callback(self, msg):
        """Process incoming odometry message and publish as TF"""

        t = TransformStamped()
        t.header.stamp = self.get_clock().now().to_msg()
        t.header.frame_id = self.parent_frame
        t.child_frame_id = self.child_frame

        pos = msg.pose.pose.position
        quat_orig = [
            msg.pose.pose.orientation.x,
            msg.pose.pose.orientation.y,
            msg.pose.pose.orientation.z,
            msg.pose.pose.orientation.w,
        ]

        #TOFIX: These transformations doesn't seem to be right. This might be a problem with the sim robot transforms interacting with rtabmap
        
        t.transform.translation.x = pos.y
        t.transform.translation.y = -pos.x
        t.transform.translation.z = pos.z

        rotation_quat = transforms3d.quaternions.axangle2quat([0, 1, 1], math.radians(-180))

        transformed_quat = transforms3d.quaternions.qmult(rotation_quat, quat_orig)

        t.transform.rotation.x = transformed_quat[0]
        t.transform.rotation.y = transformed_quat[1]
        t.transform.rotation.z = transformed_quat[2]
        t.transform.rotation.w = transformed_quat[3]

        self.tf_broadcaster.sendTransform(t)


def main(args=None):
    rclpy.init(args=args)
    node = SimTFPublisher()
    rclpy.spin(node)
    node.destroy_node()
    rclpy.shutdown()


if __name__ == "__main__":
    main()