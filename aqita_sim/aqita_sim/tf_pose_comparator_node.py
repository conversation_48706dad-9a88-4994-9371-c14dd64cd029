#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
import tf2_ros
from tf2_ros import TransformBroadcaster
from visualization_msgs.msg import Marker, MarkerArray
from std_msgs.msg import Float64, String, Bool
import numpy as np
import math
import transforms3d
from geometry_msgs.msg import TransformStamped, Point, Pose
from rclpy.time import Time
import os
import csv
import datetime


class TFPoseComparatorNode(Node):
    def __init__(self):
        super().__init__("tf_pose_comparator")

        self.declare_parameter("reference_frame", "map")
        self.declare_parameter("measured_frame", "base_link")
        self.declare_parameter("reference_child_frame", "sim_base_link")
        self.declare_parameter("max_history", 100)
        self.declare_parameter("publish_rate", 10.0)
        self.declare_parameter("reference_color", [0.0, 0.0, 1.0, 1.0])
        self.declare_parameter("measured_color", [1.0, 0.0, 0.0, 1.0])
        self.declare_parameter("log_data", True)
        self.declare_parameter("log_directory", "/workspace/data/pose_compare")
        self.declare_parameter("log_interval", 0.5)  # 2 Hz logging rate

        self.reference_frame = self.get_parameter("reference_frame").value
        self.measured_frame = self.get_parameter("measured_frame").value
        self.reference_child_frame = self.get_parameter("reference_child_frame").value
        self.max_history = self.get_parameter("max_history").value
        self.publish_rate = self.get_parameter("publish_rate").value
        self.reference_color = self.get_parameter("reference_color").value
        self.measured_color = self.get_parameter("measured_color").value
        self.log_data = self.get_parameter("log_data").value
        self.log_directory = self.get_parameter("log_directory").value
        self.log_interval = self.get_parameter("log_interval").value

        # Set up TF buffers and listeners
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer, self)
        self.tf_broadcaster = TransformBroadcaster(self)

        # Storage for trajectories and drift values
        self.reference_poses = []
        self.measured_poses = []
        self.drift_values = []
        self.reference_latest = None
        self.measured_latest = None

        self.log_entry_count = 0

        # Publishers
        self.trajectory_pub = self.create_publisher(
            MarkerArray, "~/trajectory_markers", 10
        )
        self.pose_drift_pub = self.create_publisher(Float64, "~/position_drift", 10)
        self.orientation_drift_pub = self.create_publisher(
            Float64, "~/orientation_drift", 10
        )
        self.total_drift_pub = self.create_publisher(Float64, "~/total_drift", 10)
        self.log_file_pub = self.create_publisher(String, "~/log_file_path", 10)
        self.log_count_pub = self.create_publisher(Float64, "~/log_entry_count", 10)

        # Timer for regular processing
        self.timer = self.create_timer(1.0 / self.publish_rate, self.timer_callback)

        # Setup logging if enabled
        if self.log_data:
            self.setup_logging()
            self.log_timer = self.create_timer(
                self.log_interval, self.log_data_callback
            )

        self.get_logger().info("TF Pose Comparator Node initialized")
        self.get_logger().info(f"Reference frame: {self.reference_frame}")
        self.get_logger().info(f"Measured frame: {self.measured_frame}")
        self.get_logger().info(f"Reference child frame: {self.reference_child_frame}")
        if self.log_data:
            self.get_logger().info(f"Logging data to: {self.log_file_path}")
            self.get_logger().info(f"Logging rate: {1.0/self.log_interval} Hz")

    def setup_logging(self):
        """Set up the CSV file for logging data"""
        if not os.path.exists(self.log_directory):
            try:
                os.makedirs(self.log_directory)
                self.get_logger().info(f"Created directory: {self.log_directory}")
            except Exception as e:
                self.get_logger().error(
                    f"Failed to create directory {self.log_directory}: {e}"
                )
                self.log_directory = os.getcwd()
                self.get_logger().info(
                    f"Falling back to current directory: {self.log_directory}"
                )

        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file_path = os.path.join(
            self.log_directory, f"tf_comparator_{timestamp}.csv"
        )

        try:
            with open(self.log_file_path, "w", newline="") as csvfile:
                fieldnames = [
                    "timestamp",
                    "ref_pos_x",
                    "ref_pos_y",
                    "ref_pos_z",
                    "ref_quat_x",
                    "ref_quat_y",
                    "ref_quat_z",
                    "ref_quat_w",
                    "meas_pos_x",
                    "meas_pos_y",
                    "meas_pos_z",
                    "meas_quat_x",
                    "meas_quat_y",
                    "meas_quat_z",
                    "meas_quat_w",
                    "position_drift",
                    "orientation_drift_rad",
                    "orientation_drift_deg",
                    "total_drift",
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

            path_msg = String()
            path_msg.data = self.log_file_path
            self.log_file_pub.publish(path_msg)

            self.get_logger().info(f"Created log file: {self.log_file_path}")
        except Exception as e:
            self.get_logger().error(f"Failed to create log file: {e}")
            self.log_data = False

    def log_data_callback(self):
        """Log current data to CSV file"""
        if (
            not self.log_data
            or not self.reference_latest
            or not self.measured_latest
            or not self.drift_values
        ):
            return

        latest_drift = self.drift_values[-1]
        timestamp = self.get_clock().now().nanoseconds / 1e9

        ref_pos = self.reference_latest.position
        ref_quat = self.reference_latest.orientation
        meas_pos = self.measured_latest.position
        meas_quat = self.measured_latest.orientation

        log_data = {
            "timestamp": timestamp,
            "ref_pos_x": ref_pos.x,
            "ref_pos_y": ref_pos.y,
            "ref_pos_z": ref_pos.z,
            "ref_quat_x": ref_quat.x,
            "ref_quat_y": ref_quat.y,
            "ref_quat_z": ref_quat.z,
            "ref_quat_w": ref_quat.w,
            "meas_pos_x": meas_pos.x,
            "meas_pos_y": meas_pos.y,
            "meas_pos_z": meas_pos.z,
            "meas_quat_x": meas_quat.x,
            "meas_quat_y": meas_quat.y,
            "meas_quat_z": meas_quat.z,
            "meas_quat_w": meas_quat.w,
            "position_drift": latest_drift["position"],
            "orientation_drift_rad": latest_drift["orientation"],
            "orientation_drift_deg": math.degrees(latest_drift["orientation"]),
            "total_drift": latest_drift["total"],
        }

        try:
            with open(self.log_file_path, "a", newline="") as csvfile:
                fieldnames = list(log_data.keys())
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writerow(log_data)

                self.log_entry_count += 1
                count_msg = Float64()
                count_msg.data = float(self.log_entry_count)
                self.log_count_pub.publish(count_msg)

                if self.log_entry_count % 20 == 0:
                    self.get_logger().info(f"Logged {self.log_entry_count} data points")

        except Exception as e:
            self.get_logger().error(f"Failed to write to log file: {e}")

    def timer_callback(self):
        try:
            # Look up transforms
            reference_transform = self.tf_buffer.lookup_transform(
                self.reference_frame, self.reference_child_frame, Time().to_msg()
            )

            measured_transform = self.tf_buffer.lookup_transform(
                self.reference_frame, self.measured_frame, Time().to_msg()
            )

            # Convert to poses and store
            reference_pose = self.transform_to_pose(reference_transform)
            measured_pose = self.transform_to_pose(measured_transform)

            self.reference_latest = reference_pose
            self.measured_latest = measured_pose

            self.reference_poses.append(reference_pose)
            self.measured_poses.append(measured_pose)

            if len(self.reference_poses) > self.max_history:
                self.reference_poses.pop(0)
            if len(self.measured_poses) > self.max_history:
                self.measured_poses.pop(0)

            self.calculate_drift()
            self.publish_visualization()
            self.broadcast_error_transform()

        except (
            tf2_ros.LookupException,
            tf2_ros.ConnectivityException,
            tf2_ros.ExtrapolationException,
        ) as e:
            self.get_logger().warning(f"TF lookup failed: {e}")

    def transform_to_pose(self, transform):
        pose = Pose()

        pose.position.x = transform.transform.translation.x
        pose.position.y = transform.transform.translation.y
        pose.position.z = transform.transform.translation.z

        pose.orientation.x = transform.transform.rotation.x
        pose.orientation.y = transform.transform.rotation.y
        pose.orientation.z = transform.transform.rotation.z
        pose.orientation.w = transform.transform.rotation.w

        return pose

    def calculate_drift(self):
        if not self.reference_latest or not self.measured_latest:
            return

        # Position drift
        ref_pos = self.reference_latest.position
        meas_pos = self.measured_latest.position

        pos_drift = math.sqrt(
            (ref_pos.x - meas_pos.x) ** 2
            + (ref_pos.y - meas_pos.y) ** 2
            + (ref_pos.z - meas_pos.z) ** 2
        )

        # Orientation drift
        ref_q = self.reference_latest.orientation
        meas_q = self.measured_latest.orientation

        ref_quat = np.array([ref_q.x, ref_q.y, ref_q.z, ref_q.w])
        meas_quat = np.array([meas_q.x, meas_q.y, meas_q.z, meas_q.w])

        # Calculate the quaternion difference
        rel_quat = transforms3d.quaternions.qmult(
            transforms3d.quaternions.qinverse(ref_quat), meas_quat
        )

        # Convert to angle
        angle = 2 * math.acos(min(abs(rel_quat[3]), 1.0))

        # Total drift
        orientation_weight = 0.1
        total_drift = pos_drift + orientation_weight * angle

        # Store drift
        self.drift_values.append(
            {"position": pos_drift, "orientation": angle, "total": total_drift}
        )

        if len(self.drift_values) > self.max_history:
            self.drift_values.pop(0)

        # Publish drift values
        pos_drift_msg, orient_drift_msg, total_drift_msg = (
            Float64(),
            Float64(),
            Float64(),
        )
        pos_drift_msg.data = pos_drift
        orient_drift_msg.data = angle
        total_drift_msg.data = total_drift

        self.pose_drift_pub.publish(pos_drift_msg)
        self.orientation_drift_pub.publish(orient_drift_msg)
        self.total_drift_pub.publish(total_drift_msg)

        # Log occasionally
        if len(self.drift_values) % 10 == 0:
            self.get_logger().info(
                f"Drift: {total_drift:.4f} m (pos: {pos_drift:.4f} m, "
                f"orient: {math.degrees(angle):.2f}°)"
            )

    def publish_visualization(self):
        marker_array = MarkerArray()

        # Create trajectory markers
        if self.reference_poses:
            marker_array.markers.append(
                self.create_trajectory_marker(
                    self.reference_poses, 0, self.reference_color
                )
            )

        if self.measured_poses:
            marker_array.markers.append(
                self.create_trajectory_marker(
                    self.measured_poses, 1, self.measured_color
                )
            )

        # Create current position markers
        if self.reference_latest and self.measured_latest:
            marker_array.markers.append(
                self.create_pose_marker(
                    self.reference_latest, 2, self.reference_color, 0.3
                )
            )
            marker_array.markers.append(
                self.create_pose_marker(
                    self.measured_latest, 3, self.measured_color, 0.3
                )
            )
            marker_array.markers.append(
                self.create_connection_marker(
                    self.reference_latest.position,
                    self.measured_latest.position,
                    4,
                    [0.0, 1.0, 0.0, 0.8],
                )
            )

        self.trajectory_pub.publish(marker_array)

    def create_trajectory_marker(self, poses, marker_id, color_rgba):
        marker = Marker()
        marker.header.frame_id = self.reference_frame
        marker.header.stamp = self.get_clock().now().to_msg()
        marker.ns = "trajectories"
        marker.id = marker_id
        marker.type = Marker.LINE_STRIP
        marker.action = Marker.ADD
        marker.scale.x = 0.05

        marker.color.r = color_rgba[0]
        marker.color.g = color_rgba[1]
        marker.color.b = color_rgba[2]
        marker.color.a = color_rgba[3]

        for pose in poses:
            p = pose.position
            point = Point()
            point.x, point.y, point.z = p.x, p.y, p.z
            marker.points.append(point)

        return marker

    def create_pose_marker(self, pose, marker_id, color_rgba, size):
        marker = Marker()
        marker.header.frame_id = self.reference_frame
        marker.header.stamp = self.get_clock().now().to_msg()
        marker.ns = "poses"
        marker.id = marker_id
        marker.type = Marker.ARROW
        marker.action = Marker.ADD
        marker.pose = pose

        marker.scale.x = size
        marker.scale.y = size / 10.0
        marker.scale.z = size / 10.0

        marker.color.r = color_rgba[0]
        marker.color.g = color_rgba[1]
        marker.color.b = color_rgba[2]
        marker.color.a = color_rgba[3]

        return marker

    def create_connection_marker(self, point1, point2, marker_id, color_rgba):
        marker = Marker()
        marker.header.frame_id = self.reference_frame
        marker.header.stamp = self.get_clock().now().to_msg()
        marker.ns = "connections"
        marker.id = marker_id
        marker.type = Marker.LINE_LIST
        marker.action = Marker.ADD
        marker.scale.x = 0.02

        marker.color.r = color_rgba[0]
        marker.color.g = color_rgba[1]
        marker.color.b = color_rgba[2]
        marker.color.a = color_rgba[3]

        p1, p2 = Point(), Point()
        p1.x, p1.y, p1.z = point1.x, point1.y, point1.z
        p2.x, p2.y, p2.z = point2.x, point2.y, point2.z

        marker.points.append(p1)
        marker.points.append(p2)

        return marker

    def broadcast_error_transform(self):
        if not self.reference_latest or not self.measured_latest:
            return

        error_tf = TransformStamped()
        error_tf.header.stamp = self.get_clock().now().to_msg()
        error_tf.header.frame_id = self.reference_child_frame
        error_tf.child_frame_id = "error_frame"

        # Calculate the error transformation (from reference to measured)
        ref_pos = np.array(
            [
                self.reference_latest.position.x,
                self.reference_latest.position.y,
                self.reference_latest.position.z,
            ]
        )

        meas_pos = np.array(
            [
                self.measured_latest.position.x,
                self.measured_latest.position.y,
                self.measured_latest.position.z,
            ]
        )

        ref_quat = np.array(
            [
                self.reference_latest.orientation.x,
                self.reference_latest.orientation.y,
                self.reference_latest.orientation.z,
                self.reference_latest.orientation.w,
            ]
        )

        meas_quat = np.array(
            [
                self.measured_latest.orientation.x,
                self.measured_latest.orientation.y,
                self.measured_latest.orientation.z,
                self.measured_latest.orientation.w,
            ]
        )

        # Calculate position error
        pos_error = meas_pos - ref_pos

        # Calculate orientation error
        orient_error = transforms3d.quaternions.qmult(
            transforms3d.quaternions.qinverse(ref_quat), meas_quat
        )

        # Set the transform
        error_tf.transform.translation.x = pos_error[0]
        error_tf.transform.translation.y = pos_error[1]
        error_tf.transform.translation.z = pos_error[2]

        error_tf.transform.rotation.x = orient_error[0]
        error_tf.transform.rotation.y = orient_error[1]
        error_tf.transform.rotation.z = orient_error[2]
        error_tf.transform.rotation.w = orient_error[3]

        self.tf_broadcaster.sendTransform(error_tf)


def main(args=None):
    rclpy.init(args=args)
    node = TFPoseComparatorNode()

    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()


if __name__ == "__main__":
    main()
