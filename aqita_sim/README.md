# Aqita Sim

A ROS2 package for simulation analysis and validation tools.

## Components

### SimTFPublisher

The SimTFPublisher transforms simulation odometry to match the ROS coordinate system. It's essential for aligning simulator-generated poses with ROS conventions.

#### Features

- Transforms odometry from simulator coordinate system to ROS coordinate system
- Handles both position and orientation transformations correctly
- Supports configurable transformation parameters
- Publishes transformed poses as TF frames

### TF Pose Comparator

The TF Pose Comparator is a tool for comparing TF frames, calculating drift, and visualizing trajectories. It's particularly useful for comparing ground truth poses from simulation with estimated positions.

#### Features

- Compares two TF frames (reference and measured)
- Calculates positional and orientation drift metrics
- Visualizes trajectories and current positions in RViz
- Provides real-time drift measurements for monitoring
- Logs drift data to CSV files for offline analysis
- Broadcasts error transform between reference and measured frames

### Drone Teleop

The Drone Teleop node provides intuitive keyboard control for drone movement in the simulator.

#### Features

- WASD keys for forward/backward/left/right movement
- QE keys for rotation control
- Space for ascending, X for descending
- Incremental speed control (press the same key multiple times)
- Smooth motion transitions
- Customizable speed parameters

## Usage

### Launch the Complete System

```bash
# Basic usage with default parameters
ros2 launch aqita_sim tf_pose_comparison.launch.py

# Custom configuration
ros2 launch aqita_sim tf_pose_comparison.launch.py \
  sim_odom_topic:=/custom/sim/odom \
  measured_frame:=custom_base_link \
  reference_child_frame:=custom_sim_base_link
```

### Launch Components Individually

#### SimTFPublisher

```bash
ros2 run aqita_sim sim_tf_publisher_node --ros-args \
  -p sim_odom_topic:=/sim/odom \
  -p parent_frame:=map \
  -p child_frame:=sim/base_link
```

#### TF Pose Comparator

```bash
ros2 run aqita_sim tf_pose_comparator_node --ros-args \
  -p reference_frame:=map \
  -p measured_frame:=base_link \
  -p reference_child_frame:=sim/base_link \
  -p log_directory:=/path/to/logs
```

#### Drone Teleop

```bash
# Basic usage (default topic is /cmd_vel_aqita)
ros2 run aqita_sim drone_teleop

# Custom velocity topic
ros2 run aqita_sim drone_teleop --ros-args -p cmd_vel_topic:=/custom/cmd_vel
```

##### Drone Control Keys

```
   q       w      e
     a     s     d
```

- `w/a/s/d`: Forward/left/backward/right movement
- `q/e`: Rotate left/right
- `space`: Ascend (move up)
- `x`: Descend (move down)
- `s` (when hovering): Start moving backward
- `s` (when moving): Stop all movement (hover)

Key behavior:
- Press a key once to start moving in that direction
- Press the same key again to increase speed by 20%
- Press the opposite direction key to stop and cancel previous movement
- Press `Ctrl+C` to quit

### Visualization in RViz

1. Open RViz: `ros2 run rviz2 rviz2`
2. Add a MarkerArray display
3. Set the topic to `/tf_pose_comparator/trajectory_markers`
4. Add a TF display to visualize the pose frames
5. Set the fixed frame to `map`

### Analyze Logged Data

The TF Pose Comparator logs drift metrics to CSV files in the specified directory (default: `/workspace/data/pose_compare`). You can analyze these logs with your preferred data analysis tools.

```bash
# Check the latest log file
ls -lt /workspace/data/pose_compare/

# Plot data using your favorite tool (e.g., Python with pandas & matplotlib)
python3 analyze_drift.py /workspace/data/pose_compare/tf_comparator_20250430_123456.csv
```

## Configuration Parameters

### SimTFPublisher Parameters

- `sim_odom_topic`: Topic for simulation odometry (default: `/sim/odom`)
- `parent_frame`: Parent TF frame (default: `map`)
- `child_frame`: Child TF frame (default: `sim/base_link`)

### TF Pose Comparator Parameters

- `reference_frame`: Common reference frame (default: `map`)
- `measured_frame`: Frame to be evaluated (default: `base_link`)
- `reference_child_frame`: Ground truth frame (default: `sim/base_link`)
- `max_history`: Maximum number of poses to store in history (default: 100)
- `publish_rate`: Rate at which to publish visualizations in Hz (default: 10.0)
- `reference_color`: Color for reference trajectory [R,G,B,A] (default: [0.0, 0.0, 1.0, 1.0] - blue)
- `measured_color`: Color for measured trajectory [R,G,B,A] (default: [1.0, 0.0, 0.0, 1.0] - red)
- `log_data`: Enable/disable data logging (default: true)
- `log_directory`: Directory to save CSV logs (default: `/workspace/data/pose_compare`)
- `log_interval`: Logging interval in seconds (default: 0.5, which is 2 Hz)

### Drone Teleop Parameters

- `cmd_vel_topic`: Topic for publishing velocity commands (default: `/cmd_vel_aqita`)

## Published Topics

### SimTFPublisher

- Publishes transformed odometry data as TF frames

### TF Pose Comparator

- `~/trajectory_markers` (visualization_msgs/MarkerArray): Visualization markers for RViz
- `~/position_drift` (std_msgs/Float64): Current positional drift
- `~/orientation_drift` (std_msgs/Float64): Current orientation drift
- `~/total_drift` (std_msgs/Float64): Combined drift metric
- `~/log_file_path` (std_msgs/String): Path to the current log file
- `~/log_entry_count` (std_msgs/Float64): Number of entries written to the log file

### Drone Teleop

- Publishes to the configured velocity topic (default: `/cmd_vel_aqita`) using the geometry_msgs/Twist message type

## TF Frames

- `map`: Common reference frame
- `base_link`: Measured pose frame (e.g., from RTAB-Map)
- `sim/base_link`: Reference pose frame (transformed from simulation)
- `error_frame`: A frame showing the error between reference and measured poses

## Dependencies

- ROS2 (tested with Humble)
- Python dependencies:
  - numpy
  - transforms3d

## Installation

```bash
# Install Python dependencies
pip install transforms3d numpy

# Build the package
cd ~/your_workspace
colcon build --packages-select aqita_sim
```