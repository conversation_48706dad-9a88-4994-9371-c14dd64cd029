# AQITA GitHub Repository Structure and General Rules for ROS2 Packages

## [aqita-ros](https://github.com/UVIONIX/aqita-ros)

The repository will contain a collection of custom ROS2 packages for the AQITA warehouse inventory management UAV.
These packages should only be custom ones and generic.
They may depend on other open-source ROS2 packages that are forked into our GitHub repository,
but those forked packages should not be inside the `aqita-ros` repository.

The `aqita-ros` repository has to contain the main launch file (and any related sub-launch files with some exceptions)
for starting all system components.
The `aqita-ros` repository has to contain all configuration files (yaml, urdf, xacro) for specific system components.
However, these configuration files should not be related to the operation of other specific ROS2 packages
that have their own repository,
i.e. the navigation configuration file or nvblox configuration file should not be inside the `aqita-ros` repository.

## [nav2-aqita](https://github.com/UVIONIX/nav2-aqita)

The repository contains ROS2 packages related to the navigation algorithms.
Some of them can be our development, while others can be modifications of existing nav2 packages.
The `nav2-aqita` repository can contain a branch in which we have a fork (or copy) of every nav2 package
that we will ever need.
The main `nav2-aqita` branch has to contain only our custom packages and nav2 packages that are modified for our needs.
Every package in the main branch of `nav2-aqita` has to be a submodule of the [aqita-ros](#aqita-ros) repository.
The launch file for the navigation algorithms as well as any configuration files
have to be in the `nav2-aqita` repository.
They have to be referenced by the main system launch file, which is in the [aqita-ros](#aqita-ros) repository. 

## [nvblox-aqita](https://github.com/UVIONIX/nvblox-aqita)

The repository contains ROS2 packages related to running the NVBLOX occupancy mapping algorithm.
This package should follow the same guidelines, defined for the [nav2-aqita](#nav2-aqita) repo.
The main branch has to contain only custom or modified packages, which have to be submodules of the [aqita-ros](#aqita-ros)
repository.

## Shared configs

Since the [aqita-ros](#aqita-ros) repository can contain configuration files for some system components, such as cameras,
if [nav2-aqita](#nav2-aqita) requires some specific camera configuration it should provide a configuration file
which will overwrite the default configuration file in the [aqita-ros](#aqita-ros) repository. 
In this way it will be the main system launch file's responsibility to load the default camera configuration file
and then overwrite it with the configuration file from the [nav2-aqita](#nav2-aqita) repository. 
If another repo (such as [nvblox-aqita](#nvblox-aqita)) requires camera configuration 
which is in conflict with the configuration from the [nav2-aqita](#nav2-aqita) repo, 
then the conflict should be resolved by the repository maintainers. 
Ideally, the camera configuration file inside the [aqita-ros](#aqita-ros) repo 
should be OK for all packages that rely on the cameras to operate.

## Future repositories

Any future repositories should follow the general guidelines defined above.

# Naming conventions

The repositories should be named in lowercase letters, with a dash between different words (such as [aqita-ros](#aqita-ros),
[nav2-aqita](#nav2-aqita), [nvblox-aqita](#nvblox-aqita)).

The packages developed by us should be named in lowercase letters, with an underscore between
different words (such as `aqita_launch`, `aqita_topic_tools`).
Also, these packages should contain `aqita` in their name,
which will differentiate them form any potential open-source packages with similar or identical names.
Care must be exercised when naming packages.
The package name should contain a keyword from the name of the parent repository,
unique for the repository's main functionality.
In this way there shouldn't be packages with duplicate names, which are in different repositories.
For example, if we create a custom path following algorithm inside the [nav2-aqita](#nav2-aqita) repository the
algorithm source code has to be a ROS2 package named `nav2_aqita_super_cool_path_following_algorithm`.

# General repository folder structure

Each repository has to have a dedicated launch ROS2 package,
which contains launch and configuration files for the specific functionalities.
Inside this package there have to be folders named `launch`, `config`, etc.

The `launch` folder may have subfolders for specific components i.e. sensors, test
cases and other related subpackages.
The `config` folder should contain configuration files (yaml, xacro)
and can also contain subfolders for specific components and functionalities.

For example, the [nav2-aqita](#nav2-aqita) repo has to have a package named `nav2_aqita_bringup`.
The `launch` folder will contain the python file which starts all navigation-related nodes.

# Type of ROS2 packages

There are two types of ROS2 packages - python and C++.
Packages can be created by using ROS2 commands in a terminal.

**All packages that we create should be C++ packages**
even if they contain only python files and do not contain any .cpp source files.

More information on ROS2 packages and their structure can be found here
https://docs.ros.org/en/humble/Tutorials/Beginner-Client-Libraries/Creating-Your-First-ROS2-Package.html

When creating new ROS2 packages using the ROS2 terminal commands you can skip the section which specifies the license.

# Example setup of a new repository

Suppose that we want to create a new repository for the RTAB-Map VSLAM algorithm.
We name this repository `rtabmap-aqita`.
On the main branch we create a package which will contain the associated launch and configuration files.
The name of this package should be `rtabmap_aqita_bringup`.

We create this package by using the command
```bash
ros2 pkg create --build-type ament_cmake rtabmap_aqita_bringup
```

Inside the `rtabmap_aqita_bringup` package we create the folders `launch` and `config`, which will contain launch .py
files and any configuration files. Inside the launch folder we can create a main launch file for the rtab-map algorithm
and name it `rtabmap.launch.py`. The launch folder can contain a subfolder for launching different test cases. We create
the subfolder `test_cases` and add two launch files, named `test_rtabmap_rgbd_vslam.launch.py` 
and `test_rtabmap_stereo_vslam.launch.py`. 
Inside the config folder we create a `rtabmap_base_config.yaml` configuration file,
which can be referenced by the `.launch.py` files for setting-up the base (or common) algorithm parameters.
We can also create configuration files for specific scenarios, such as RGBD or stereo VSLAM types, 
and use those files to overwrite some of the base configuration parameters. 
We name these files `rtabmap_rgbd_config.yaml` and `rtabmap_stereo_config.yaml`. 
With this setup the `rtabmap_aqita_bringup` repo will have the following structure:

	rtabmap_aqita_bringup
		CMakeLists.txt
		package.xml
		include
		src
		launch
			rtabmap.launch.py
			test_cases
				test_rtabmap_rgbd_vslam.launch.py
				test_rtabmap_stereo_vslam.launch.py
		config
			rtabmap_base_config.yaml
			rtabmap_rgbd_config.yaml
			rtabmap_stereo_config.yaml

If we want the source code of the RTAB-Map package we should create a new branch of the `rtabmap-aqita` repo
and fork (or copy) the source code within this new branch.
If we need to modify the RTAB-Map source code we either have to create a
new custom RTAB-Map package and placed it in the main `rtabmap-aqita` branch, or place the modified package in the main
branch. Since the RTAB-Map source code is organized as different ROS2 packages the proper action to be chosen will
depend on the specifics of the necessary modification.

# Notes for open-source ROS2 package modifications

We should try to avoid modifying the source code of any open-source ROS2 packages that we are using.
Many ROS2 packages (such as NAV2 and MAVROS) are modular and plugin based.
This means that if we are missing a needed functionality,
we can probably add it by creating a custom plugin for that particular package.
The custom plugin should be a ROS2 package on the main branch of the respective repository.
If we do not modify their source code these ROS2 packages can be built inside the docker image that we are using.
In some cases, the only way to achieve a given functionality, or to improve the performance of a given algorithm,
would be to modify its source code.

For example, the integration of semantic segmentation functionality inside the RTAB-Map VSLAM algorithm
will probably not be possible without source code modification.
This will require rebuilding the RTAB-Map package
which may create conflicts if this package is already build inside the docker image.

In general, if we modify the source code of any packages
they should not be build inside the docker image that we are using.
