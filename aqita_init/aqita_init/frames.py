# TODO: Licence description

# NOTE: This file defines the frame names associated various system components and means of obtaining these frame names by function calls.
# Import the file where needed to gain access to the API functions

from collections import namedtuple

# Frame suffixes for a RealSense camera
RS_CAMERA_FRAMES_SUFFIXES = {
    'left_cam_frame': '_infra1_frame',
    'right_cam_frame': '_infra2_frame',
    'left_cam_optical_frame': '_infra1_optical_frame',
    'right_cam_optical_frame': '_infra2_optical_frame',
    'depth_frame': '_depth_frame',
    'depth_optical_frame': '_depth_optical_frame',
    'rgb_frame': '_color_frame',
    'rgb_optical_frame': '_color_optical_frame',
    'imu_frame': '_imu',
    'imu_optical_frame': '_imu_optical_frame',
    'accel_frame': '_accel_frame',
    'accel_optical_frame': '_accel_optical_frame',
    'gyro_frame': '_gyro_frame',
    'gyro_optical_frame': '_gyro_optical_frame',
}

# Get Realsense frames
def get_realsense_camera_frames(cam_name: str):
    '''
    Get the RealSense camera frames associated with a given camera name
    '''
    cam_frames = namedtuple(
        'cam_frames',
        [
            'left_cam_frame',
            'right_cam_frame',
            'left_cam_optical_frame',
            'right_cam_optical_frame',
            'depth_frame',
            'depth_optical_frame',
            'rgb_frame',
            'rgb_optical_frame',
            'imu_frame',
            'imu_optical_frame',
            'accel_frame',
            'accel_optical_frame',
            'gyro_frame',
            'gyro_optical_frame',
        ],
    )
    cam_name = cam_name.strip().strip('/').strip()
    return cam_frames(
        cam_name + RS_CAMERA_FRAMES_SUFFIXES['left_cam_frame'],
        cam_name + RS_CAMERA_FRAMES_SUFFIXES['right_cam_frame'],
        cam_name + RS_CAMERA_FRAMES_SUFFIXES['left_cam_optical_frame'],
        cam_name + RS_CAMERA_FRAMES_SUFFIXES['right_cam_optical_frame'],
        cam_name + RS_CAMERA_FRAMES_SUFFIXES['depth_frame'],
        cam_name + RS_CAMERA_FRAMES_SUFFIXES['depth_optical_frame'],
        cam_name + RS_CAMERA_FRAMES_SUFFIXES['rgb_frame'],
        cam_name + RS_CAMERA_FRAMES_SUFFIXES['rgb_optical_frame'],
        cam_name + RS_CAMERA_FRAMES_SUFFIXES['imu_frame'],
        cam_name + RS_CAMERA_FRAMES_SUFFIXES['imu_optical_frame'],
        cam_name + RS_CAMERA_FRAMES_SUFFIXES['accel_frame'],
        cam_name + RS_CAMERA_FRAMES_SUFFIXES['accel_optical_frame'],
        cam_name + RS_CAMERA_FRAMES_SUFFIXES['gyro_frame'],
        cam_name + RS_CAMERA_FRAMES_SUFFIXES['gyro_optical_frame'],
    )

