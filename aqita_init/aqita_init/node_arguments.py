# TODO: Licence description

# NOTE: This file defines the launch arguments of various system nodes.
# Import the file where needed to gain access to the launch arguments definitions

from dataclasses import dataclass


# NVBLOX launch arguments
@dataclass
class NvbloxLaunchArguments:
    node_name: str
    camera_names: str
    params_yaml_path: str
    attach_to_shared_component_container: str
    component_container_name: str
    global_odometry_topic: str
    esdf_slice_frame: str
    esdf_slice_offset: str


nvblox_launch_arguments = NvbloxLaunchArguments(
    node_name='node_name',
    camera_names='camera_names',
    params_yaml_path='params_yaml_path',
    attach_to_shared_component_container='attach_to_shared_component_container',
    component_container_name='component_container_name',
    global_odometry_topic='global_odometry_topic',
    esdf_slice_frame='esdf_slice_frame',
    esdf_slice_offset='esdf_slice_offset',
)
