# TODO: Licence description

# NOTE: This file defines the topic names of various system components and means of obtaining these topic names by function calls.
# Import the file where needed to gain access to the API functions

from collections import namedtuple

# Topics suffixes for a RealSense camera
RS_CAMERA_TOPIC_SUFFIXES = {
    'left_img_topic': '/infra1/image_rect_raw',
    'right_img_topic': '/infra2/image_rect_raw',
    'rgb_img_topic': '/color/image_raw',
    'depth_img_topic': '/depth/image_rect_raw',
    'imu_topic': '/imu',
    'left_cam_info_topic': '/infra1/camera_info',
    'right_cam_info_topic': '/infra2/camera_info',
    'rgb_cam_info_topic': '/color/camera_info',
    'depth_cam_info_topic': '/depth/camera_info',
}


# Get Realsense camera topics
def get_realsense_camera_topics(cam_name: str):
    '''
    Get the RealSense camera topics associated with a given camera name
    '''
    cam_topics = namedtuple(
        'cam_topics',
        [
            'left_img_topic',
            'right_img_topic',
            'rgb_img_topic',
            'depth_img_topic',
            'imu_topic',
            'left_cam_info_topic',
            'right_cam_info_topic',
            'rgb_cam_info_topic',
            'depth_cam_info_topic',
        ],
    )
    cam_name = '/' + cam_name.strip().strip('/').strip()
    return cam_topics(
        cam_name + RS_CAMERA_TOPIC_SUFFIXES['left_img_topic'],
        cam_name + RS_CAMERA_TOPIC_SUFFIXES['right_img_topic'],
        cam_name + RS_CAMERA_TOPIC_SUFFIXES['rgb_img_topic'],
        cam_name + RS_CAMERA_TOPIC_SUFFIXES['depth_img_topic'],
        cam_name + RS_CAMERA_TOPIC_SUFFIXES['imu_topic'],
        cam_name + RS_CAMERA_TOPIC_SUFFIXES['left_cam_info_topic'],
        cam_name + RS_CAMERA_TOPIC_SUFFIXES['right_cam_info_topic'],
        cam_name + RS_CAMERA_TOPIC_SUFFIXES['rgb_cam_info_topic'],
        cam_name + RS_CAMERA_TOPIC_SUFFIXES['depth_cam_info_topic'],
    )


# Topics suffixes for a NVBLOX node
NVBLOX_TOPIC_SUFFIXES = {'static_map_slice_topic': '/static_map_slice'}


# Get NVBLOX node topics
def get_nvblox_node_topics(node_name: str):
    '''
    Get the topics assosiated with a NVBLOX node with a given name
    '''
    node_topics = namedtuple('node_topics', ['static_map_slice_topic'])
    node_name = '/' + node_name.strip().strip('/').strip()
    return node_topics(node_name + NVBLOX_TOPIC_SUFFIXES['static_map_slice_topic'])
