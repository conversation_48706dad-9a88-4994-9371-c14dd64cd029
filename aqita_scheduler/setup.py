from setuptools import find_packages, setup
from glob import glob

package_name = 'aqita_scheduler'

setup(
    name=package_name,
    version='0.0.1',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages', ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        ('lib/' + package_name, glob(f'{package_name}/*.py')),
        ('share/' + package_name, glob(f'{package_name}/*.py')),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='To<PERSON> Markov',
    maintainer_email='<EMAIL>',
    description='Mission scheduler node that triggers mission start via the state machine',
    license='Proprietary',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'mission_scheduler = aqita_scheduler.mission_scheduler:main'
        ],
    },
)
