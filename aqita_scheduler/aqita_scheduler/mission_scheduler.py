import rclpy
from rclpy.node import Node
from aqita_interfaces.srv import MissionStartTrigger
from aqita_interfaces.srv import MissionStartAck

class MissionScheduler(Node):
    def __init__(self):
        super().__init__('mission_scheduler')

        self.declare_parameter('mission_id', 0)
        self.declare_parameter('drone_id', "0")

        drone_id = self.get_parameter('drone_id').get_parameter_value().string_value
        mission_id = self.get_parameter('mission_id').get_parameter_value().integer_value

        self.mission_trigger_client = self.create_client(MissionStartTrigger, 'mission_start_request')
        self.mission_ack_client = self.create_client(MissionStartAck, 'mission_start_ack')

        # Wait for service to be available
        trigger_ready = self.mission_trigger_client.wait_for_service(timeout_sec=3.0)
        if not trigger_ready:
            self.get_logger().info('Mission trigger service not available!')
            raise RuntimeError("Mission trigger service not available!")
        

        ack_ready = self.mission_ack_client.wait_for_service(timeout_sec=3.0)
        if not ack_ready:
            self.get_logger().info('Mission ACK service not available!')
            raise RuntimeError("Mission ACK service not available!")
        

        # Call mission ack service with timeout
        ack_request = MissionStartAck.Request()
        ack_request.drone_id = str(drone_id)
        ack_request.mission_id = str(mission_id)

        trigger_request = MissionStartTrigger.Request()
        trigger_request.drone_id = str(drone_id)
        trigger_request.mission_id = str(mission_id)

        ack_future = self.mission_ack_client.call_async(ack_request)
        rclpy.spin_until_future_complete(self, ack_future, timeout_sec=5.0)
        if ack_future.done():
            ack_response: MissionStartAck.Response = ack_future.result()
            if ack_response.ack:
                # Ack is true, proceed to call mission trigger service
                trigger_future = self.mission_trigger_client.call_async(trigger_request)
                rclpy.spin_until_future_complete(self, trigger_future, timeout_sec=3.0)
                if trigger_future.done():
                    trigger_response = trigger_future.result()
                    if trigger_response is not None:
                        self.get_logger().info(f'Mission Trigger Result: {trigger_response}')
                    else:
                        self.get_logger().error('Mission Trigger service call failed')
                else:
                    self.get_logger().error('Mission Trigger service call timed out')
                    raise RuntimeError("Mission Trigger timeout")
            else:
                self.get_logger().warning('Mission ACK not received or false')
        else:
            self.get_logger().error('Mission ACK service call timed out')
            raise RuntimeError("Mission ACK timeout")

def main(args=None):
    rclpy.init(args=args)
    
    scheduler = MissionScheduler()
    
    scheduler.destroy_node()
    rclpy.shutdown()

if __name__ == '__main__':
    main()