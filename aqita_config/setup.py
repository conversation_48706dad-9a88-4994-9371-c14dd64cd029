import os
from setuptools import find_packages, setup
from glob import glob

package_name = 'aqita_config'

setup(
    name=package_name,
    version='0.1.0',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages',['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        (os.path.join('share', package_name, 'robot/xoss_3rs/params/deploy'), glob(os.path.join(package_name, 'robot', 'xoss_3rs', 'params', 'deploy', '*.yaml'))),
        (os.path.join('share', package_name, 'robot/xoss_3rs/params/rosbag'), glob(os.path.join(package_name, 'robot', 'xoss_3rs', 'params', 'rosbag', '*.yaml'))),
        (os.path.join('share', package_name, 'robot/xoss_3rs/params/sim'), glob(os.path.join(package_name, 'robot', 'xoss_3rs', 'params', 'sim', '*.yaml'))),
        (os.path.join('share', package_name, 'robot/xoss_3rs/params/test'), glob(os.path.join(package_name, 'robot', 'xoss_3rs', 'params', 'test', '*.yaml'))),
        (os.path.join('share', package_name, 'robot', 'xoss_3rs'), glob(os.path.join(package_name, 'robot', 'xoss_3rs', '*.xacro'))),
        (os.path.join('share', package_name, 'sensors', 'realsense'), glob(os.path.join(package_name, 'sensors', 'realsense', '*.yaml'))),
        (os.path.join('share', package_name, 'sensors', 'realsense'), glob(os.path.join(package_name, 'sensors', 'realsense', '*.xacro'))),
        (os.path.join('share', package_name, 'sensors', 'realsense'), glob(os.path.join(package_name, 'sensors', 'realsense', '*.launch.py'))),
        (os.path.join('share', package_name, 'slam', 'isaac_ros_vslam'), glob(os.path.join(package_name, 'slam', 'isaac_ros_vslam', '*.yaml'))),
        (os.path.join('share', package_name, 'slam', 'rtabmap'), glob(os.path.join(package_name, 'slam', 'rtabmap', '*.yaml'))),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='Stanislav Darmonski',
    maintainer_email='<EMAIL>',
    description='TODO: Package description',
    license='TODO: License declaration',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
        ],
    },
)
