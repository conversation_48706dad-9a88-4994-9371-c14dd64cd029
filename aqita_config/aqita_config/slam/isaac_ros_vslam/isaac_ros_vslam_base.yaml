

# Common VSLAM configuration parameters

---
/**:
  ros__parameters:

    num_cameras: 2 # Number of cameras used. Set to 2 for a single stereo camera. Maximum value is 32
    sync_matching_threshold_ms: 20.0 # Maximum duration between image timestamps to still consider them synced
    enable_image_denoising: false # If enabled, input images are denoised
    rectified_images: true # Flag to mark if the incoming images are rectified or raw
    enable_ground_constraint_in_odometry: false # If enabled, odometry poses will be modified such that the camera moves on a horizontal plane
    enable_ground_constraint_in_slam: false # If enabled, SLAM poses will be modified such that the camera moves on a horizontal plane
    enable_localization_n_mapping: false # If enabled, SLAM mode is on. If disabled, only odometry is on
    enable_imu_fusion: true # If enabled, IMU data is used
    gyro_noise_density: 0.000244
    gyro_random_walk: 0.000019393
    accel_noise_density: 0.001862
    accel_random_walk: 0.003
    calibration_frequency: 200.0
    image_jitter_threshold_ms: 40.0 # Default: 34.0
    imu_jitter_threshold_ms: 40.0
    img_mask_top: 0
    img_mask_bottom: 0
    img_mask_left: 0
    img_mask_right: 0
    localize_on_startup: false
    map_frame: "map"
    odom_frame: "odom"
    base_frame: "base_link"
    camera_optical_frames: ""
    imu_frame: "imu"
    image_buffer_size: 200 # Buffer size used by the image synchronizer
    imu_buffer_size: 100 # Buffer size used by the IMU sequencer
    image_qos: "DEFAULT" # Available option is "SENSOR_DATA"
    imu_qos: "DEFAULT" # Available option is "SENSOR_DATA"
    override_publishing_stamp: false
    publish_map_to_odom_tf: true
    publish_odom_to_base_tf: true
    invert_map_to_odom_tf: false
    invert_odom_to_base_tf: false
    enable_slam_visualization: true
    enable_observations_view: true
    enable_landmarks_view: true
    path_max_size: 2048
    verbosity: 0
    enable_debug_mode: false
    debug_dump_path: "/tmp/cuvslam"

