# TODO: License description

import os, sys
from ament_index_python.packages import get_package_share_directory
from typing import List
from logging import Logger
from launch import Action
from launch_ros.descriptions import ComposableNode
from launch_ros.actions import LoadComposableNodes
from aqita_config.base_classes import OdomType, StereoCamera, OdomConfiguration, load_yaml_file_parameters

class IsaacRosOdomConfiguration(OdomConfiguration):
    """Isaac-ROS odometry configuration class"""

    ODOM_NODE_NAME = 'isaac_ros_odom_node'
    BASE_PARAMS_FILE = 'isaac_ros_vslam_base.yaml'

    def __init__(self, logger: Logger,
                 stereo_camera_list: List[StereoCamera],
                 robot_base_link: str,
                 use_imu: bool,
                 imu_topic: str | None,
                 imu_frame_id: str | None,
                 shared_container_name: str,
                 custom_odom_params_filepath: str | None = None):
        """
        Constructor of an Isaac-ROS odometry configuration class
        
        Args:
            logger: logger object to use for logging
            stereo_camera_list: list of stereo cameras to use for odometry
            robot_base_link: name of the robot base link
            use_imu: whether to use IMU messages
            imu_topic: topic on which the IMU messages are being published
            imu_frame_id: frame in which the IMU data is published
            shared_container_name: name of the shared container to which the odometry node will be attached
            custom_odom_params_filepath: path to a yaml file with custom odometry parameters, containing the field 'visual_odometry'
        """

        # Initialize the base class
        super().__init__(odom_type=OdomType.STEREO)
        logger.info('Initializing Isaac-ROS odometry configuration ...')

        # Initialize the base odometry parameters
        base_params_filepath = os.path.join(get_package_share_directory('aqita_config'), 'slam', 'isaac_ros_vslam', self.BASE_PARAMS_FILE)

        # Initialize the odometry parameter overwrites
        if custom_odom_params_filepath is not None:
            logger.info('Loading Isaac-ROS odometry parameter overwrites ...')
            odom_param_overwrites = load_yaml_file_parameters(fields=['visual_odometry'], filepath=custom_odom_params_filepath, logger=logger)

            if odom_param_overwrites is None:
                logger.error('Error loading Isaac-ROS odometry parameter overwrites. Aborting launch!')
                sys.exit()

            odom_param_overwrites = odom_param_overwrites[0]
        else:
            odom_param_overwrites = {}

        # Check IMU fusion capability
        if use_imu and imu_topic is not None and imu_frame_id is not None:
            enable_imu_fusion = True
        else:
            enable_imu_fusion = False

        logger.info(f'IMU usage in Isaac-ROS odometry set to: {enable_imu_fusion}')

        # Create the node remappings
        cam_index = 0
        remappings = []
        camera_optical_frames = []

        for cam in stereo_camera_list:
            camera_optical_frames.append(cam.left_cam_optical_frame)
            camera_optical_frames.append(cam.right_cam_optical_frame)
            remappings.append(('visual_slam/image_'         +str(cam_index),    cam.left_image_topic))
            remappings.append(('visual_slam/camera_info_'   +str(cam_index),    cam.left_cam_info_topic))
            remappings.append(('visual_slam/image_'         +str(cam_index+1),  cam.right_image_topic))
            remappings.append(('visual_slam/camera_info_'   +str(cam_index+1),  cam.right_cam_info_topic))

            logger.info(f'Camera with name {cam.camera_name} added to odom node launch description with optical frames and remappings:')
            logger.info(f'\t {camera_optical_frames[cam_index]}')
            logger.info(f'\t {camera_optical_frames[cam_index+1]}')
            for j in range(len(remappings)-4,len(remappings)):
                logger.info(f'\t {remappings[j]}')

            cam_index += 2

        output_topics_prefix = '/isaac_ros'
        self._odom_topic = output_topics_prefix+'_visual_slam/tracking/odometry'
        if enable_imu_fusion:
            remappings.append(('visual_slam/imu', imu_topic))

        remappings.append(('visual_slam/tracking/odometry',                 self._odom_topic))
        remappings.append(('visual_slam/tracking/vo_pose_covariance',       output_topics_prefix+'_visual_slam/tracking/vo_pose_covariance'))
        remappings.append(('visual_slam/tracking/vo_pose',                  output_topics_prefix+'_visual_slam/tracking/vo_pose'))
        remappings.append(('visual_slam/tracking/slam_path',                output_topics_prefix+'_visual_slam/tracking/slam_path'))
        remappings.append(('visual_slam/tracking/vo_path',                  output_topics_prefix+'_visual_slam/tracking/vo_path'))
        remappings.append(('visual_slam/status',                            output_topics_prefix+'_visual_slam/status'))
        remappings.append(('visual_slam/vis/gravity',                       output_topics_prefix+'_visual_slam/vis/gravity'))
        remappings.append(('visual_slam/vis/landmarks_cloud',               output_topics_prefix+'_visual_slam/vis/landmarks_cloud'))
        remappings.append(('visual_slam/vis/localizer',                     output_topics_prefix+'_visual_slam/vis/localizer'))
        remappings.append(('visual_slam/vis/localizer_loop_closure_cloud',  output_topics_prefix+'_visual_slam/vis/localizer_loop_closure_cloud'))
        remappings.append(('visual_slam/vis/localizer_map_cloud',           output_topics_prefix+'_visual_slam/vis/localizer_map_cloud'))
        remappings.append(('visual_slam/vis/localizer_observations_cloud',  output_topics_prefix+'_visual_slam/vis/localizer_observations_cloud'))
        remappings.append(('visual_slam/vis/loop_closure_cloud',            output_topics_prefix+'_visual_slam/vis/loop_closure_cloud'))
        remappings.append(('visual_slam/vis/observations_cloud',            output_topics_prefix+'_visual_slam/vis/observations_cloud'))
        remappings.append(('visual_slam/vis/pose_graph_edges',              output_topics_prefix+'_visual_slam/vis/pose_graph_edges'))
        remappings.append(('visual_slam/vis/pose_graph_edges2',             output_topics_prefix+'_visual_slam/vis/pose_graph_edges2'))
        remappings.append(('visual_slam/vis/pose_graph_nodes',              output_topics_prefix+'_visual_slam/vis/pose_graph_nodes'))
        remappings.append(('visual_slam/vis/slam_odometry',                 output_topics_prefix+'_visual_slam/vis/slam_odometry'))
        remappings.append(('visual_slam/vis/velocity',                      output_topics_prefix+'_visual_slam/vis/velocity'))
        remappings.append(('visual_slam/initial_pose',                      output_topics_prefix+'_visual_slam/initial_pose'))
        remappings.append(('visual_slam/trigger_hint',                      output_topics_prefix+'_visual_slam/trigger_hint'))
        remappings.append(('visual_slam/reset',                             output_topics_prefix+'_visual_slam/reset'))
        remappings.append(('visual_slam/get_all_poses',                     output_topics_prefix+'_visual_slam/get_all_poses'))
        remappings.append(('visual_slam/set_slam_pose',                     output_topics_prefix+'_visual_slam/set_slam_pose'))
        remappings.append(('visual_slam/save_map',                          output_topics_prefix+'_visual_slam/save_map'))
        remappings.append(('visual_slam/load_map',                          output_topics_prefix+'_visual_slam/load_map'))
        remappings.append(('visual_slam/localize_in_map',                   output_topics_prefix+'_visual_slam/localize_in_map'))
        
        # Create the odometry node
        odom_param_overwrites.update(
            {
                "num_cameras": len(camera_optical_frames),
                "base_frame": robot_base_link,
                "imu_frame": (imu_frame_id if enable_imu_fusion else ''),
                "camera_optical_frames": camera_optical_frames,
                "enable_imu_fusion": enable_imu_fusion,
                "publish_map_to_odom_tf": False,
                "publish_odom_to_base_tf": True,
                "enable_localization_n_mapping": False
            }
        )

        logger.info(f'Creating Isaac-ROS odometry node with odom topic set to {self._odom_topic} and node parameters:')
        for key, value in odom_param_overwrites.items():
            logger.info(f'\t{key}: {value}')

        odom_node = ComposableNode(
            name=self.ODOM_NODE_NAME,
            package='isaac_ros_visual_slam',
            plugin='nvidia::isaac_ros::visual_slam::VisualSlamNode',
            parameters=[
                base_params_filepath,
                odom_param_overwrites
            ],
            remappings=remappings
        )

        # Create the launch actions
        self._launch_actions = LoadComposableNodes(
                target_container=shared_container_name,
                composable_node_descriptions=[odom_node])

    def get_launch_actions(self) -> Action:
        """Returns odom node launch actions"""
        return self._launch_actions

    @property
    def odom_topic(self) -> str:
        """Returns the name of the topic on which odometry is published"""
        return self._odom_topic

