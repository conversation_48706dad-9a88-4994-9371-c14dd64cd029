visual_odometry:
  ros__parameters:
    # Basic configuration
    frame_id: "base_link"                 # TF frame for odometry base reference
    odom_frame_id: "odom"                 # TF frame for published odometry
    publish_tf: true                      # Publish TF transforms
    use_sim_time: false                   # Use simulation time
    publish_null_when_lost: true          # Publish identity transform when tracking is lost

    # Subscription settings
    subscribe_rgbd: true                  # Subscribe to combined RGBD messages
    subscribe_rgb: false                  # Subscribe to RGB/infrared images
    subscribe_depth: false                # Subscribe to depth images
    subscribe_imu: true                   # Subscribe to IMU messages
    
    # Synchronization parameters
    approx_sync: true                     # Use approximate time synchronization
    approx_sync_max_interval: 0.01        # Max time difference between messages (seconds)
    sync_queue_size: 15                   # Size of the message queue
    qos: 1                                # Quality of Service (1=reliable)
    
    # IMU configuration
    wait_imu_to_init: true                # Wait for IMU to initialize before starting
    imu_filter_angular_velocity: true     # Filter IMU angular velocity
    imu_frame_id: ""                      # TF frame for IMU
    Odom/ResetCountdown: "0"              # Frames before automatically resetting odometry (0=disabled)
    Odom/Strategy: "0"                    # 0=Frame-to-Map (F2M), 1=Frame-to-Frame (F2F)
    Odom/GuessMotion: "true"              # Use motion estimation between frames
    Odom/FilteringStrategy: "1"           # 0=No filtering, 1=Kalman filtering
    Odom/IMUFilter: "true"                # Apply complementary filter to IMU
    Odom/IMUGravityWeight: "0.2"          # Weight between acc and vision for vertical direction
    
    # Feature detection
    #Vis/FeatureType: "0"                  # 0=SURF, 2=ORB
    Vis/MaxFeatures: "1000"               # Max features extracted per image
    Vis/CorFlowGpu: "true"                # Use GPU for optical flow
    Vis/GPU: "true"                       # Use GPU for other computations
    Vis/CorType: "1"                      # 0=Features matching, 1=Optical flow
    
    # ORB-specific parameters
    ORB/ScaleFactor: "1.2"                # Scale factor between pyramid levels
    ORB/NLevels: "12"                     # Number of pyramid levels
    ORB/EdgeThreshold: "31"               # Pixels away from border to detect features
    ORB/FirstLevel: "0"                   # First level of pyramid
    ORB/WTA_K: "2"                        # Number of points for orientation
    ORB/ScoreType: "1"                    # 0=HARRIS, 1=FAST
    ORB/PatchSize: "31"                   # Patch size for descriptor computation
    ORB/Gpu: "true"                       # Use GPU for ORB extraction
    
    # Pose estimation
    Vis/EstimationType: "0"               # 0=3D->3D, 1=3D->2D PnP
    Vis/PnPFlags: "1"                     # PnP solver flags: 0=ITERATIVE, 1=RANSAC
    Vis/PnPReprojError: "2.0"             # Reprojection error threshold for RANSAC
    Vis/MinInliers: "20"                  # Min visual correspondences to accept transform
    Vis/InlierDistance: "0.1"             # Max distance for visual correspondence
    Vis/RefineIterations: "5"             # Iterations to refine visual transformation
    Vis/MaxDepth: "100.0"                 # Max depth of visual correspondences (m)
    Vis/MinDepth: "0.2"                   # Min depth of visual correspondences (m)
    
    # Bundle adjustment
    Vis/BundleAdjustment: "1"             # 0=disabled, 1=enabled
    Vis/BundleAdjustmentMaxFrames: "10"   # Max number of frames for bundle adjustment
    Vis/CorGuessWinSize: "30"             # Window size for optical flow
    
    # Memory optimization
    OdomF2M/MaxSize: "2000"               # Maximum map size for F2M odometry
    Mem/LaplacianVariance: "0.3"          # Variance of Laplacian used for bad image rejection
    
    # Dynamic parameters for different flight speeds
    Odom/KeyFrameThr: "0.25"              # Distance/angle to create a keyframe
    Vis/CorNNDR: "0.8"                    # Nearest neighbor ratio threshold

    # For pose-graph optimization
    g2o/Optimizer: "0"                    # [0=Levenberg 1=GaussNewton]
    g2o/Solver: "2"                       # 1=csparse, 2=cholmod, 3=pcg, 4=eigen
    g2o/Robust: "true"                    # Enable robust kernel
    g2o/PixelVariance: "1.0"              # Pixel variance for odometry constraints