# TODO: License description

import os, sys
from typing import List
from logging import Logger
from ament_index_python.packages import get_package_share_directory
from launch import Action
from launch.actions import TimerAction, ExecuteProcess
from launch_ros.actions import Node, LoadComposableNodes
from launch_ros.descriptions import ComposableNode
from aqita_config.base_classes import OdomType, OdomSource, StereoCamera, RobotConfiguration, OdomConfiguration, load_yaml_file_parameters
from aqita_config.slam.isaac_ros_vslam.isaac_ros_vslam_config import IsaacRosOdomConfiguration

class RtabMapSyncConfiguration:
    """Base class for creating camera synchronization nodes in a multi-camera system"""

    SYNC_NODE_PLUGIN_CLASS = {
        OdomType.RGBD.name: 'RGBDSync',
        OdomType.STEREO.name: 'StereoSync'
    }

    BASE_SYNC_CONFIG_FILE = 'rtabmap_sync_base.yaml'

    def __init__(self, logger: Logger,
                 odom_type: OdomType,
                 stereo_camera_list: List[StereoCamera],
                 shared_container_name: str,
                 custom_sync_params_filepath: str | None = None):
        """
        Constructor of a base camera synchronization nodes class

        Args:
            logger: logger object to use for logging
            odom_type: odometry type
            stereo_camera_list: list of stereo cameras to synchronize
            shared_container_name: name of the shared container to which the synchronization nodes will be attached
            custom_sync_params_filepath: path to a yaml file with custom synchronization parameters, containing the fields
                                         'camera_sync' and 'rgbd_sync'
        """

        # Get the base synchronization parameters
        logger.info('Loading RTAB-Map camera synchronization nodes base parameters ...')
        base_sync_params_filepath = os.path.join(get_package_share_directory('aqita_config'), 'slam', 'rtabmap', self.BASE_SYNC_CONFIG_FILE)
        base_sync_params = load_yaml_file_parameters(fields=['camera_sync','rgbd_sync'], filepath=base_sync_params_filepath, logger=logger)
        if base_sync_params is None:
            logger.error('Error loading RTAB-Map camera synchronization nodes base parameters. Aborting launch!')
            sys.exit()

        base_camera_sync_params = base_sync_params[0]
        base_rgbd_sync_params = base_sync_params[1]

        # Get the custom synchronization parameter overwrites
        if custom_sync_params_filepath is not None:
            logger.info('Loading RTAB-Map synchronization parameters overwrites ...')
            sync_param_overwrites = load_yaml_file_parameters(fields=['camera_sync','rgbd_sync'], filepath=custom_sync_params_filepath, logger=logger)
            if sync_param_overwrites is None:
                logger.error('Error loading RTAB-Map synchronization parameters overwrites. Aborting launch!')
                sys.exit()

            custom_camera_sync_params = sync_param_overwrites[0]
            custom_rgbd_sync_params = sync_param_overwrites[1]
        else:
            custom_camera_sync_params = {}
            custom_rgbd_sync_params = {}

        # Create camera synchronization nodes
        camera_sync_nodes = []
        camera_sync_params = {}
        num_stereo_cameras = len(stereo_camera_list)

        logger.info(f'Creating RTAB-Map camera synchronization nodes for odometry type {odom_type.name} with parameters:')
        camera_sync_params.update(base_camera_sync_params)
        camera_sync_params.update(custom_camera_sync_params)

        for key, value in camera_sync_params.items():
            logger.info(f'\t{key}: {value}')

        for i, cam in enumerate(stereo_camera_list):
            if odom_type == OdomType.RGBD:
                camera_sync_remappings = [
                    ('rgb/image', cam.rgb_image_topic),
                    ('depth/image', cam.depth_image_topic),
                    ('rgb/camera_info', cam.rgb_cam_info_topic)]
            else:
                camera_sync_remappings = [
                    ('left/image_rect', cam.left_image_topic),
                    ('right/image_rect', cam.right_image_topic),
                    ('left/camera_info', cam.left_cam_info_topic),
                    ('right/camera_info', cam.right_cam_info_topic)]

            rgbd_image_topic_suffix = str(i) if num_stereo_cameras > 1 else '' # Do not modify the output image topic name if only one camera is used
            camera_sync_remappings.extend([('rgbd_image', 'rgbd_image'+rgbd_image_topic_suffix),
                                           ('rgbd_image/compressed', 'rgbd_image'+rgbd_image_topic_suffix+'/compressed')])
            
            logger.info(f'Creating images synchronization node for camera {cam.camera_name} with remappings ...')
            for topic_remap in camera_sync_remappings:
                logger.info(f'\t{topic_remap}')

            sync_node = ComposableNode(
                name=cam.camera_name+'_sync_node',
                package='rtabmap_sync',
                plugin='rtabmap_sync::'+self.SYNC_NODE_PLUGIN_CLASS[odom_type.name],
                parameters=[camera_sync_params],
                remappings=camera_sync_remappings
            )

            camera_sync_nodes.append(sync_node)

        # For a multi-camera setup we have to synchronize the different camera streams
        if num_stereo_cameras > 1:
            rgbd_sync_params = {}

            logger.info(f'Creating RGBDX synchronization node with parameters:')
            rgbd_sync_params.update(base_rgbd_sync_params)
            rgbd_sync_params.update(custom_rgbd_sync_params)
            rgbd_sync_params.update({'rgbd_cameras': num_stereo_cameras})

            for key, value in rgbd_sync_params.items():
                logger.info(f'\t{key}: {value}')
            
            camera_sync_nodes.append(
                ComposableNode(
                    name='rgbd_images_sync_node',
                    package='rtabmap_sync',
                    plugin='rtabmap_sync::RGBDXSync',
                    parameters=[rgbd_sync_params]
                )
            )

        # Create the launch action
        logger.info(f'Creating RTAB-Map camera synchronization nodes launch action for odometry type {odom_type.name} ...')
        self._launch_actions = LoadComposableNodes(
                target_container=shared_container_name,
                composable_node_descriptions=camera_sync_nodes)

    def get_launch_actions(self) -> Action:
        """Returns the RTAB-Map camera synchronization nodes launch action"""
        return self._launch_actions

class RtabMapOdomConfiguration(OdomConfiguration):
    """Rtab-Map odometry configuration class"""

    ODOM_NODE_PLUGIN_CLASS = {
        OdomType.RGBD.name: 'RGBDOdometry',
        OdomType.STEREO.name: 'StereoOdometry'
    }

    ODOM_NODE_NAME = 'rtabmap_odom_node'
    BASE_PARAMS_FILE = 'rtabmap_odom_base.yaml'

    def __init__(self, logger: Logger,
                 odom_type: OdomType,
                 robot_base_link: str,
                 rgbd_cameras: int,
                 use_imu: bool,
                 imu_topic: str | None,
                 imu_frame_id: str | None,
                 shared_container_name: str,
                 custom_odom_params_filepath: str | None = None):
        """
        Constructor of a Rtab-Map odometry configuration class

        Args:
            logger: logger object to use for logging
            odom_type: odometry type
            robot_base_link: name of the robot base link
            rgbd_cameras: number of RGBD cameras. Set to zero to when the node has to subscribe to RGBDImages message type
            use_imu: whether to use IMU messages
            imu_topic: topic on which the IMU messages are being published
            imu_frame_id: frame in which the IMU data is published
            shared_container_name: name of the shared container to which the odometry node will be attached
            custom_odom_params_filepath: path to a yaml file with custom odometry parameters, containing the field 'visual_odometry'
        """

        # Initialize the base class
        super().__init__(odom_type=odom_type)
        logger.info('Initializing Rtab-Map odometry configuration ...')

        # Initialize the base odometry parameters
        base_odom_params_filepath = os.path.join(get_package_share_directory('aqita_config'), 'slam', 'rtabmap', self.BASE_PARAMS_FILE)
        base_odom_params = load_yaml_file_parameters(fields=['visual_odometry'], filepath=base_odom_params_filepath, logger=logger)

        if base_odom_params is None:
            logger.error('Error loading RTAB-Map base odometry parameters. Aborting launch!')
            sys.exit()

        base_odom_params = base_odom_params[0]

        # Initialize the odometry parameter overwrites
        if custom_odom_params_filepath is not None:
            logger.info('Loading Rtab-Map odometry parameter overwrites ...')
            odom_param_overwrites = load_yaml_file_parameters(fields=['visual_odometry'], filepath=custom_odom_params_filepath, logger=logger)

            if odom_param_overwrites is None:
                logger.error('Error loading RTAB-Map odometry parameter overwrites. Aborting launch!')
                sys.exit()

            odom_param_overwrites = odom_param_overwrites[0]
        else:
            odom_param_overwrites = {}

        # Check IMU fusion capability
        if use_imu and imu_topic is not None and imu_frame_id is not None:
            enable_imu_fusion = True
        else:
            enable_imu_fusion = False

        logger.info(f'IMU usage in Rtab-Map odometry set to: {enable_imu_fusion}')
        
        # Create the node remappings
        self._odom_topic = '/odom'
        remappings = [('odom', self._odom_topic)]
        if enable_imu_fusion:
            remappings.append(('imu', imu_topic))

        # Create the odometry node
        odom_params = {}
        logger.info(f'Creating Rtab-Map odometry node with odom topic set to {self._odom_topic} and parameters:')
        odom_params.update(base_odom_params)
        odom_params.update(odom_param_overwrites)
        odom_params.update(
            {
                'frame_id': robot_base_link,
                'odom_frame_id': 'odom',
                'publish_tf': True,
                'initial_pose': '0 0 0 0 0 0',
                'subscribe_rgb': False,
                'subscribe_depth': False,
                'subscribe_rgbd': True,
                'subscribe_imu': enable_imu_fusion,
                'imu_frame_id': (imu_frame_id if enable_imu_fusion else ''),
                'rgbd_cameras': rgbd_cameras
            }
        )

        for key, value in odom_params.items():
            logger.info(f'\t{key}: {value}')

        odom_node = ComposableNode(
            name=self.ODOM_NODE_NAME,
            package='rtabmap_odom',
            plugin='rtabmap_odom::'+self.ODOM_NODE_PLUGIN_CLASS[odom_type.name],
            parameters=[odom_params],
            remappings=remappings
        )

        # Create the launch actions
        self._launch_actions = LoadComposableNodes(
                target_container=shared_container_name,
                composable_node_descriptions=[odom_node])

    def get_launch_actions(self) -> Action:
        """Returns odom node launch actions"""
        return self._launch_actions

    @property
    def odom_topic(self) -> str:
        """Returns the name of the topic on which odometry is published"""
        return self._odom_topic

class RtabMapPoseEstimator:
    """Rtab-Map pose estimator class"""

    SLAM_NODE_NAME = 'rtabmap_slam_node'
    BASE_PARAMS_FILE = 'rtabmap_core_base.yaml'

    def __init__(self, logger: Logger,
                 odom_src: OdomSource,
                 odom_type: OdomType,
                 robot_config: RobotConfiguration,
                 shared_container_name: str,
                 use_imu: bool = True,
                 load_map: bool = False,
                 map_filepath: str = '',
                 custom_sync_params_filepath: str | None = None,
                 custom_odom_params_filepath: str | None = None,
                 custom_slam_params_filepath: str | None = None):
        """
        Constructor of a Rtab-Map pose estimator class.

        Args:
            logger: logger object to use for logging
            odom_src: odometry source
            odom_type: odometry type
            robot_config: robot configuration object
            shared_container_name: name of the shared container to which the SLAM node will be attached
            use_imu: whether to use IMU messages
            load_map: whether to load an existing map (True) or start a new map (False)
            map_filepath: path to the map file if load_map == True
            custom_sync_params_filepath: path to a yaml file with custom synchronization parameters, containing the fields
                                         'camera_sync' and 'rgbd_sync'
            custom_odom_params_filepath: path to a yaml file with custom odometry parameters, containing the field 'visual_odometry'
            custom_slam_params_filepath: path to a yaml file with custom SLAM parameters, containing the field 'rtabmap'
        """

        logger.info('Initializing Rtab-Map pose estimator ...')

        # Initialize the base SLAM parameters
        enable_imu_fusion = True if use_imu and robot_config.imu_topic is not None and robot_config.imu_frame_id is not None else False
        rgbd_cameras = 0 if robot_config.num_stereo_cameras > 1 else 1

        # Create the camera sync configuration
        sync_config = RtabMapSyncConfiguration(
            logger=logger,
            odom_type=odom_type,
            stereo_camera_list=robot_config.get_stereo_camera_list(),
            shared_container_name=shared_container_name,
            custom_sync_params_filepath=custom_sync_params_filepath)
        
        # Create the odometry configuration
        if odom_src == OdomSource.ISAAC_ROS:
            odom_config = IsaacRosOdomConfiguration(
                logger=logger,
                stereo_camera_list=robot_config.get_stereo_camera_list(),
                robot_base_link=robot_config.robot_base_link,
                use_imu=enable_imu_fusion,
                imu_topic=robot_config.imu_topic,
                imu_frame_id=robot_config.imu_frame_id,
                shared_container_name=shared_container_name)
        elif odom_src == OdomSource.RTAB_MAP:
            odom_config = RtabMapOdomConfiguration(
                logger=logger,
                odom_type=odom_type,
                robot_base_link=robot_config.robot_base_link,
                rgbd_cameras=rgbd_cameras,
                use_imu=enable_imu_fusion,
                imu_topic=robot_config.imu_topic,
                imu_frame_id=robot_config.imu_frame_id,
                shared_container_name=shared_container_name,
                custom_odom_params_filepath=custom_odom_params_filepath)
        else:
            logger.error(f'Unknown odometry source {odom_src} specified. Aborting launch!')
            sys.exit()

        # Create the RTAB-MAP remappings
        # NOTE: If using only one camera the rtabmap odom and slam nodes will use the synchronized messages from that camera.
        # If using multiple cameras the rtabmap odom and slam nodes will use the synchronized messages from all cameras which are published as a
        # single rtabmap_msgs/RGBDImages message. To make the odom and slam nodes subscribe to this message we have to set
        # subscribe_rgbd=true and rgbd_cameras=0. Checkout http://wiki.ros.org/rtabmap_odom and http://wiki.ros.org/rtabmap_slam for more info
        self._odom_topic = odom_config.odom_topic
        rtabmap_remappings = [('odom', odom_config.odom_topic)]
        if enable_imu_fusion:
            rtabmap_remappings.append(('imu', robot_config.imu_topic))

        # Initialize the base slam parameters
        base_slam_params_filepath = os.path.join(get_package_share_directory('aqita_config'), 'slam', 'rtabmap', self.BASE_PARAMS_FILE)
        base_slam_params = load_yaml_file_parameters(fields=['rtabmap'], filepath=base_slam_params_filepath, logger=logger)

        if base_slam_params is None:
            logger.error('Error loading RTAB-Map base SLAM parameters. Aborting launch!')
            sys.exit()

        base_slam_params = base_slam_params[0]

        # Initialize the slam parameter overwrites
        if custom_slam_params_filepath is not None:
            logger.info('Loading Rtab-Map slam parameter overwrites ...')
            slam_param_overwrites = load_yaml_file_parameters(fields=['rtabmap'], filepath=custom_slam_params_filepath, logger=logger)

            if slam_param_overwrites is None:
                logger.error('Error loading Rtab-Map SLAM parameter overwrites. Aborting launch!')
                sys.exit()

            slam_param_overwrites = slam_param_overwrites[0]
        else:
            slam_param_overwrites = {}
        
        if odom_src == OdomSource.RTAB_MAP:
            slam_param_overwrites.update({'subscribe_odom_info': True})

        logger.info(f'Creating Rtab-Map SLAM node with parameters:')
        slam_params = {}
        slam_params.update(base_slam_params)
        slam_params.update(slam_param_overwrites)
        slam_params.update(
            {
                'map_frame_id': 'map',
                'frame_id': robot_config.robot_base_link,
                'publish_tf': True,
                'initial_pose': '0 0 0 0 0 0',
                'subscribe_rgb': False,
                'subscribe_depth': False,
                'subscribe_rgbd': True,
                'subscribe_imu': enable_imu_fusion,
                'imu_frame_id': (robot_config.imu_frame_id if enable_imu_fusion else ''),
                'rgbd_cameras': rgbd_cameras
            }
        )

        for key, value in slam_params.items():
            logger.info(f'\t{key}: {value}')

        slam_node = LoadComposableNodes(
            target_container=shared_container_name,
            composable_node_descriptions=[
                ComposableNode(
                    name=self.SLAM_NODE_NAME,
                    package='rtabmap_slam',
                    plugin='rtabmap_slam::CoreWrapper',
                    parameters=[slam_params],
                    remappings=rtabmap_remappings
                )
            ]
        )

        # Create the visualization launch description
        vis_node = Node(
            package='rtabmap_viz',
            executable='rtabmap_viz',
            output='screen',
            parameters=[slam_params],
            remappings=rtabmap_remappings
        )

        # Compose the launch actions
        self._launch_actions = []
        if load_map:
            # Load an existing map
            if os.path.exists(map_filepath):
                logger.info(f'Loading map file {map_filepath} ...')
                self._launch_actions.append(
                    ExecuteProcess(
                        cmd=['cp', map_filepath, slam_params['database_path']],
                        shell=True,
                        output='screen'
                    )
                )
            else:
                logger.error(f'Error loading the specified map file {map_filepath}. Aborting launch!')
                sys.exit()
        else:
            # Start a new map
            new_map_path = slam_params['database_path']
            logger.info(f'Starting new map {new_map_path} ...')
            if os.path.exists(slam_params['database_path']):
                # Delete the existing map
                logger.warning('\tExisting map will be deleted!')
                self._launch_actions.append(
                    ExecuteProcess(
                        cmd=['rm', slam_params['database_path']],
                        shell=True,
                        output='screen'
                    )
                )

        self._launch_actions.append(TimerAction(period=robot_config.launch_completed_time,      actions=[sync_config.get_launch_actions()]))
        self._launch_actions.append(TimerAction(period=robot_config.launch_completed_time+10,   actions=[odom_config.get_launch_actions()]))
        self._launch_actions.append(TimerAction(period=robot_config.launch_completed_time+20,   actions=[slam_node]))
        #self._launch_actions.append(TimerAction(period=robot_config.launch_completed_time+25,   actions=[vis_node]))

    @property
    def odom_topic(self) -> str:
        """Returns the name of the topic on which odometry is published"""
        return self._odom_topic

    def get_launch_actions(self) -> List[Action]:
        """Returns a list of launch actions"""
        return self._launch_actions

