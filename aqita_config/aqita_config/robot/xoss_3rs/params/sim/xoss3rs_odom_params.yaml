visual_odometry:
  ros__parameters:
    use_sim_time: True
    topic_queue_size: 10
    sync_queue_size: 2
    wait_imu_to_init: false
    publish_null_when_lost: true
    approx_sync_max_interval: 0.0
    approx_sync: false
    Odom/Strategy: "0"                  # 0=Frame-to-Map (F2M), 1=Frame-to-Frame (F2F)
    Odom/ResetCountdown: "0"
    Vis/EstimationType: "0"
    #Vis/InlierDistance: "0.1"
    #Vis/RefineIterations: "5"
    #Vis/FeatureType: "6"
    #Vis/CorType: "0"                   # Correspondences computation approach: 0=Features Matching, 1=Optical Flow
    Vis/CorFlowGpu: "False"
    Vis/MaxFeatures: "800"              # Was: 1500
    #Vis/GridCols: "1"
    #Vis/GridRows: "1"
    ORB/Gpu: "False"
    #FAST/Gpu: "True"
    #GFTT/Gpu: "True"
    #SIFT/Gpu: "True"
    #SURF/GpuVersion: "True"
    #Stereo/Gpu: "False"
    #Optimizer/Strategy: "2"            # Graph optimization strategy: 0=TORO, 1=g2o, 2=GTSAM and 3=Ceres
    #Optimizer/Robust: "False"
    #g2o/Optimizer: "0"                 # 0=Levenberg 1=GaussNewton
    #g2o/Solver: "0"                    # 0=csparse 1=pcg 2=cholmod 3=Eigen
