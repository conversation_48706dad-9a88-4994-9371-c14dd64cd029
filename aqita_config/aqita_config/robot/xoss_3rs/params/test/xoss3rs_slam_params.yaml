rtabmap:
  ros__parameters:
    # Basic configuration
    use_sim_time: False
    topic_queue_size: 10
    sync_queue_size: 50
    database_path: "./data/rtabmap.db"
    tf_delay: 0.04
    approx_sync_max_interval: 0.07
    approx_sync: true
    Vis/EstimationType: "0"
    Vis/CorFlowGpu: "False"
    Rtabmap/DetectionRate: "1.0"
    Mem/BinDataKept: "True"
    VhEp/MatchCountMin: "8"
    RGBD/OptimizeFromGraphEnd: "False"
    Rtabmap/CreateIntermediateNodes: "False"
    ORB/Gpu: "False"
    RGBD/AngularUpdate: "0.1"
    RGBD/LinearUpdate: "0.1"
    RGBD/NeighborLinkRefining: "True" # Default False
    RGBD/MaxLocalRetrieved: "2"
    RGBD/LocalRadius: "10"
    RGBD/OptimizeMaxError: "0.6"
    RGBD/CreateOccupancyGrid: "False"
    Mem/ImagePreDecimation: "1"
    Mem/ImagePostDecimation: "1"
    Mem/STMSize: "10"
    Mem/RehearsalSimilarity: "0.6"
    Kp/MaxFeatures: "500"
    Vis/MinInliers: "20"
    Vis/MaxDepth: "0"
    Vis/MinDepth: "0"
    Reg/Strategy: "0"
    Rtabmap/TimeThr: "1000" # Default 0 -> should we set it to 0, Was 650
    RGBD/MaxLoopClosureDistance: "0"




