rtabmap:
  ros__parameters:
    # Basic configuration
    topic_queue_size: 10
    sync_queue_size: 50
    database_path: "./data/rtabmap.db"
    tf_delay: 0.04
    approx_sync_max_interval: 0.07
    approx_sync: true
    Vis/EstimationType: "0"
    #Vis/InlierDistance: "0.1"
    #Vis/RefineIterations: "5"
    #Vis/FeatureType: "6"
    #Vis/CorType: "0"                   # Correspondences computation approach: 0=Features Matching, 1=Optical Flow
    Vis/CorFlowGpu: "False"
    Rtabmap/DetectionRate: "1.0"
    RGBD/LocalRadius: "5.0"
    Mem/BinDataKept: "True"
    RGBD/MaxLoopClosureDistance: "1.0"
    VhEp/MatchCountMin: "8"
    RGBD/OptimizeFromGraphEnd: "False"
    Rtabmap/CreateIntermediateNodes: "False"
    ORB/Gpu: "False"
    #FAST/Gpu: "True"
    #GFTT/Gpu: "True"
    #SIFT/Gpu: "True"
    #SURF/GpuVersion: "True"
    #Stereo/Gpu: "False"
    #Optimizer/Strategy: "2"            # Graph optimization strategy: 0=TORO, 1=g2o, 2=GTSAM and 3=Ceres
    #Optimizer/Robust: "False"
    #g2o/Optimizer: "0"                 # 0=Levenberg 1=GaussNewton
    #g2o/Solver: "0"                    # 0=csparse 1=pcg 2=cholmod 3=Eigen
