
# Common parameters of a Realsense D455 camera module
# For complete parameter list and specifications refer to https://dev.intelrealsense.com/docs/ros2-wrapper 

---
/**:
    ros__parameters:

        camera_name: "rsd455"
        serial_no: " " # Serial number of the camera used for its unique identification in a multi-camera system
        enable_infra1: true # Flag to enable/disable the left infrared image stream
        enable_infra2: true # Flag to enable/disable the right infrared image stream
        enable_color: false # Flag to enable/disable the RGB image stream
        enable_depth: false # Flag to enable/disable the depth image stream
        enable_gyro: true # Flag to enable/disable the gyro measurements
        enable_accel: true # Flag to enable/disable the accelerometer measurements
        enable_sync: true # Gathers closest frames of different sensors, infra red, color and depth, to be sent with the same time tag
        gyro_fps: 200 # Gyro measurements frame rate
        accel_fps: 200 # Accelerometer measurements frame rate
        unite_imu_method: 2 # Accel-to-Gyro timestamp fusion method (0: none, 1: copy, 2: linear_interpolation)
        publish_tf: true # Flag to enable/disable TF publication. If False no TFs are published. If True and tf_publish_rate=0 only static TFs are published
        tf_publish_rate: 0.0 # Dynamic TF publication frequency, [Hz]. Has no effect if publish_tf=False
        accel_info_qos: "SYSTEM_DEFAULT"
        accel_qos: "SYSTEM_DEFAULT"
        color_info_qos: "SYSTEM_DEFAULT"
        color_qos: "SYSTEM_DEFAULT"
        depth_info_qos: "SYSTEM_DEFAULT"
        depth_qos: "SYSTEM_DEFAULT"
        gyro_info_qos: "SYSTEM_DEFAULT"
        gyro_qos: "SYSTEM_DEFAULT"
        infra1_info_qos: "SYSTEM_DEFAULT"
        infra1_qos: "SYSTEM_DEFAULT"
        infra2_info_qos: "SYSTEM_DEFAULT"
        infra2_qos: "SYSTEM_DEFAULT"
        infra_info_qos: "SYSTEM_DEFAULT"
        infra_qos: "SYSTEM_DEFAULT"
        initial_reset: false

        rgb_camera:
            profile: "640x360x90" # Profile for the RGB image in the format "WxHxFPS"

        depth_module:
            emitter_enabled: 0 # Flag to enable/disable the emitter for improved depth estimation (set to 0 or 1)
            profile: "848x480x60" # Profile for the infrared and depth images in the format "WxHxFPS"
