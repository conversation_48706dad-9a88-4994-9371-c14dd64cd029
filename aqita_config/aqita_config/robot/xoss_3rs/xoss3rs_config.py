# TODO: License description

import os, sys, subprocess
import isaac_ros_launch_utils as lu
from typing import List
from ament_index_python.packages import get_package_share_directory
from logging import Logger
from launch.actions import TimerAction
from launch.substitutions import Command
from launch_ros.actions import Node, LoadComposableNodes
from launch_ros.descriptions import ParameterValue, ComposableNode
from aqita_config.base_classes import RobotConfiguration
from aqita_config.sensors.realsense.realsense_config import RealSenseCamera
from aqita_config.sensors.isaac.camera.isaac_cam_config import IsaacSimCamera
from aqita_config.robot.xoss_3rs.xoss3rs_this_robot import Xoss3RsThisRobot, XOSS_3RS_THIS_ROBOT

class Xoss3RsRobotConfiguration(RobotConfiguration):
    """A robot configuration class for the XOSS UAV equipped with 3 RealSense cameras, 
    corresponding to the setup visible [here](https://uvionix.sharepoint.com/sites/aqita/shared%20documents/urdf/realsense-3-cameras-bracket-for-xoss.jpeg?web=1)
    """
    ROBOT_NAME = 'XOSS_3RS'
    ROBOT_BASE_LINK = 'aqita'
    STEREO_CAMERA_MODEL = 'rsd455'
    CAMERA_NODE_LAUNCH_FILE = 'realsense.launch.py'
    RSP_XACRO_FILE = 'xoss3rs.urdf.xacro'
    RSP_NODE_NAME = 'xoss3rs_rsp'

    def __init__(self, logger: Logger,
                 this_robot: Xoss3RsThisRobot,
                 enable_front_camera=True,
                 enable_left_camera=True,
                 enable_right_camera=True,
                 enable_infra_stream=True,
                 enable_color_stream=False,
                 enable_depth_stream=False,
                 enable_emitter=False,
                 enable_intra_process_com=False,
                 depth_module_profile='848x480x60',
                 shared_container_name='xoss3rs',
                 simulation_mode=False,
                 camera_launch_delay=0.0):
        """A robot configuration class for the XOSS UAV equipped with 3 RealSense stereo cameras, 
        corresponding to the setup visible [here](https://uvionix.sharepoint.com/sites/aqita/shared%20documents/urdf/realsense-3-cameras-bracket-for-xoss.jpeg?web=1)
        
        Args:
            logger: logger object to use for logging
            this_robot: unique robot identification tag
            enable_front_camera: whether to enable the front stereo camera
            enable_left_camera: whether to enable the left stereo camera
            enable_right_camera: whether to enable the right stereo camera
            enable_infra_stream: whether to enable the infra streams from the left and right image sensors
            enable_color_stream: whether to enable the color stream from the RGB sensor
            enable_depth_stream: whether to enable the depth stream
            enable_emitter: whether to enable the emitter for improved depth estimation
            enable_intra_process_com: whether to enable intraprocess communication for the camera nodes
            depth_module_profile: stream profile for the infrared, depth and RGB images in the format "WxHxFPS"
            shared_container_name: name of the shared container to which the camera nodes will be attached
            simulation_mode: whether to run the robot configuration in simulation mode which expects camera and IMU data from IsaacSim
            camera_launch_delay: camera nodes launch delay is seconds
        """
        # Initialize the base class
        super().__init__(robot_config_name=self.ROBOT_NAME, robot_base_link=self.ROBOT_BASE_LINK, logger=logger)

        # Add the camera objects
        self._front_camera_name = XOSS_3RS_THIS_ROBOT.get(this_robot).get('front_cam_name')
        self._left_camera_name = XOSS_3RS_THIS_ROBOT.get(this_robot).get('left_cam_name')
        self._right_camera_name = XOSS_3RS_THIS_ROBOT.get(this_robot).get('right_cam_name')
        self._front_camera_serial_no = XOSS_3RS_THIS_ROBOT.get(this_robot).get('front_cam_serial_no')
        self._left_camera_serial_no = XOSS_3RS_THIS_ROBOT.get(this_robot).get('left_cam_serial_no')
        self._right_camera_serial_no = XOSS_3RS_THIS_ROBOT.get(this_robot).get('right_cam_serial_no')
        self._front_camera_enabled = enable_front_camera
        self._left_camera_enabled = enable_left_camera
        self._right_camera_enabled = enable_right_camera

        logger.info(f'Adding stereo camera sensors to robot configuration {self._robot_config_name} ...')
        if not simulation_mode:
            if self._front_camera_enabled:
                self.add_stereo_camera(RealSenseCamera(name=self._front_camera_name,
                                                       model = self.STEREO_CAMERA_MODEL,
                                                       serial_no=self._front_camera_serial_no))
            
            if self._left_camera_enabled:
                self.add_stereo_camera(RealSenseCamera(name=self._left_camera_name,
                                                       model = self.STEREO_CAMERA_MODEL,
                                                       serial_no=self._left_camera_serial_no))

            if self._right_camera_enabled:
                self.add_stereo_camera(RealSenseCamera(name=self._right_camera_name,
                                                       model = self.STEREO_CAMERA_MODEL,
                                                       serial_no=self._right_camera_serial_no))

            # Check camera connections
            logger.info('Checking camera connections ...')
            rs_connected_devices = subprocess.run(['rs-fw-update', '-l'], stdout=subprocess.PIPE, universal_newlines=True).stdout.split('\n')
            for cam in self.get_stereo_camera_list():
                cam_info = [line for line in rs_connected_devices if line.find(cam.camera_serial_no) > -1]

                if len(cam_info) > 0:
                    logger.info(f'Camera with name {cam.camera_name} and serial number {cam.camera_serial_no} detected')
                    logger.info(f'\t{cam_info[0]}')
                else:
                    logger.error(f'Camera with name {cam.camera_name} and serial number {cam.camera_serial_no} not detected')
                    logger.error('Error detecting the required camera devices. Aborting launch!')
                    sys.exit()
        else:
            if self._front_camera_enabled:
                self.add_stereo_camera(IsaacSimCamera(name=self._front_camera_name,
                                                       model = self.STEREO_CAMERA_MODEL,
                                                       serial_no=self._front_camera_serial_no))

            if self._left_camera_enabled:
                self.add_stereo_camera(IsaacSimCamera(name=self._left_camera_name,
                                                       model = self.STEREO_CAMERA_MODEL,
                                                       serial_no=self._left_camera_serial_no))

            if self._right_camera_enabled:
                self.add_stereo_camera(IsaacSimCamera(name=self._right_camera_name,
                                                       model = self.STEREO_CAMERA_MODEL,
                                                       serial_no=self._right_camera_serial_no))

        # Create the robot state publisher node
        if not simulation_mode:
            logger.info(f'Creating robot state publisher node for robot configuration {self._robot_config_name} ...')
            self._rsp_node = Node(
                package='robot_state_publisher',
                executable='robot_state_publisher',
                name=self.RSP_NODE_NAME,
                output='screen',
                parameters=[
                    {
                        'robot_description': ParameterValue(
                            Command(
                                [
                                    'xacro ',
                                    os.path.join(get_package_share_directory('aqita_config'), 'robot', 'xoss_3rs', self.RSP_XACRO_FILE),
                                    ' ',
                                    'robot_base_link:=',
                                    self._robot_base_link,
                                    ' ',
                                    'front_camera_name:=',
                                    self._front_camera_name,
                                    ' ',
                                    'left_camera_name:=',
                                    self._left_camera_name,
                                    ' ',
                                    'right_camera_name:=',
                                    self._right_camera_name,
                                ]
                            ),
                            value_type=str,
                        )
                    }
                ]
            )
        else:
            self._rsp_node = None

        # Create the camera launch actions
        self._launch_actions = []
        logger.info(f'Creating camera launch actions for robot configuration {self._robot_config_name} ...')
        stream_profile = depth_module_profile.strip().split('x')
        if len(stream_profile) != 3:
            # The depth module stream profile should be in the format "WxHxFPS"
            logger.error(f'Error parsing the provided depth module stream profile {depth_module_profile}. Aborting launch!')
            sys.exit()

        try:
            stream_width = int(stream_profile[0])
            stream_height = int(stream_profile[1])
            stream_fps = int(stream_profile[2])
        except:
            # The stream width, height and FPS should be integer values
            logger.error(f'Error parsing the stream width, height and FPS from the provided depth module stream profile {depth_module_profile}')
            logger.error('The stream width, height and FPS should be numeric values. Aborting launch!')
            sys.exit()

        if stream_fps > 30 and enable_depth_stream and self._num_stereo_cameras > 2:
            # Stream FPS not supported for the specified number of stereo cameras while depth is enabled due to bandwidth limitations
            depth_module_profile = str(stream_width)+'x'+str(stream_height)+'x'+str(30)
            logger.warning(f'Stream FPS of {stream_fps} Hz not supported with {self._num_stereo_cameras} stereo cameras while depth is enabled')
            logger.warning(f'The depth stream profile will be set to {depth_module_profile}')
                
        en_depth = ('True' if enable_depth_stream else 'False')
        en_color = ('True' if enable_color_stream else 'False')
        en_infra = ('True' if enable_infra_stream else 'False')
        en_emitter = ('True' if enable_emitter else 'False')
        en_intra_process_comms = ('True' if enable_intra_process_com else 'False')
        node_launch_delay = camera_launch_delay

        if self._front_camera_enabled:
            # Use the front camera IMU as the IMU message source
            imu_cam = self.get_stereo_camera(self._front_camera_name)
        elif self._left_camera_enabled:
            # Use the left camera IMU as the IMU message source
            imu_cam = self.get_stereo_camera(self._left_camera_name)
        else:
            # Use the right camera IMU as the IMU message source
            imu_cam = self.get_stereo_camera(self._right_camera_name)

        logger.info(f'Camera {imu_cam.camera_name} will be used as an IMU message source')
        
        if not simulation_mode:
            for cam in self.get_stereo_camera_list():
                en_gyro = ('True' if imu_cam.camera_name == cam.camera_name else 'False')
                en_accel = ('True' if imu_cam.camera_name == cam.camera_name else 'False')

                logger.info(f'Creating launch action for camera {cam.camera_name} with serial no {cam.camera_serial_no} ...')
                logger.info(f'\tStream profile set to: {depth_module_profile}')
                logger.info(f'\tenable_depth: {en_depth}')
                logger.info(f'\tenable_color: {en_color}')
                logger.info(f'\tenable_infra1: {en_infra}')
                logger.info(f'\tenable_infra2: {en_infra}')
                logger.info(f'\tenable_emitter: {en_emitter}')
                logger.info(f'\tenable_gyro: {en_gyro}')
                logger.info(f'\tenable_accel: {en_accel}')
                logger.info(f'\tuse_intra_process_comms: {en_intra_process_comms}')
                logger.info(f'\tcomponent_container_name: {shared_container_name}')

                cam_launch = lu.include(
                    'aqita_config', 'sensors/realsense/'+self.CAMERA_NODE_LAUNCH_FILE,
                    launch_arguments={
                        'camera_name': cam.camera_name,
                        'camera_serial_no': cam.camera_serial_no,
                        'publish_tf': 'False',
                        'enable_depth': en_depth,
                        'enable_color': en_color,
                        'enable_infra1': en_infra,
                        'enable_infra2':en_infra ,
                        'enable_emitter': en_emitter,
                        'enable_gyro': en_gyro,
                        'enable_accel': en_accel,
                        'depth_module_profile': depth_module_profile,
                        'rgb_camera_profile': depth_module_profile,
                        'use_intra_process_comms': en_intra_process_comms,
                        'attach_to_shared_component_container': 'True',
                        'component_container_name': shared_container_name,
                        'overwrite_params_filepath': ''
                    },
                    delay = node_launch_delay
                )

                self._launch_actions.append(cam_launch)
                node_launch_delay += 10.0

        # Create the IMU frame id modifier node launch action
        # NOTE: The RealSense cameras publish the IMU messages with frame_id = camera_imu_optical_frame. Since this frame does not exist
        # we have to republish the IMU messages with correct frame id which is the gyro optical frame associated with the respective camera.
        if not simulation_mode:
            imu_original_topic = imu_cam.get_realsense_camera_topics().imu_topic # The original topic on which the IMU message is published with wrong frame id
            self._imu_topic = imu_original_topic+'/data' # The new topic on which the IMU message will be published with the correct frame id
            self._imu_frame_id = imu_cam.get_realsense_camera_frames().gyro_optical_frame # The correct IMU message frame id
        else:
            imu_original_topic = imu_cam.get_isaacsim_camera_topics().imu_topic
            self._imu_topic = imu_original_topic
            self._imu_frame_id = imu_cam.get_isaacsim_camera_frames().imu_optical_frame
        
        logger.info(f'Creating IMU message publisher node using camera {imu_cam.camera_name} as a message source ...')
        logger.info(f'\t IMU message topic set to {self._imu_topic}')
        logger.info(f'\t IMU message frame id set to {self._imu_frame_id}')

        if not simulation_mode:
            self._launch_actions.append(
                TimerAction(
                    period=node_launch_delay,
                    actions=[
                        LoadComposableNodes(
                            target_container=shared_container_name,
                            composable_node_descriptions = [
                                ComposableNode(
                                    name=imu_cam.camera_name+'_imu_data_modifier',
                                    package='aqita_topic_tools',
                                    plugin='aqita::ros2::topic_tools::ImuFrameIdModifier',
                                    parameters=[{
                                            'original_topic': imu_original_topic,
                                            'new_topic': self._imu_topic,
                                            'target_frame_id': self._imu_frame_id}])
                            ]
                        )
                    ]
                )
            )

        # Set the expected launch actions completed time
        self._launch_completed_time = node_launch_delay + 20.0

    def get_stereo_camera(self, name) -> RealSenseCamera | IsaacSimCamera:
        """Returns the stereo camera object by its provided name"""
        for cam in self.get_stereo_camera_list():
            if cam.camera_name == name:
                return cam

    def get_stereo_camera_list(self) -> List[RealSenseCamera]:
        """Returns a list of the stereo camera objects"""
        return self._stereo_camera_list
    
    def get_rsp_node(self) -> Node:
        """Returns a robot state publisher node"""
        return self._rsp_node
    
    def get_launch_actions(self) -> List[lu.GroupAction | lu.Action]:
        """Returns a list of launch actions"""
        return self._launch_actions
    
    @property
    def launch_completed_time(self) -> float:
        """Returns the time at which all launch actions should be completed"""
        return self._launch_completed_time
    
    @property
    def imu_topic(self) -> str | None:
        """Returns the IMU message topic. Returns None if there is no IMU in the robot configuration"""
        return self._imu_topic

    @property
    def imu_frame_id(self) -> str | None:
        """Returns the IMU message frame id. Returns None if there is no IMU in the robot configuration"""
        return self._imu_frame_id

# Robot factory
def create_robot(
        logger: Logger,
        this_robot: Xoss3RsThisRobot,
        enable_front_camera=True,
        enable_left_camera=True,
        enable_right_camera=True,
        enable_infra_stream=True,
        enable_color_stream=False,
        enable_depth_stream=False,
        enable_emitter=False,
        enable_intra_process_com=False,
        depth_module_profile='848x480x60',
        shared_container_name='xoss3rs',
        simulation_mode=False,
        camera_launch_delay=0.0) -> Xoss3RsRobotConfiguration:
    """Returns a robot configuration with the provided custom parameters"""
    robot_name = XOSS_3RS_THIS_ROBOT.get(this_robot).get('robot_name')
    logger.info(f'Creating custom configuration for robot {robot_name} ...')
    return Xoss3RsRobotConfiguration(
        logger=logger,
        this_robot=this_robot,
        enable_front_camera=enable_front_camera,
        enable_left_camera=enable_left_camera,
        enable_right_camera=enable_right_camera,
        enable_infra_stream=enable_infra_stream,
        enable_color_stream=enable_color_stream,
        enable_depth_stream=enable_depth_stream,
        enable_emitter=enable_emitter,
        enable_intra_process_com=enable_intra_process_com,
        depth_module_profile=depth_module_profile,
        shared_container_name=shared_container_name,
        simulation_mode=simulation_mode,
        camera_launch_delay=camera_launch_delay)

def create_robot_stereo(
        logger: Logger,
        this_robot: Xoss3RsThisRobot,
        enable_front_camera=True,
        enable_left_camera=True,
        enable_right_camera=True,
        enable_intra_process_com=False,
        depth_module_profile='848x480x60',
        shared_container_name='xoss3rs',
        simulation_mode=False,
        camera_launch_delay=0.0) -> Xoss3RsRobotConfiguration:
    """Returns a robot configuration with stereo camera streams"""
    robot_name = XOSS_3RS_THIS_ROBOT.get(this_robot).get('robot_name')
    logger.info(f'Configuring robot {robot_name} with stereo camera streams ...')
    return Xoss3RsRobotConfiguration(
        logger=logger,
        this_robot=this_robot,
        enable_front_camera=enable_front_camera,
        enable_left_camera=enable_left_camera,
        enable_right_camera=enable_right_camera,
        enable_infra_stream=True,
        enable_color_stream=False,
        enable_depth_stream=False,
        enable_emitter=False,
        enable_intra_process_com=enable_intra_process_com,
        depth_module_profile=depth_module_profile,
        shared_container_name=shared_container_name,
        simulation_mode=simulation_mode,
        camera_launch_delay=camera_launch_delay)

def create_robot_rgbd(
        logger: Logger,
        this_robot: Xoss3RsThisRobot,
        enable_front_camera=True,
        enable_left_camera=True,
        enable_right_camera=True,
        enable_intra_process_com=False,
        depth_module_profile='848x480x30',
        shared_container_name='xoss3rs',
        simulation_mode=False,
        camera_launch_delay=0.0) -> Xoss3RsRobotConfiguration:
    """Returns a robot configuration with rgb and depth camera streams"""
    robot_name = XOSS_3RS_THIS_ROBOT.get(this_robot).get('robot_name')
    logger.info(f'Configuring robot {robot_name} with rgb and depth camera streams ...')
    return Xoss3RsRobotConfiguration(
        logger=logger,
        this_robot=this_robot,
        enable_front_camera=enable_front_camera,
        enable_left_camera=enable_left_camera,
        enable_right_camera=enable_right_camera,
        enable_infra_stream=False,
        enable_color_stream=True,
        enable_depth_stream=True,
        enable_emitter=True,
        enable_intra_process_com=enable_intra_process_com,
        depth_module_profile=depth_module_profile,
        shared_container_name=shared_container_name,
        simulation_mode=simulation_mode,
        camera_launch_delay=camera_launch_delay)
