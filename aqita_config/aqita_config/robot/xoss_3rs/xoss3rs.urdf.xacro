<?xml version="1.0"?>

<!--
// TODO: License description
-->

<!-- Robot description file for the XOSS UAV equipped with 3 Realsense cameras.
The file corresponds to the setup visible here:
  https://uvionix.sharepoint.com/sites/aqita/shared%20documents/urdf/realsense-3-cameras-bracket-for-xoss.jpeg?web=1
-->

<robot name="xoss3-rs" xmlns:xacro="http://ros.org/wiki/xacro">
    <!--
      Parameters:
        - robot_base_link: the name of the robot base link
        - front_camera_name: the name of the front camera
        - left_camera_name: the name of the left camera
        - right_camera_name: the name of the right camera
    -->

    <!-- Define configuration parameters -->
    <xacro:arg name="robot_base_link"  default="base_link" />
    <xacro:arg name="front_camera_name"   default="front_D455_cam" />
    <xacro:arg name="left_camera_name"  default="left_D455_cam" />
    <xacro:arg name="right_camera_name"  default="right_D455_cam" />

    <!-- Include macro definitions -->
    <xacro:include filename="$(find aqita_config)/sensors/realsense/realsense_macro.urdf.xacro" />

    <!-- Robot base link definition -->
    <link name="$(arg robot_base_link)"/>

    <!-- Front camera description -->
    <xacro:rs_camera type="modular" 
    cam_name="$(arg front_camera_name)" 
    robot_base_link="$(arg robot_base_link)" 
    base_offset_x="0.12485" 
    base_offset_y="0.0475" 
    base_offset_z="0.0139" 
    base_roll="0" 
    base_pitch="0" 
    base_yaw="0" 
    baseline="0.095009" 
    imu_offset_x="-0.00965" 
    imu_offset_y="-0.03055" 
    imu_offset_z="-0.0051" 
    imu_roll="0" 
    imu_pitch="3.1415926535897931" 
    imu_yaw="0"
    >
    </xacro:rs_camera>

    <!-- Left camera description -->
    <xacro:rs_camera type="modular" 
    cam_name="$(arg left_camera_name)" 
    robot_base_link="$(arg robot_base_link)" 
    base_offset_x="-0.0475" 
    base_offset_y="0.12485" 
    base_offset_z="0.0139" 
    base_roll="0" 
    base_pitch="0" 
    base_yaw="1.570796327" 
    baseline="0.095009" 
    imu_offset_x="-0.00965" 
    imu_offset_y="-0.03055" 
    imu_offset_z="-0.0051" 
    imu_roll="0" 
    imu_pitch="3.1415926535897931" 
    imu_yaw="0"
    >
    </xacro:rs_camera>

    <!-- Right camera description -->
    <xacro:rs_camera type="modular" 
    cam_name="$(arg right_camera_name)" 
    robot_base_link="$(arg robot_base_link)" 
    base_offset_x="0.0475" 
    base_offset_y="-0.12485" 
    base_offset_z="0.0139" 
    base_roll="0" 
    base_pitch="0" 
    base_yaw="-1.570796327" 
    baseline="0.095009" 
    imu_offset_x="-0.00965" 
    imu_offset_y="-0.03055" 
    imu_offset_z="-0.0051" 
    imu_roll="0" 
    imu_pitch="3.1415926535897931" 
    imu_yaw="0"
    >
    </xacro:rs_camera>
</robot>
