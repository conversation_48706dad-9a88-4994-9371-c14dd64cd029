# TODO: License description

from enum import Enum

class Xoss3RsThisRobot(Enum):
    """Enum defining the unique robot identification tags"""
    PLD = 0     # Plovdiv robot
    SOF = 1     # Sofia robot

XOSS_3RS_THIS_ROBOT = {
    Xoss3RsThisRobot.PLD: {
        'robot_name': 'Xoss3Rs@PLD',
        'front_cam_name': 'cam_front',
        'left_cam_name': 'cam_left',
        'right_cam_name': 'cam_right',
        'front_cam_serial_no': '231122301680',
        'left_cam_serial_no': '231122301961',
        'right_cam_serial_no': '231122300977',
    },
    Xoss3RsThisRobot.SOF: {
        'robot_name': 'Xoss3Rs@SOF',
        'front_cam_name': 'cam_front',
        'left_cam_name': 'cam_left',
        'right_cam_name': 'cam_right',
        'front_cam_serial_no': '123456789012',
        'left_cam_serial_no': '123456789012',
        'right_cam_serial_no': '123456789012',
    }
}

