# TODO: License description

from collections import namedtuple
from aqita_config.base_classes import StereoCamera

class RealSenseCamera(StereoCamera):
    """A RealSense stereo camera class with specific attributes"""

    # Frame suffixes for a RealSense camera
    RS_CAMERA_FRAME_SUFFIXES = {
        'left_cam_frame': '_infra1_frame',
        'right_cam_frame': '_infra2_frame',
        'left_cam_optical_frame': '_infra1_optical_frame',
        'right_cam_optical_frame': '_infra2_optical_frame',
        'depth_frame': '_depth_frame',
        'depth_optical_frame': '_depth_optical_frame',
        'rgb_frame': '_color_frame',
        'rgb_optical_frame': '_color_optical_frame',
        'imu_frame': '_imu',
        'imu_optical_frame': '_imu_optical_frame',
        'accel_frame': '_accel_frame',
        'accel_optical_frame': '_accel_optical_frame',
        'gyro_frame': '_gyro_frame',
        'gyro_optical_frame': '_gyro_optical_frame',
    }

    # Topic suffixes for a RealSense camera
    RS_CAMERA_TOPIC_SUFFIXES = {
        'left_img_topic': '/infra1/image_rect_raw',
        'right_img_topic': '/infra2/image_rect_raw',
        'rgb_img_topic': '/color/image_raw',
        'depth_img_topic': '/depth/image_rect_raw',
        'imu_topic': '/imu',
        'left_cam_info_topic': '/infra1/camera_info',
        'right_cam_info_topic': '/infra2/camera_info',
        'rgb_cam_info_topic': '/color/camera_info',
        'depth_cam_info_topic': '/depth/camera_info',
    }

    def __init__(self, name: str, model: str, serial_no: str):
        # Initialize the base class
        super().__init__(name=name, model=model, unique_id=serial_no)
    
    @property
    def camera_serial_no(self) -> str:
        """Returns the stereo camera serial number which is the same as the camera unique id"""
        return self.camera_unique_id
    
    def get_realsense_camera_frames(self):
        """Get the camera frames associated with the RealSense camera name"""
        cam_frames = namedtuple(
            'cam_frames',
            [
                'left_cam_frame',
                'right_cam_frame',
                'left_cam_optical_frame',
                'right_cam_optical_frame',
                'depth_frame',
                'depth_optical_frame',
                'rgb_frame',
                'rgb_optical_frame',
                'imu_frame',
                'imu_optical_frame',
                'accel_frame',
                'accel_optical_frame',
                'gyro_frame',
                'gyro_optical_frame',
            ],
        )

        return cam_frames(
            self._name + self.RS_CAMERA_FRAME_SUFFIXES['left_cam_frame'],
            self._name + self.RS_CAMERA_FRAME_SUFFIXES['right_cam_frame'],
            self._name + self.RS_CAMERA_FRAME_SUFFIXES['left_cam_optical_frame'],
            self._name + self.RS_CAMERA_FRAME_SUFFIXES['right_cam_optical_frame'],
            self._name + self.RS_CAMERA_FRAME_SUFFIXES['depth_frame'],
            self._name + self.RS_CAMERA_FRAME_SUFFIXES['depth_optical_frame'],
            self._name + self.RS_CAMERA_FRAME_SUFFIXES['rgb_frame'],
            self._name + self.RS_CAMERA_FRAME_SUFFIXES['rgb_optical_frame'],
            self._name + self.RS_CAMERA_FRAME_SUFFIXES['imu_frame'],
            self._name + self.RS_CAMERA_FRAME_SUFFIXES['imu_optical_frame'],
            self._name + self.RS_CAMERA_FRAME_SUFFIXES['accel_frame'],
            self._name + self.RS_CAMERA_FRAME_SUFFIXES['accel_optical_frame'],
            self._name + self.RS_CAMERA_FRAME_SUFFIXES['gyro_frame'],
            self._name + self.RS_CAMERA_FRAME_SUFFIXES['gyro_optical_frame'],
        )

    def get_realsense_camera_topics(self):
        """Get the camera topics associated with the RealSense camera name"""
        cam_topics = namedtuple(
            'cam_topics',
            [
                'left_img_topic',
                'right_img_topic',
                'rgb_img_topic',
                'depth_img_topic',
                'imu_topic',
                'left_cam_info_topic',
                'right_cam_info_topic',
                'rgb_cam_info_topic',
                'depth_cam_info_topic',
            ],
        )

        return cam_topics(
            '/' + self._name + self.RS_CAMERA_TOPIC_SUFFIXES['left_img_topic'],
            '/' + self._name + self.RS_CAMERA_TOPIC_SUFFIXES['right_img_topic'],
            '/' + self._name + self.RS_CAMERA_TOPIC_SUFFIXES['rgb_img_topic'],
            '/' + self._name + self.RS_CAMERA_TOPIC_SUFFIXES['depth_img_topic'],
            '/' + self._name + self.RS_CAMERA_TOPIC_SUFFIXES['imu_topic'],
            '/' + self._name + self.RS_CAMERA_TOPIC_SUFFIXES['left_cam_info_topic'],
            '/' + self._name + self.RS_CAMERA_TOPIC_SUFFIXES['right_cam_info_topic'],
            '/' + self._name + self.RS_CAMERA_TOPIC_SUFFIXES['rgb_cam_info_topic'],
            '/' + self._name + self.RS_CAMERA_TOPIC_SUFFIXES['depth_cam_info_topic'],
        )
       
    @property
    def left_cam_optical_frame(self):
        """Returns the optical frame of the left image sensor"""
        return self.get_realsense_camera_frames().left_cam_optical_frame

    @property
    def right_cam_optical_frame(self):
        """Returns the optical frame of the right image sensor"""
        return self.get_realsense_camera_frames().right_cam_optical_frame

    @property
    def rgb_image_topic(self) -> str:
        """Returns the RGB image topic"""
        return self.get_realsense_camera_topics().rgb_img_topic

    @property
    def rgb_cam_info_topic(self) -> str:
        """Returns the RGB camera info topic"""
        return self.get_realsense_camera_topics().rgb_cam_info_topic

    @property
    def depth_image_topic(self) -> str:
        """Returns the depth image topic"""
        return self.get_realsense_camera_topics().depth_img_topic

    @property
    def depth_cam_info_topic(self) -> str:
        """Returns the depth cam info topic"""
        return self.get_realsense_camera_topics().depth_cam_info_topic

    @property
    def left_image_topic(self) -> str:
        """Returns the left image topic"""
        return self.get_realsense_camera_topics().left_img_topic

    @property
    def left_cam_info_topic(self) -> str:
        """Returns the left cam info topic"""
        return self.get_realsense_camera_topics().left_cam_info_topic

    @property
    def right_image_topic(self) -> str:
        """Returns the right image topic"""
        return self.get_realsense_camera_topics().right_img_topic

    @property
    def right_cam_info_topic(self) -> str:
        """Returns the right cam info topic"""
        return self.get_realsense_camera_topics().right_cam_info_topic
