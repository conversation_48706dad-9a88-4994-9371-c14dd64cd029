# TODO: License description

import os, yaml
import isaac_ros_launch_utils as lu
from enum import Enum
from typing import List
from abc import ABC, abstractmethod
from logging import Logger

def load_yaml_file_parameters(fields: List[str], filepath: str, logger: Logger) -> List[dict] | None:
    """
    The function returns the parameters specified in a given yaml file as a dictionary list. The parameters
    in the yaml file are expected to be grouped by the field names, provided in the fields list. Given element
    in the output dictionary list corresponds to the respective element in the input fields list.

    Args:
        fields: list of the parameter groups in the yaml file
        filepath: path to the yaml file to load parameters from
        logger: logger object used for logging
    """

    if os.path.exists(filepath):
        try:
            with open(filepath, 'r') as f:
                loaded_params = yaml.safe_load(f)
                if loaded_params:
                    param_list = []
                    for param_set in fields:
                        if param_set not in loaded_params:
                            logger.error(f"Failed loading parameter set {param_set} from file {filepath}")
                            return None
                        
                        param_list.append(loaded_params[param_set]['ros__parameters'])
                        logger.info(f'Loaded parameter set {param_set} from file {filepath}')

                    logger.info(f'Successfully loaded parameters from file {filepath}')
                    return param_list
                else:
                    logger.error(f"Failed loading parameters from file {filepath}")
                    return None
        except Exception as e:
            logger.error(f"Error loading parameters from file {filepath}: {e}")
            return None
    else:
        logger.error(f"Error loading parameters from file {filepath}. File not found!")
        return None

class OdomSource(Enum):
    """Enum defining the core odometry sources"""
    RTAB_MAP = 0
    ISAAC_ROS = 1

class OdomType(Enum):
    """Enum defining the core odometry types"""
    RGBD = 0
    STEREO = 1

class StereoCamera(ABC):
    """A generic stereo camera class with common attributes"""

    def __init__(self, name: str, model: str, unique_id: str):
        """
        Constructor of a base stereo camera class

        Args:
            name: name to associate with the stereo camera
            model: stereo camera model
            unique_id: unique identifier to associate with the stereo camera
        """
        self._name = name.strip().strip('/').strip()
        self._model = model
        self._unique_id = unique_id

    @property
    @abstractmethod
    def left_cam_optical_frame(self):
        """Returns the optical frame of the left image sensor"""
        pass

    @property
    @abstractmethod
    def right_cam_optical_frame(self):
        """Returns the optical frame of the right image sensor"""
        pass

    @property
    @abstractmethod
    def rgb_image_topic(self) -> str:
        """Returns the RGB image topic"""
        pass

    @property
    @abstractmethod
    def rgb_cam_info_topic(self) -> str:
        """Returns the RGB camera info topic"""
        pass

    @property
    @abstractmethod
    def depth_image_topic(self) -> str:
        """Returns the depth image topic"""
        pass

    @property
    @abstractmethod
    def depth_cam_info_topic(self) -> str:
        """Returns the depth cam info topic"""
        pass

    @property
    @abstractmethod
    def left_image_topic(self) -> str:
        """Returns the left image topic"""
        pass

    @property
    @abstractmethod
    def left_cam_info_topic(self) -> str:
        """Returns the left cam info topic"""
        pass

    @property
    @abstractmethod
    def right_image_topic(self) -> str:
        """Returns the right image topic"""
        pass

    @property
    @abstractmethod
    def right_cam_info_topic(self) -> str:
        """Returns the right cam info topic"""
        pass

    @property
    def camera_name(self) -> str:
        """Returns the stereo camera name"""
        return self._name
    
    @property
    def camera_model(self) -> str:
        """Returns the stereo camera model"""
        return self._model
    
    @property
    def camera_unique_id(self) -> str:
        """Returns the stereo camera unique id"""
        return self._unique_id

class RobotConfiguration(ABC):
    """Base robot configuration class"""

    def __init__(self, robot_config_name: str, robot_base_link: str, logger: Logger):
        """
        Constructor of a base robot configuration class

        Args:
            robot_config_name: name to associate with the robot configuration
            robot_base_link: name of the robot base link
            logger: logger object to use for logging
        """
        self._robot_config_name = robot_config_name
        self._robot_base_link = robot_base_link
        self._logger = logger
        self._num_stereo_cameras = 0
        self._stereo_camera_list = []

        self._logger.info(f'Creating robot configuration {self._robot_config_name} ...')
        self._logger.info(f'Robot base link name set to {self._robot_base_link}')

    def add_stereo_camera(self, camera: StereoCamera):
        """Adds a stereo camera object to the list of cameras and updates the camera counter"""
        self._stereo_camera_list.append(camera)
        self._num_stereo_cameras += 1
        self._logger.info(f'Stereo camera sensor added to robot configuration {self._robot_config_name}. Camera name: {camera.camera_name}, model: {camera.camera_model}, id: {camera.camera_unique_id}')

    @abstractmethod
    def get_stereo_camera(self, name):
        """Returns the stereo camera object by its provided name"""
        pass
    
    @abstractmethod
    def get_stereo_camera_list(self):
        """Returns a list of the defined stereo camera objects"""
        pass

    @abstractmethod
    def get_rsp_node(self):
        """Returns a robot state publisher node"""
        pass

    @abstractmethod
    def get_launch_actions(self) -> List[lu.GroupAction | lu.Action]:
        """Returns a list of launch actions"""
        pass

    @property
    @abstractmethod
    def launch_completed_time(self) -> float:
        """Returns the time at which all launch actions should be completed"""
        pass

    @property
    @abstractmethod
    def imu_topic(self) -> str | None:
        """Returns the IMU message topic. Returns None if there is no IMU in the robot configuration"""
        pass

    @property
    @abstractmethod
    def imu_frame_id(self) -> str | None:
        """Returns the IMU message frame id. Returns None if there is no IMU in the robot configuration"""
        pass

    @property
    def robot_config_name(self) -> str:
        """Returns the name of the robot configuration"""
        return self._robot_config_name
    
    @property
    def robot_base_link(self) -> str:
        """Returns the name of the robot base link"""
        return self._robot_base_link

    @property
    def num_stereo_cameras(self):
        """Returns the number of stereo cameras in the robot configuration"""
        return self._num_stereo_cameras

class OdomConfiguration(ABC):
    """Base odometry configuration class"""

    def __init__(self, odom_type: OdomType):
        """
        Constructor of a base odometry configuration class
        
        Args:
            odom_type: odometry type
        """
        self._odom_type = odom_type

    @abstractmethod
    def get_launch_actions(self):
        """Returns the odometry configuration launch actions"""
        pass

    @property
    @abstractmethod
    def odom_topic(self):
        """Returns the name of the topic on which odometry is published"""
        pass

    @property
    def odom_type(self) -> OdomType:
        """Returns the odometry type"""
        return self._odom_type

class PoseEstimator(ABC):
    """Base pose estimator configuration class"""

    def __init__(self):
        """Constructor of a base pose estimator configuration class"""
        pass

