
#include "rclcpp/rclcpp.hpp"
#include "nav_msgs/msg/odometry.hpp"
#include "geometry_msgs/msg/transform_stamped.hpp"
#include "tf2_ros/transform_listener.h"
#include "tf2_ros/buffer.h"

namespace aqita
{
namespace ros2
{
namespace topic_tools
{

/** 
 * @brief Robot base link odometry message publisher node
 * @param parent_frame name of the parrent frame w.r.t. which the odometry message will be defined
 * @param robot_base_link name of the robot base link
 * @param topic_name name of the output topic to publish
 * @param tf_rate rate at which the odometry message will be published in Hz
 * @note The node publishes the transform from the specified parent frame to the specified base_link frame as an odometry message.
 * The transform is obtained by looking-it-up from the TF tree
*/
class BaselinkOdometryPublisher : public rclcpp::Node
{
public:
    // constructor
    explicit BaselinkOdometryPublisher(const rclcpp::NodeOptions & options)
    : Node("baselink_odom_publisher", options),
    tf_buffer_(get_clock()),
    tf_listener_(tf_buffer_)
    {
        RCLCPP_INFO(get_logger(), "Initializing baselink odometry publisher node...");

        // declare parameters
        declare_parameter<std::string>("parent_frame", "map");
        declare_parameter<std::string>("robot_base_link", "base_link");
        declare_parameter<std::string>("topic_name", "map_to_baselink_transform");
        declare_parameter<int>("tf_rate", 10);

        // get the parameters
        parent_frame_ = get_parameter("parent_frame").as_string();
        robot_base_link_ = get_parameter("robot_base_link").as_string();
        topic_name_ = get_parameter("topic_name").as_string();
        tf_rate_ = get_parameter("tf_rate").as_int();
        timer_period_ms_ = static_cast<int>(1000.0 / tf_rate_);

        RCLCPP_INFO(get_logger(), "Publishing frequency set to: %d", static_cast<int>(1000.0 / timer_period_ms_));

        // create a timer for publishing at the desired frequency
        timer_ = create_wall_timer(std::chrono::milliseconds(timer_period_ms_), std::bind(&BaselinkOdometryPublisher::publish_transform, this));

        // create the odometry message publisher
        publisher_ = create_publisher<nav_msgs::msg::Odometry>(topic_name_, 10);
    }

private:
    // timer callback function
    void publish_transform()
    {
        geometry_msgs::msg::TransformStamped transform_stamped;

        try
        {
            // lookup the transform from the parent frame to the baselink frame
            transform_stamped = tf_buffer_.lookupTransform(parent_frame_, robot_base_link_, tf2::TimePointZero);

            // create the odometry message
            nav_msgs::msg::Odometry odom_msg;
            odom_msg.header.stamp = transform_stamped.header.stamp;
            odom_msg.header.frame_id = parent_frame_;
            odom_msg.child_frame_id = robot_base_link_;

            // set the pose from the transform
            odom_msg.pose.pose.position.x = transform_stamped.transform.translation.x;
            odom_msg.pose.pose.position.y = transform_stamped.transform.translation.y;
            odom_msg.pose.pose.position.z = transform_stamped.transform.translation.z;
            odom_msg.pose.pose.orientation = transform_stamped.transform.rotation;

            // twist (velocity) is unknown - set to zero
            odom_msg.twist.twist.linear.x = 0.0;
            odom_msg.twist.twist.linear.y = 0.0;
            odom_msg.twist.twist.linear.z = 0.0;
            odom_msg.twist.twist.angular.x = 0.0;
            odom_msg.twist.twist.angular.y = 0.0;
            odom_msg.twist.twist.angular.z = 0.0;

            // publish the odometry message
            publisher_->publish(odom_msg);
        }
        catch( const tf2::TransformException &ex )
        {
            RCLCPP_ERROR(get_logger(), "Could not obtain the transform from %s to %s: %s", parent_frame_.c_str(), robot_base_link_.c_str(), ex.what());
        }
    }

    // parameters
    int tf_rate_;
    int timer_period_ms_;
    std::string parent_frame_;
    std::string robot_base_link_;
    std::string topic_name_;

    // transform listener
    tf2_ros::Buffer tf_buffer_;
    tf2_ros::TransformListener tf_listener_;

    // timer and publisher
    rclcpp::TimerBase::SharedPtr timer_;
    rclcpp::Publisher<nav_msgs::msg::Odometry>::SharedPtr publisher_;
};

}   // topic_tools
}   // namespace ros2
}   // namespace aqita

// export the node as a plugin
#include "rclcpp_components/register_node_macro.hpp"
RCLCPP_COMPONENTS_REGISTER_NODE(aqita::ros2::topic_tools::BaselinkOdometryPublisher)
