
#include "rclcpp/rclcpp.hpp"
#include "geographic_msgs/msg/geo_point_stamped.hpp"

namespace aqita
{
namespace ros2
{
namespace topic_tools
{

/** 
 * @brief Global position origin publisher node
 * @param topic_name name of the topic to publish
 * @param topic_rate topic publishing frequency in Hz
 * @param latitude latitude of the global position origin point in degrees
 * @param longitude longitude of the global position origin point in degrees
 * @param geodetic_altitude geodetic altitude (height above the reference ellipsoid) of the global position origin point in meters
 * @note The node publishes the specified global position origin as a GeoPointStamped geographic message on the specified topic name
 * and at the specified publishing frequency
*/
class GlobalPositionOriginPublisher : public rclcpp::Node
{
public:
    // constructor
    explicit GlobalPositionOriginPublisher(const rclcpp::NodeOptions & options)
    : Node("global_position_origin_publisher", options)
    {
        RCLCPP_INFO(get_logger(), "Initializing global position origin publisher node...");

        // declare parameters
        declare_parameter<std::string>("topic_name", "gp_origin");
        declare_parameter<int>("topic_rate", 10);
        declare_parameter<double>("latitude", 0.0);
        declare_parameter<double>("longitude", 0.0);
        declare_parameter<double>("geodetic_altitude", 0.0);

        // get the parameters
        topic_name_ = get_parameter("topic_name").as_string();
        topic_rate_ = get_parameter("topic_rate").as_int();
        latitude_ = get_parameter("latitude").as_double();
        longitude_ = get_parameter("longitude").as_double();
        geodetic_altitude_ = get_parameter("geodetic_altitude").as_double();
        timer_period_ms_ = static_cast<int>(1000.0 / topic_rate_);

        RCLCPP_INFO(get_logger(), "Publishing frequency set to: %d", static_cast<int>(1000.0 / timer_period_ms_));

        // create a timer for publishing at the desired frequency
        timer_ = create_wall_timer(std::chrono::milliseconds(timer_period_ms_), std::bind(&GlobalPositionOriginPublisher::publish_topic, this));

        // create the global position message publisher
        publisher_ = create_publisher<geographic_msgs::msg::GeoPointStamped>(topic_name_, 10);
    }

private:
    // timer callback function
    void publish_topic()
    {
        geographic_msgs::msg::GeoPointStamped gpo_msg;

        gpo_msg.header.stamp = get_clock()->now();
        gpo_msg.position.latitude = latitude_;
        gpo_msg.position.longitude = longitude_;
        gpo_msg.position.altitude = geodetic_altitude_;

        publisher_->publish(gpo_msg);
    }

    // parameters
    int topic_rate_;
    int timer_period_ms_;
    double latitude_;
    double longitude_;
    double geodetic_altitude_;
    std::string topic_name_;

    // timer and publisher
    rclcpp::TimerBase::SharedPtr timer_;
    rclcpp::Publisher<geographic_msgs::msg::GeoPointStamped>::SharedPtr publisher_;
};

}   // topic_tools
}   // namespace ros2
}   // namespace aqita

// export the node as a plugin
#include "rclcpp_components/register_node_macro.hpp"
RCLCPP_COMPONENTS_REGISTER_NODE(aqita::ros2::topic_tools::GlobalPositionOriginPublisher)
