
#include "rclcpp/rclcpp.hpp"
#include "sensor_msgs/msg/camera_info.hpp"

namespace aqita
{
namespace ros2
{
namespace topic_tools
{

/** 
 * @brief Node for modifying the frame_id field of a camera_info topic
 * @param original_topic name of the camera_info topic whose frame_id field has to be modified
 * @param new_topic name of the new camera_info topic to publish with the modified frame_id field
 * @param target_frame_id desired frame_id of the new camera_info topic
 * @note The node will publish a new camera_info topic with the specified name by copying the information from the
 * original topic and modifying the frame_id field
*/
class CameraInfoFrameIdModifier : public rclcpp::Node
{
public:
    // constructor
    explicit CameraInfoFrameIdModifier(const rclcpp::NodeOptions & options)
    : Node("camera_info_frame_id_modifier", options)
    {
        // declare parameters
        declare_parameter<std::string>("original_topic", "/camera/camera_info");
        declare_parameter<std::string>("new_topic", "/camera/new_camera_info");
        declare_parameter<std::string>("target_frame_id", "new_frame_id");

        // get the parameters
        original_topic_ = get_parameter("original_topic").as_string();
        new_topic_ = get_parameter("new_topic").as_string();
        target_frame_id_ = get_parameter("target_frame_id").as_string();

        // create the subscription to the original topic
        subscription_ = create_subscription<sensor_msgs::msg::CameraInfo>(
            original_topic_, 10,
            std::bind(&CameraInfoFrameIdModifier::process_msg, this, std::placeholders::_1));

        // create the publisher for the modified message
        publisher_ = create_publisher<sensor_msgs::msg::CameraInfo>(new_topic_, 10);
    }

private:
    // callback function to modify the message
    void process_msg(const sensor_msgs::msg::CameraInfo::SharedPtr msg)
    {
        // modify the frame_id
        auto modified_msg = *msg;
        modified_msg.header.frame_id = target_frame_id_;

        // publish the modified message
        publisher_->publish(modified_msg);
    }

    // parameters
    std::string original_topic_;
    std::string new_topic_;
    std::string target_frame_id_;

    // subscriber and publisher
    rclcpp::Subscription<sensor_msgs::msg::CameraInfo>::SharedPtr subscription_;
    rclcpp::Publisher<sensor_msgs::msg::CameraInfo>::SharedPtr publisher_;
};

}   // topic_tools
}   // namespace ros2
}   // namespace aqita

// export the node as a plugin
#include "rclcpp_components/register_node_macro.hpp"
RCLCPP_COMPONENTS_REGISTER_NODE(aqita::ros2::topic_tools::CameraInfoFrameIdModifier)
