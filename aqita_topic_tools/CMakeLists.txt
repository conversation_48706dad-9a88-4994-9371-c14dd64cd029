
# TODO: Licence description

cmake_minimum_required(VERSION 3.8)
project(aqita_topic_tools LANGUAGES C CXX)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake_auto REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclcpp_components REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(tf2_ros REQUIRED)
# uncomment the following section in order to fill in
# further dependencies manually.
# find_package(<dependency> REQUIRED)

# discover header file dependencies
ament_auto_find_build_dependencies()

# camera_info message frame ID modifier node
ament_auto_add_library(camera_info_frame_id_modifier SHARED src/camera_info_frame_id_modifier.cpp)
rclcpp_components_register_nodes(camera_info_frame_id_modifier "aqita::ros2::topic_tools::CameraInfoFrameIdModifier")
set(node_plugins "${node_plugins}aqita::ros2::topic_tools::CameraInfoFrameIdModifier;$<TARGET_FILE:camera_info_frame_id_modifier>\n")

# IMU message frame ID modifier node
ament_auto_add_library(imu_frame_id_modifier SHARED src/imu_frame_id_modifier.cpp)
rclcpp_components_register_nodes(imu_frame_id_modifier "aqita::ros2::topic_tools::ImuFrameIdModifier")
set(node_plugins "${node_plugins}aqita::ros2::topic_tools::ImuFrameIdModifier;$<TARGET_FILE:imu_frame_id_modifier>\n")

# image message frame ID modifier node
ament_auto_add_library(image_frame_id_modifier SHARED src/image_frame_id_modifier.cpp)
rclcpp_components_register_nodes(image_frame_id_modifier "aqita::ros2::topic_tools::ImageFrameIdModifier")
set(node_plugins "${node_plugins}aqita::ros2::topic_tools::ImageFrameIdModifier;$<TARGET_FILE:image_frame_id_modifier>\n")

# baselink odometry publisher node
ament_auto_add_library(baselink_odom_publisher SHARED src/baselink_odometry_publisher.cpp)
rclcpp_components_register_nodes(baselink_odom_publisher "aqita::ros2::topic_tools::BaselinkOdometryPublisher")
set(node_plugins "${node_plugins}aqita::ros2::topic_tools::BaselinkOdometryPublisher;$<TARGET_FILE:baselink_odom_publisher>\n")

# global position origin publisher node
ament_auto_add_library(global_position_origin_publisher SHARED src/global_position_origin_publisher.cpp)
rclcpp_components_register_nodes(global_position_origin_publisher "aqita::ros2::topic_tools::GlobalPositionOriginPublisher")
set(node_plugins "${node_plugins}aqita::ros2::topic_tools::GlobalPositionOriginPublisher;$<TARGET_FILE:global_position_origin_publisher>\n")

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_auto_package()
