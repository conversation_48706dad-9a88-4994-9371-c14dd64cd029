# aqita_topic_tools

The package contains various nodes for ROS2 topics manipulation as well as nodes for publishing specific topics as needed by the AQITA warehouse inventory management UAV. 

## Dependencies

The package depends on the following additional ROS2 packages:
- geographic_msgs

## Installing the necessary dependencies by building from source

Clone the necessary ROS2 packages into the src folder of the respective ROS2 workspace (this has to be done in a terminal **outside** of any running docker containers)

```bash
git clone https://github.com/ros-geographic-info/geographic_info.git -b ros2
```

Build the obtained ROS2 packages using colcon (this has to be done in a terminal **inside** of a running docker container, if such is used)

```bash
colcon build --symlink-install --packages-up-to geographic_msgs
```

## Build

To build the package run the following command in a terminal inside of a running docker container, if such is used

```bash
colcon build --symlink-install --packages-select aqita_topic_tools
```
