# AQITA-ROS

The repository contains a collection of custom ROS2 packages for the AQITA warehouse inventory management UAV.

## Features

- **Custom ROS2 Packages**: Includes launch files, configurations, and nodes for UAV operations.
- **Realsense Camera Integration**: Pre-configured support for Realsense D455 cameras.
- **Dockerized Development**: Simplified setup using Docker for consistent environments.
- **Multi-Camera Support**: Configurations for multi-camera systems with synchronized data streams.
- **RTAB-Map Integration**: Ready-to-use configurations for real-time appearance-based mapping.

## Prerequisites

Before using this repository, ensure you have the following installed:

- Ubuntu 22.04
- ROS2 Humble
- Docker and NVIDIA Container Toolkit (for Dockerized workflows)
- NVIDIA GPU with CUDA support (if applicable)

## Quickstart

1. For deployment on a x86 system follow the instructions [here](https://uvionix.atlassian.net/wiki/spaces/AW/pages/13042090/Notes+on+initial+system+deployment+on+x86)
2. For deployment on a Jetson Orin NX device follow the instructions [here](https://uvionix.atlassian.net/wiki/spaces/AW/pages/13172748/Notes+on+initial+system+deployment+on+the+Jetson+Orin+NX+16GB)

## Docker Container

### Container Registry Access

The AQITA Docker images are hosted on GitHub Container Registry (GHCR). The `run_aqita.sh` script will attempt to pull the appropriate image based on your system architecture (x86_64 or ARM/aarch64).

To manually log in to the GitHub Container Registry:

1. Create a GitHub Personal Access Token (PAT) with `read:packages` scope:
   - Go to GitHub → Settings → Developer settings → Personal access tokens
   - Generate a new token with appropriate permissions
   - Save the token securely

2. Login to the GitHub Container Registry:
   ```bash
   echo $GITHUB_PAT | docker login ghcr.io -u USERNAME --password-stdin
   ```
   Replace `USERNAME` with your GitHub username and ensure `$GITHUB_PAT` contains your token.

3. Pull pre-built images (if needed):
   ```bash
   docker pull ghcr.io/uvionix/aqita-x86:latest   # For x86_64 systems
   docker pull ghcr.io/uvionix/aqita-arm:latest   # For ARM/Jetson systems
   ```

### Running the Container

The `run_aqita.sh` script provides several options:

```bash
./run_aqita.sh [OPTIONS]
```

Options:
- `-n, --name NAME`: Container name (default: aqita_container)
- `-i, --image IMAGE`: Docker image to use (auto-detected based on architecture)
- `-w, --workspace DIR`: Workspace directory to mount (default: current directory)
- `-p, --pull`: Pull the latest image before running
- `-a, --docker-arg ARG`: Pass additional arguments to docker run
- `-h, --help`: Show the help message

## Development Workflow

### Building the Workspace

Inside the Docker container, the following commands will build the ROS2 workspace:

```bash
# Build all packages
colcon build --symlink-install

# Build a specific package
colcon build --symlink-install --packages-select aqita_bringup
```

### Running RTAB-Map

To run the RTAB-Map test launch file:

```bash
ros2 launch aqita_launch test_rtabmap.launch.py
```

### Package Structure

- **aqita_bringup**: Contains launch files for bringing up the system
- **aqita_launch**: Contains test and demo launch files
- **aqita_description**: Contains robot and sensor descriptions
- **aqita_navigation**: Contains navigation-related packages

## Troubleshooting

### Common Issues

1. **Docker permission errors**: Add your user to the docker group:
   ```bash
   sudo usermod -aG docker $USER
   newgrp docker
   ```

2. **Camera detection issues**: Check USB connections and verify that the camera serial numbers in your configuration match the actual devices.

3. **CUDA errors**: Ensure your NVIDIA drivers are compatible with the CUDA version used in the Docker container.
You can run the folloing command to test if opencv with cuda works inside the docker.
    ```bash
    python3 docker/tests/testopencvcuda.py`
    ``` 

4. **GitHub Container Registry authentication issues**: Make sure your GitHub Personal Access Token has the appropriate permissions and hasn't expired.

### Getting Help

- File an issue on the GitHub repository

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/my-feature`)
3. Make your changes
4. Commit your changes (`git commit -am '[JIRATAG-N] Add my feature'`)
5. Push to the branch (`git push origin feature/my-feature`)
6. Create a new Pull Request
