[tool.black]
line-length = 120                    # Max line length before wrapping
target-version = ["py38", "py39"]    # Adjust based on your minimum supported Python versions
skip-string-normalization = true     # Preserve existing quotes (no forced double-quotes)
skip-magic-trailing-comma = false    # Enable "magic" trailing commas for cleaner diffs
preview = true                       # Use the latest stable preview style from Black
include = '\.pyi?$'                  # Only format Python files
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''
