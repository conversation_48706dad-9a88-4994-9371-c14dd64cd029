{"cert": "{{CERT}}", "key": "{{<PERSON>E<PERSON>}}", "root-ca": "/home/<USER>/CA/root-CA.crt", "thing-name": "{{thing_name}}", "endpoint": "{{iot_endpoint}}", "logging": {"level": "INFO", "type": "STDOUT", "file": "{{LOG_FILE}}", "enable-sdk-logging": true, "sdk-log-level": "INFO", "sdk-log-file": "{{SDK_LOG_FILE}}"}, "tunneling": {"enabled": true}, "fleet-provisioning": {"enabled": true, "template-name": "{{template_name}}", "template-parameters": "{\"ThingName\": \"{{thing_name}}\", \"Serial\": \"{{thing_serial}}\"}", "csr-file": "", "device-key": ""}, "sample-shadow": {"enabled": true, "shadow-name": "{{SHADOW_NAME}}"}}