import threading
import time
import pandas as pd
from datetime import datetime
import os
from queue import Queue
import queue
import logging
from typing import Dict, Any

class MetricArchiver:
    """
    A class to archive metrics to Parquet files in a non-blocking manner.
    Creates new Parquet files every 30 minutes and handles the archiving in a background thread.
    """
    
    def __init__(self, logger=None, archive_dir: str = "metric_archives"):
        """
        Initialize the MetricArchiver.
        
        Args:
            archive_dir (str): Directory where Parquet files will be stored
        """
        self.archive_dir = archive_dir
        self.queue = Queue()
        self.running = False
        self.current_file = None
        self.current_df = None
        self.last_file_creation = None
        self.ARCHIVE_INTERVAL = 5 * 60  # 30 minutes in seconds
        self.WRITE_INTERVAL = 5  # Write to disk every 5 seconds
        self.last_write_time = time.time()
        
        # Create archive directory if it doesn't exist
        os.makedirs(archive_dir, exist_ok=True)
        
        # Setup logging
        self.logger = logger or logging.getLogger(__name__)
        
        # Start the background thread
        self.thread = threading.Thread(target=self._archive_worker, daemon=True, name="ArchiveThread")
        self.thread.start()
    
    def _create_empty_dataframe(self) -> pd.DataFrame:
        """Create an empty DataFrame with the correct schema and dtypes."""
        return pd.DataFrame({
            'drone_id': pd.Series(dtype='str'),
            'timestamp': pd.Series(dtype='float64'),
            'name': pd.Series(dtype='str'),
            'type': pd.Series(dtype='str'),
            'value': pd.Series(dtype='float64'),
            'tenant': pd.Series(dtype='str')
        })
    
    def _metric_to_dataframe(self, metric_data: Dict[str, Any]) -> pd.DataFrame:
        """Convert a metric dictionary to a DataFrame row with proper types."""
        return pd.DataFrame({
            'drone_id': [str(metric_data['drone_id'])],
            'timestamp': [float(metric_data['timestamp'])],
            'name': [str(metric_data['name'])],
            'type': [str(metric_data['type'])],
            'value': [float(metric_data['value'])],
            'tenant': [str(metric_data['tenant'])]
        })
    
    def _multivalue_to_dataframe(self, metric_data: Dict[str, Any]) -> pd.DataFrame:
        """
        Convert a DB_MULTIVALUE metric dictionary to a DataFrame with multiple rows.
        
        Args:
            metric_data (Dict[str, Any]): The metric data containing multiple values
            
        Returns:
            pd.DataFrame: DataFrame with one row per value
        """
        rows = []
        timestamp = float(metric_data['timestamp'])
        drone_id = str(metric_data['drone_id'])
        tenant = str(metric_data.get('tenant', 'DUMMY'))
        metric_type = str(metric_data.get('type', 'DUMMY'))
        
        for name, value in metric_data['values'].items():
            rows.append({
                'drone_id': drone_id,
                'timestamp': timestamp,
                'name': str(name),
                'type': metric_type,
                'value': float(value),
                'tenant': tenant
            })
            
        return pd.DataFrame(rows)

    def _multirecord_to_dataframe(self, metric_data: Dict[str, Any]) -> pd.DataFrame:
        """
        Convert a DB_MULTIRECORD metric dictionary to a DataFrame with multiple rows.
        
        Args:
            metric_data (Dict[str, Any]): The metric data containing multiple records
            
        Returns:
            pd.DataFrame: DataFrame with one row per record
        """
        rows = []
        drone_id = str(metric_data['drone_id'])
        tenant = str(metric_data.get('tenant', 'DUMMY'))
        metric_type = str(metric_data.get('type', 'DUMMY'))
        
        for record in metric_data['values']:
            rows.append({
                'drone_id': drone_id,
                'timestamp': float(record['timestamp']),
                'name': str(record['name']),
                'type': metric_type,
                'value': float(record['value']),
                'tenant': tenant
            })
            
        return pd.DataFrame(rows)
    
    def _write_to_parquet(self) -> None:
        """Write current data to parquet file, handling existing data properly."""
        start_time = time.time()
        
        if os.path.exists(self.current_file):
            read_start = time.time()
            existing_df = pd.read_parquet(
                self.current_file, 
                engine='pyarrow', 
                use_threads=True,
                memory_map=True
            )
            read_time = time.time() - read_start
            
            concat_start = time.time()
            combined_df = pd.concat([existing_df, self.current_df], ignore_index=True)
            concat_time = time.time() - concat_start
        else:
            read_time = 0.0
            concat_time = 0.0
            combined_df = self.current_df
        
        write_start = time.time()
        combined_df.to_parquet(
            self.current_file, 
            index=False, 
            engine='pyarrow',
            # memory_map=True
        )
        write_time = time.time() - write_start
        
        total_time = time.time() - start_time
        self.logger.info(
            f"Archive timing [read={read_time:.3f}s, concat={concat_time:.3f}s, "
            f"write={write_time:.3f}s, total={total_time:.3f}s]"
        )
        
        self.last_write_time = time.time()
        self.current_df = self._create_empty_dataframe()
    
    def archive_metric(self, metric_data: Dict[str, Any]) -> None:
        """
        Queue a metric for archiving. This is non-blocking.
        
        Args:
            metric_data (Dict[str, Any]): The metric data to archive
        """
        self.queue.put(metric_data)
    
    def _get_new_filename(self) -> str:
        """Generate a new filename based on the current timestamp."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return os.path.join(self.archive_dir, f"metrics_{timestamp}.parquet")
    
    def _should_create_new_file(self) -> bool:
        """Check if we should create a new Parquet file."""
        if self.last_file_creation is None:
            return True
        
        time_since_last = time.time() - self.last_file_creation
        return time_since_last >= self.ARCHIVE_INTERVAL
    
    def _create_new_file(self) -> None:
        """Create a new Parquet file and initialize the DataFrame with the correct schema."""
        self.current_file = self._get_new_filename()
        self.current_df = self._create_empty_dataframe()
        self.last_file_creation = time.time()
        self.logger.info(f"Created new archive file: {self.current_file}")
    
    def _archive_worker(self) -> None:
        """Background worker that processes the queue and writes to Parquet files."""
        self.running = True
        
        while self.running:
            try:
                # Get metric data from queue with timeout
                metric_data = self.queue.get(timeout=1.0)
                
                # Check if we need a new file
                if self._should_create_new_file():
                    self._create_new_file()
                
                # Handle different metric types
                if metric_data.get("type") == "DB_MULTIRECORD":
                    df_row = self._multirecord_to_dataframe(metric_data)
                elif metric_data.get("type") == "DB_MULTIVALUE":
                    df_row = self._multivalue_to_dataframe(metric_data)
                else:
                    df_row = self._metric_to_dataframe(metric_data)
                
                
                # Concatenate with explicit handling of empty DataFrames
                if len(self.current_df) == 0:
                    self.current_df = df_row
                else:
                    self.current_df = pd.concat([self.current_df, df_row], ignore_index=True)
                
                # Write to disk if enough time has passed
                current_time = time.time()
                if current_time - self.last_write_time >= self.WRITE_INTERVAL:
                    self._write_to_parquet()
                
                self.queue.task_done()
                
            except queue.Empty:
                # No data in queue, write any buffered data if enough time has passed
                current_time = time.time()
                if (self.current_df is not None and 
                    len(self.current_df) > 0 and 
                    current_time - self.last_write_time >= self.WRITE_INTERVAL):
                    self._write_to_parquet()
                continue
            except Exception as e:
                self.logger.error(f"Error in archive worker: {e}")
    
    def stop(self) -> None:
        """Stop the archiver and wait for the queue to be processed."""
        self.running = False
        self.queue.join()  # Wait for queue to be empty
        
        # Write any remaining buffered data
        if self.current_df is not None and len(self.current_df) > 0:
            self._write_to_parquet()
            
        if self.thread.is_alive():
            self.thread.join(timeout=5.0)  # Wait for thread to finish 