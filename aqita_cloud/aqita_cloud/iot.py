"""
Robust MQTT Client with SQLite-based message buffering and automatic retry capabilities.

This module provides a resilient MQTT client that:
- Uses SQLite for persistent message buffering
- Implements automatic retry logic with configurable intervals
- Handles connection state management with indefinite reconnection
- Provides message ordering
- Ensures thread-safe operations
"""

import glob
import queue
import threading
import time
import sqlite3
import logging
import os
from typing import Optional, Dict, Any, Callable
from dataclasses import dataclass
from awscrt import io, mqtt, mqtt_request_response, io, mqtt_request_response
from awsiot import mqtt_connection_builder, iotshadow, ServiceStreamOptions, V2ServiceException
import concurrent.futures
from threading import Lock
from aqita_cloud.provisioning import SHADOW_OUTPUT_CONTAINER
import datetime

import orj<PERSON>
import uuid

from aqita_cloud.shadow_handler import ShadowHandler
from aqita_cloud.mqtt_message_buffer import MqttMessageBuffer


@dataclass
class ProcessingConfig:
    """Configuration for message processing behavior."""
    processing_interval: float = 0.5  # seconds
    vacuum_interval: float = 60.0  # 1 minute in seconds


@dataclass
class MQTTConfig:
    """Configuration for MQTT connection."""
    endpoint: str
    client_id: str
    thing_name: str
    cert_filepath: str
    pri_key_filepath: str
    ca_filepath: str
    clean_session: bool = False
    keep_alive_secs: int = 10
    ping_timeout_ms: int = 3000
    protocol_operation_timeout_ms: int = 5000
    tcp_connect_timeout_ms: int = 5000
    reconnect_min_timeout_secs: int = 1
    reconnect_max_timeout_secs: int = 5


class RobustMQTTClient:
    """
    A robust MQTT client with SQLite-based message buffering and automatic retry capabilities.
    
    Features:
    - Persistent message storage using SQLite
    - Automatic connection retry with exponential backoff
    - Thread-safe operations
    - Non-blocking publish and subscribe operations
    - Graceful shutdown and cleanup
    """

    def __init__(
        self,
        mqtt_config: MQTTConfig,
        processing_config: Optional[ProcessingConfig] = None,
        db_path: str = "mqtt_buffer.db",
        logger: Optional[logging.Logger] = None,
        clear=False
    ):
        """
        Initialize the robust MQTT client.
        
        Args:
            mqtt_config: MQTT connection configuration
            processing_config: Message processing behavior configuration
            db_path: Path to SQLite database file
            logger: Optional logger instance
        """
        self.mqtt_config = mqtt_config
        self.processing_config = processing_config or ProcessingConfig()
        self.logger = logger or logging.getLogger(__name__)
        
        # Connection state management
        self.is_connected = threading.Event()
        self.is_connected.clear()
        self.shutdown_event = threading.Event()
        
        # Thread management
        self.connection_thread: Optional[threading.Thread] = None
        self.processing_thread: Optional[threading.Thread] = None
        self.subscription_thread: Optional[threading.Thread] = None
        self.batch_update_thread: Optional[threading.Thread] = None
        self.batch_delete_thread: Optional[threading.Thread] = None
        self.batch_insert_thread: Optional[threading.Thread] = None

        self.threads_started = False
        self.initial_connect = False
        
        # Initialize message buffer
        self.message_buffer = MqttMessageBuffer(
            db_path=db_path,
            logger=self.logger,
            clear=clear,
            insert_batch_size=1000,
            insert_batch_seconds=1.0,
            update_batch_size=1000,
            delete_batch_size=1000,
            vacuum_interval=self.processing_config.vacuum_interval
        )
        
        # MQTT connection
        self.mqtt_connection = None
        
        # Shadow handler will be initialized after MQTT connection
        self.shadow_handler: Optional[ShadowHandler] = None

        # Setup MQTT connection
        self._setup_mqtt_connection()


    def _setup_mqtt_connection(self):
        """Setup MQTT connection with callbacks."""
        try:
            self.mqtt_connection = mqtt_connection_builder.mtls_from_path(
                endpoint=self.mqtt_config.endpoint,
                cert_filepath=self.mqtt_config.cert_filepath,
                pri_key_filepath=self.mqtt_config.pri_key_filepath,
                client_bootstrap=io.ClientBootstrap.get_or_create_static_default(),
                ca_filepath=self.mqtt_config.ca_filepath,
                client_id=self.mqtt_config.client_id,
                clean_session=self.mqtt_config.clean_session,
                keep_alive_secs=self.mqtt_config.keep_alive_secs,
                ping_timeout_ms=self.mqtt_config.ping_timeout_ms,
                protocol_operation_timeout_ms=self.mqtt_config.protocol_operation_timeout_ms,
                tcp_connect_timeout_ms=self.mqtt_config.tcp_connect_timeout_ms,
                reconnect_min_timeout_secs=self.mqtt_config.reconnect_min_timeout_secs,
                reconnect_max_timeout_secs=self.mqtt_config.reconnect_max_timeout_secs,
                on_connection_interrupted=self._on_connection_interrupted,
                on_connection_resumed=self._on_connection_resumed,
                on_connection_success=self._on_connection_success,
                on_connection_failure=self._on_connection_failure,
            )
        except Exception as e:
            self.logger.error(f"Failed to setup MQTT connection: {e}")
            raise

        try:
            # Initialize shadow handler
            self.shadow_handler = ShadowHandler(
                mqtt_connection=self.mqtt_connection,
                thing_name=self.mqtt_config.thing_name,
                logger=self.logger
            )
        except Exception as e:
            self.logger.error(f"Failed to setup MQTT shadow connection: {e}")
            raise

    def _on_connection_success(self, connection, callback_data):
        """Callback for successful connection."""
        self.logger.info("MQTT connection established successfully")
        self.is_connected.set()

    def _on_connection_failure(self, connection, callback_data):
        """Callback for connection failure."""
        self.logger.warning(f"MQTT connection failed: {callback_data}")
        self.is_connected.clear()

    def _on_connection_interrupted(self, connection, error, **kwargs):
        """Callback for connection interruption."""
        self.logger.warning(f"MQTT connection interrupted: {error}")
        self.is_connected.clear()

    def _on_connection_resumed(self, connection, return_code, session_present, **kwargs):
        """Callback for connection resumption."""
        self.logger.info(f"MQTT connection resumed: return_code={return_code}, session_present={session_present}")
        self.is_connected.set()

    def _insert_processor(self):
        """Background worker to process message inserts."""
        while not self.shutdown_event.is_set():
            try:
                did_process = self.message_buffer.process_insert_batch()
                if not did_process:
                    self.shutdown_event.wait(timeout=self.processing_config.processing_interval)
            except Exception as e:
                self.logger.error(f"Error in insert processor: {e}")
                time.sleep(1)

    def _delete_message_processor(self):
        """Background worker to delete sent messages."""
        while not self.shutdown_event.is_set():
            did_process = self.message_buffer.process_delete_batch()
            if not did_process:
                self.shutdown_event.wait(timeout=self.processing_config.processing_interval)

    def _update_message_processor(self):
        """Background worker to update failed messages for retry."""
        while not self.shutdown_event.is_set():
            did_process = self.message_buffer.process_update_batch()
            if not did_process:
                self.shutdown_event.wait(timeout=self.processing_config.processing_interval)

    def _on_publish_complete(self, future, msg_id: int, topic: str):
        """Callback for when a message publish completes."""
        try:
            future.result()  # This will raise an exception if publish failed
            # Delete the message on successful publish
            self.logger.info(f"Published message to {topic} (Message ID: {msg_id})")
            self.message_buffer.mark_message_for_deletion(msg_id)
        except Exception as e:
            # Mark message for retry
            self.message_buffer.mark_message_for_retry(msg_id)
            self.logger.warning(f"Failed to publish message MSG_ID: {msg_id} to {topic} , will retry later: {e}")

    def start(self):
        """Start the MQTT client and background threads."""
        if self.threads_started:
            self.logger.warning("Client already started")
            return
            
        self.logger.info("Starting robust MQTT client...")

        # Start shadow handler
        if self.shadow_handler:
            self.shadow_handler.start()

        # Start vacuum thread in message buffer
        self.message_buffer.start_vacuum_thread()

        self.batch_insert_thread = threading.Thread(target=self._insert_processor, daemon=True)
        self.batch_insert_thread.start()

        # Start connection management thread
        self.connection_thread = threading.Thread(target=self._connection_manager, daemon=True)
        self.connection_thread.start()

        # Start message processing thread
        self.processing_thread = threading.Thread(target=self._message_processor, daemon=True)
        self.processing_thread.start()

        # Start subscription processing thread
        self.subscription_thread = threading.Thread(target=self._subscription_processor, daemon=True)
        self.subscription_thread.start()

        self.batch_delete_thread = threading.Thread(target=self._delete_message_processor, daemon=True)
        self.batch_delete_thread.start()

        self.batch_update_thread = threading.Thread(target=self._update_message_processor, daemon=True)
        self.batch_update_thread.start()

        self.threads_started = True
        self.logger.info("Robust MQTT client started successfully")

    def stop(self):
        """Stop the MQTT client and cleanup resources."""
        self.logger.info("Stopping robust MQTT client...")
        
        # Signal shutdown
        self.shutdown_event.set()
        
        # Stop shadow handler
        if self.shadow_handler:
            self.shadow_handler.stop()
        
        # Disconnect MQTT
        if self.mqtt_connection and self.is_connected.is_set():
            try:
                disconnect_future = self.mqtt_connection.disconnect()
                disconnect_future.result(timeout=5)
            except Exception as e:
                self.logger.error(f"Error during MQTT disconnect: {e}")
        
        # Wait for threads to finish
        for thread in [self.batch_insert_thread, self.connection_thread,
                      self.processing_thread, self.subscription_thread,
                      self.batch_update_thread, self.batch_delete_thread]:
            if thread and thread.is_alive():
                thread.join(timeout=2)
        
        # Clean up callbacks
        self.message_buffer.clear_callbacks()
        
        # Close database
        self.message_buffer.close()
        
        self.threads_started = False
        self.logger.info("Robust MQTT client stopped")

    def _connection_manager(self):
        """Manage MQTT connection with indefinite retry."""
        while not self.shutdown_event.is_set():
            if not self.is_connected.is_set():
                try:
                    if not self.initial_connect:
                        self.logger.info("Attempting to connect to MQTT broker...")
                        connect_future = self.mqtt_connection.connect()
                        connect_future.result(timeout=5)
                        self.initial_connect = True
                except Exception as e:
                    self.logger.warning(f"MQTT connection attempt failed: {e}")
                    # Wait before retrying
                    self.shutdown_event.wait(timeout=5)
            else:
                # Wait while connected
                self.shutdown_event.wait(timeout=1)

    def _message_processor(self):
        """Background worker to process pending messages."""
        while not self.shutdown_event.is_set():

            if self.is_connected.is_set():
                did_process = self._process_pending_messages()
                
                # If messages were processed, continue to the next cycle
                if did_process:
                    continue

            # Wait before next processing cycle only if no messages are pending
            self.shutdown_event.wait(timeout=self.processing_config.processing_interval)
            

    def _process_pending_messages(self):
        """Process pending messages from the database."""
        try:
            messages = self.message_buffer.get_pending_messages(limit=100)
            
            if messages:
                message_ids = [msg[0] for msg in messages]
                self.message_buffer.mark_messages_processing(message_ids)
            else:
                return False

            for msg_id, topic, payload, qos, timestamp in messages:
                if self.shutdown_event.is_set() or not self.is_connected.is_set():
                    break
                
                # Attempt to publish the message
                self._attempt_publish(topic, payload, qos, msg_id)

            return True

        except Exception as e:
            self.logger.error(f"Error processing pending messages: {e}")
            return False

    def _attempt_publish(self, topic: str, payload: str, qos: int, msg_id: int) -> bool:
        """
        Attempt to publish a message via MQTT.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.is_connected.is_set():
                return False

            mqtt_qos = mqtt.QoS.AT_MOST_ONCE if qos == 0 else mqtt.QoS.AT_LEAST_ONCE

            publish_future, packet_id = self.mqtt_connection.publish(
                topic=topic,
                payload=payload,
                qos=mqtt_qos
            )
            publish_future.add_done_callback(lambda future: self._on_publish_complete(future, msg_id, topic))
            return True

        except Exception as e:
            self.logger.debug(f"Failed to publish message {msg_id}: {e}")
            return False

    def publish(
        self,
        topic: str,
        payload: dict,
        qos: int = 1,
    ) -> int:
        """
        Publish a message with automatic buffering and retry.
        This is a non-blocking operation that only writes to the database.
        The actual publishing will be handled by the background processing thread.

        Args:
            topic: MQTT topic to publish to
            payload: Message payload as dict
            qos: Quality of Service level (0 or 1)

        Returns:
            int: Message ID of the inserted message
        """
        str_payload = orjson.dumps(payload).decode()
        
        success = self.message_buffer.queue_message_for_insert(topic, str_payload, qos)
        if not success:
            raise Exception("Failed to queue message for publishing")
        
        return 1


    def subscribe(
        self,
        topic: str,
        callback: Callable[[str, bytes], None],
        qos: int = 1
    ) -> int:
        """
        Subscribe to an MQTT topic with automatic buffering and retry.
        This is a non-blocking operation that only writes to the database.
        The actual subscription will be handled by the background processing thread.

        Args:
            topic: MQTT topic to subscribe to
            callback: Callback function to handle received messages
            qos: Quality of Service level (0 or 1)

        Returns:
            int: Subscription ID of the inserted subscription request
        """
        current_time = time.time()
        callback_name = f"callback_{current_time}_{id(callback)}"
        
        # Store callback in the message buffer
        self.message_buffer.store_callback(callback_name, callback)
        
        # Store subscription request in database
        subscription_id = self.message_buffer.store_subscription(topic, qos, callback_name)
        
        if subscription_id is None:
            raise Exception("Failed to store subscription request")
        
        return subscription_id

    def _subscription_processor(self):
        """Background worker to process pending subscriptions."""
        while not self.shutdown_event.is_set():
            if self.is_connected.is_set():
                did_process = self._process_pending_subscriptions()
                
                # If subscriptions were processed, continue to the next cycle
                if did_process:
                    continue

            # Wait before next processing cycle only if no subscriptions are pending
            self.shutdown_event.wait(timeout=self.processing_config.processing_interval)

    def _process_pending_subscriptions(self):
        """Process pending subscriptions from the database."""
        try:
            subscriptions = self.message_buffer.get_pending_subscriptions(limit=100)
            
            if subscriptions:
                subscription_ids = [sub[0] for sub in subscriptions]
                self.message_buffer.mark_subscriptions_processing(subscription_ids)
            else:
                return False

            for sub_id, topic, qos, callback_name in subscriptions:
                if self.shutdown_event.is_set() or not self.is_connected.is_set():
                    break
                
                # Attempt to subscribe
                if self._attempt_subscribe(topic, qos, callback_name, sub_id):
                    # If successful, remove the subscription request
                    self.message_buffer.delete_subscription(sub_id)
                else:
                    # If failed, mark for retry
                    self.message_buffer.reset_subscription_processing(sub_id)

            return True

        except Exception as e:
            self.logger.error(f"Error processing pending subscriptions: {e}")
            return False

    def _attempt_subscribe(self, topic: str, qos: int, callback_name: str, sub_id: int) -> bool:
        """
        Attempt to subscribe to a topic via MQTT.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.is_connected.is_set():
                return False

            mqtt_qos = mqtt.QoS.AT_MOST_ONCE if qos == 0 else mqtt.QoS.AT_LEAST_ONCE
            callback = self.message_buffer.get_callback(callback_name)
            
            if callback is None:
                self.logger.error(f"Callback {callback_name} not found for subscription {sub_id}")
                return False

            subscribe_future, packet_id = self.mqtt_connection.subscribe(
                topic=topic,
                qos=mqtt_qos,
                callback=callback
            )

            subscribe_future.result(timeout=5)
            self.logger.info(f"Successfully subscribed to topic: {topic}")
            
            # Clean up callback after successful subscription
            self.message_buffer.remove_callback(callback_name)
            return True

        except Exception as e:
            self.logger.debug(f"Failed to subscribe to topic {topic}: {e}")
            return False

    def unsubscribe(self, topic: str) -> bool:
        """
        Unsubscribe from an MQTT topic.

        Args:
            topic: MQTT topic to unsubscribe from

        Returns:
            bool: True if unsubscription successful, False otherwise
        """
        try:
            if not self.is_connected.is_set():
                self.logger.warning(f"Cannot unsubscribe from {topic}: not connected")
                return False

            unsubscribe_future, packet_id = self.mqtt_connection.unsubscribe(topic=topic)
            unsubscribe_future.result(timeout=5)
            self.logger.info(f"Successfully unsubscribed from topic: {topic}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to unsubscribe from topic {topic}: {e}")
            return False

    def get_pending_message_count(self) -> int:
        """
        Get the number of pending messages in the buffer.

        Returns:
            int: Number of pending messages
        """
        return self.message_buffer.get_pending_message_count()

    def get_connection_status(self) -> Dict[str, Any]:
        """
        Get current connection status and statistics.

        Returns:
            dict: Connection status information
        """
        return {
            'connected': self.is_connected.is_set(),
            'pending_messages': self.get_pending_message_count(),
            'threads_started': self.threads_started,
            'client_id': self.mqtt_config.client_id,
            'endpoint': self.mqtt_config.endpoint,
            'rate': self.get_message_rate()
        }

    def __del__(self):
        """Destructor to ensure cleanup."""
        if hasattr(self, 'threads_started') and self.threads_started:
            self.stop()
        
        # Close database connection
        if hasattr(self, 'db_connection'):
            try:
                self.db_connection.close()
            except Exception as e:
                if hasattr(self, 'logger'):
                    self.logger.error(f"Error closing database connection: {e}")



    def get_message_rate(self) -> float:
        """
        Get the current message processing rate in messages per second.

        Returns:
            float: Current message processing rate
        """
        return self.message_buffer.get_message_rate()

    def subscribe_to_shadow(self, callback: Callable):
        """Subscribe to shadow update events."""
        if self.shadow_handler:
            self.shadow_handler.subscribe_to_shadow(callback)
        else:
            self.logger.error("Shadow handler not initialized")

    def subscribe_to_shadow_delta(self, callback: Callable):
        """Subscribe to shadow delta events."""
        if self.shadow_handler:
            self.shadow_handler.subscribe_to_shadow_delta(callback)
        else:
            self.logger.error("Shadow handler not initialized")

    def update_my_shadow(self, reported=None, desired=None, init=False):
        """Update the shadow state."""
        if self.shadow_handler:
            return self.shadow_handler.update_shadow(reported=reported, desired=desired, init=init)
        else:
            self.logger.error("Shadow handler not initialized")
            return None
