import json
import os
import time
import logging
from aqita_cloud.busctl import Systemd
from uuid import uuid4
import requests

SVC_UNIT = 'aws_client.service'
DBUS_SVC_UNIT = 'aws_5fclient_2service'

# DOCKER_ARGS+=("-v $HOME/.aws-iot-device-client:/aws_client_settings_dir")

DEVICE_CLIENT_DIRECTORY = '/aws_client_settings_dir'

# used inside the docker container
CLAIM_CERT_LOCATION = f"{DEVICE_CLIENT_DIRECTORY}/claim/certificate.pem"
CLAIM_PKEY_LOCATION = f"{DEVICE_CLIENT_DIRECTORY}/claim/private.key"
ROOT_CA_LOCATION = f"{DEVICE_CLIENT_DIRECTORY}/CA/root-CA.crt"
SDK_LOG_FILE = f"{DEVICE_CLIENT_DIRECTORY}/logs/sdk.log"
CLIENT_LOG_FILE = f"{DEVICE_CLIENT_DIRECTORY}/logs/client.log"

# used to create the client config that runs outside
HOME_DIR = os.getenv('HOME_DIR', '/root')
REAL_DEVICE_CLIENT_DIRECTORY = f"{HOME_DIR}/.aws-iot-device-client"
REAL_CLAIM_CERT_LOCATION = f"{REAL_DEVICE_CLIENT_DIRECTORY}/claim/certificate.pem"
REAL_CLAIM_PKEY_LOCATION = f"{REAL_DEVICE_CLIENT_DIRECTORY}/claim/private.key"
REAL_ROOT_CA_LOCATION = f"{REAL_DEVICE_CLIENT_DIRECTORY}/CA/root-CA.crt"
REAL_SDK_LOG_FILE = f"{REAL_DEVICE_CLIENT_DIRECTORY}/logs/sdk.log"
REAL_CLIENT_LOG_FILE = f"{REAL_DEVICE_CLIENT_DIRECTORY}/logs/client.log"


CLIENT_RUNTIME_CONFIG = f"{DEVICE_CLIENT_DIRECTORY}/aws-iot-device-client-runtime.conf"
# write only
CLIENT_CONFIG_FILE = f"{DEVICE_CLIENT_DIRECTORY}/client_config.json"
ACTIVE_CERT = f"{DEVICE_CLIENT_DIRECTORY}/keys/active-certificate.pem.crt"
ACTIVE_PKEY = f"{DEVICE_CLIENT_DIRECTORY}/keys/active-private.pem.key"

CLIENT_DRONE_CONFIG = f"{DEVICE_CLIENT_DIRECTORY}/drone.json"

AWS_NODE_LOCATION = f"/workspace/aqita_cloud"
CA_PEM_URL = "https://www.amazontrust.com/repository/AmazonRootCA1.pem"
CA_CER_URL = "https://www.amazontrust.com/repository/AmazonRootCA1.cer"

SHADOW_NAME = "aqita_shadow"
SHADOW_INPUT = f"{REAL_DEVICE_CLIENT_DIRECTORY}/shadow_input.json"
SHADOW_OUTPUT = f"{REAL_DEVICE_CLIENT_DIRECTORY}/shadow_output.json"

SHADOW_INPUT_CONTAINER = f"{DEVICE_CLIENT_DIRECTORY}/shadow_input.json"
SHADOW_OUTPUT_CONTAINER = f"{DEVICE_CLIENT_DIRECTORY}/shadow_output.json"


class Provisioner:

    def __init__(self, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.service_name = 'aws_client.service'
        self.systemd = Systemd(self.logger)
        self.aws_data = {}
        self.qr_data = {}
        self.api_data = {}

        # self.read_aws_config()
        self.read_qr_data()
        

    def get_serial(self):
        # TODO:Extract from somewhere
        # try:
        #     with open('/etc/machine-id', 'r') as f:
        #         return f.read().strip()
        # except FileNotFoundError:
        #     self.logger.error("Could not read /etc/machine-id, using UUID instead.")
        #     # Fallback to UUID if machine-id is not available
        return str(uuid4()).replace('-', '')

    # TODO:FIXME: this is a mock
    def read_qr_data(self):
        with open(f"{AWS_NODE_LOCATION}/qr_sample.json", 'r') as fp:
            self.qr_data = json.load(fp)
                    
    def download_provisioning_credentials(self):
        
        ca_resp = requests.get(
            url=CA_PEM_URL
        )
        with open(ROOT_CA_LOCATION, "wb") as fp:
            fp.write(ca_resp.content)

        resp = requests.get(
            url = f"{self.qr_data.get('api-endpoint')}/provision/{self.qr_data.get('facility-id')}",
            headers={
                "X-Tenant-ID": self.qr_data.get('tenant'),
                "QR-CODE-KEY": self.qr_data.get('qr-key')
            },
        )

        if not resp.ok:
            raise RuntimeError(
                f"Failed to download provisioning credentials: {resp.status_code} - {resp.text}"
            )
        
        self.api_data = resp.json()

        
        with open(CLAIM_CERT_LOCATION, "w") as fp:
            fp.write(self.api_data['claim_cert'])

        with open(CLAIM_PKEY_LOCATION, "w") as fp:
            fp.write(self.api_data['claim_key'])


    def ensure_directories(self):
        os.makedirs(f"{DEVICE_CLIENT_DIRECTORY}/logs", exist_ok=True)
        os.makedirs(f"{DEVICE_CLIENT_DIRECTORY}/claim", exist_ok=True)
        os.makedirs(f"{DEVICE_CLIENT_DIRECTORY}/CA", exist_ok=True)

        os.chmod(f"{DEVICE_CLIENT_DIRECTORY}/logs", 0o745)
        os.chmod(f"{DEVICE_CLIENT_DIRECTORY}/claim", 0o700)
        os.chmod(f"{DEVICE_CLIENT_DIRECTORY}/CA", 0o700)


    def ensure_permissions(self):
        # os.makedirs(DEVICE_CLIENT_PATH, exist_ok=True)
        
        os.chmod(CLAIM_CERT_LOCATION, 0o644)
        os.chmod(CLAIM_PKEY_LOCATION, 0o600)
        os.chmod(ROOT_CA_LOCATION, 0o644)
        os.chmod(SHADOW_INPUT_CONTAINER, 0o600)
        os.chmod(SHADOW_OUTPUT_CONTAINER, 0o600)

    def ensure_shadow_files(self):
        with open(SHADOW_INPUT_CONTAINER, 'w') as fp:
            json.dump({}, fp)

        with open(SHADOW_OUTPUT_CONTAINER, 'w') as fp:
            json.dump({}, fp)

    
    def read_aws_config(self):
        self.aws_data = {}

        with open(f"{AWS_NODE_LOCATION}/aws.json", "r") as fp:
            self.aws_data = json.loads(fp.read())

    def start_aws_iot_device_client(self):

        self.logger.info("Starting AWS IoT Device Client...")

        self.systemd.daemon_reload()
        self.systemd.stop_service(self.service_name)

        self.systemd.start_service(self.service_name)

        time.sleep(3)  # Wait for the service to start

        [active_state, sub_state] = self.systemd.service_statuses(self.service_name)

        self.logger.info(f"{self.service_name}: Current state: ActiveState={active_state}, SubState={sub_state}")

        if active_state != 'active' and sub_state != 'running':
            logging.error(f"Failed to start {self.service_name}. Please check the logs for more details.")
            return False
        

        self.logger.info("AWS IoT Device Client started successfully.")
        
        self.logger.info("AWS IoT Device Client enabled successfully.")
        self.systemd.enable_service(self.service_name)
        self.systemd.daemon_reload()

        return True

    def render_config_template(self, name):
        template = {}
        with open(f"{AWS_NODE_LOCATION}/template.json") as fp:
            template = json.load(fp)

        template['cert'] = REAL_CLAIM_CERT_LOCATION
        template['key'] = REAL_CLAIM_PKEY_LOCATION
        template['root-ca'] = REAL_ROOT_CA_LOCATION
        template['thing-name'] = name

        # with local AWS data mock
        # template['endpoint'] = self.aws_data.get('iot-endpoint')
        # template['fleet-provisioning']['template-name'] = self.aws_data.get('template-name')

        # With calls to the API
        template['endpoint'] = self.api_data.get('iot-endpoint')
        template['fleet-provisioning']['template-name'] = self.api_data.get('template-name')

        device_name = str(self.get_serial())

        template['fleet-provisioning']['template-parameters'] = '{\"ThingName\": \"' + name + '\", \"Serial\": \"' + device_name + '\"}'
        template['logging']['sdk-log-file'] = REAL_SDK_LOG_FILE
        template['logging']['file'] = REAL_CLIENT_LOG_FILE
        template['sample-shadow']['enabled'] = True
        template['sample-shadow']['shadow-name'] = SHADOW_NAME
        template['sample-shadow']['shadow-input-file'] = SHADOW_INPUT
        template['sample-shadow']['shadow-output-file'] = SHADOW_OUTPUT

        with open(CLIENT_CONFIG_FILE, 'w') as fp:
            fp.write(
                json.dumps(
                    template, sort_keys=True, indent=4
                )
            )

    def read_credentials_locations(self):
        try:
            with open(CLIENT_RUNTIME_CONFIG, 'r') as fp:
                config = json.load(fp)

                runtime_cfg = config.get("runtime-config", {})

            with open(CLIENT_DRONE_CONFIG, 'r') as fp:
                drone_cfg = json.load(fp)

            return {
                "completed": runtime_cfg.get("completed-fp", False),
                "cert": ACTIVE_CERT,
                "key": ACTIVE_PKEY,
                "root-ca": ROOT_CA_LOCATION,
                "tenant": drone_cfg['tenant'],
                "thing-name": drone_cfg['thing-name'],
                "iot-endpoint": drone_cfg['iot-endpoint'],
                "api-endpoint": drone_cfg['api-endpoint'],
                "needs-create": drone_cfg['needs-create'],
                "facility-id": drone_cfg['facility-id'],
            }        
        except Exception as e:
            self.logger.error(f"Could not read credentials locations: {e}")
            return False

    def render_drone_configuration(self, name):
        with open(CLIENT_DRONE_CONFIG, "w") as fp:
            json.dump(
                {
                    "tenant": self.qr_data['tenant'],
                    "thing-name": name,
                    "iot-endpoint": self.api_data['iot-endpoint'],
                    "api-endpoint": self.qr_data['api-endpoint'],
                    "facility-id": self.qr_data.get('facility-id'),
                    "needs-create": True
                },
                fp,
                sort_keys=True,
                indent=4
            )

    def confirm_creation(self):
        with open(CLIENT_DRONE_CONFIG, 'r') as fp:
            data = json.load(fp)
        
        data['needs-create'] = False

        with open(CLIENT_DRONE_CONFIG, 'w') as fp:
            json.dump(
                data, fp,
                sort_keys=True,
                indent=4
            )


    def clean_claim_certificates(self):
        os.unlink(CLAIM_CERT_LOCATION)
        os.unlink(CLAIM_PKEY_LOCATION)

    def generate_name(self):
        return f"{self.qr_data['tenant']}-{self.get_serial()}"

    def provision(self) -> dict | bool:

        self.logger.info("Checking if provisioning is needed...")
        credentials = self.read_credentials_locations()
        if credentials and credentials.get("completed"):
            self.logger.info("Credentials already provisioned.")
            return credentials
        
        name = self.generate_name()

        self.ensure_directories()
        self.ensure_shadow_files()

        self.download_provisioning_credentials()
        
        self.logger.info("Rendering AWS Device client configuration...")
        self.render_config_template(name)
        
        self.logger.info("Ensuring correct permissions...")
        self.ensure_permissions()

        

        self.logger.info("Setting up AWS IoT Device Client service...")
        success = self.start_aws_iot_device_client()

        if success:
            self.render_drone_configuration(name)

            credentials =  self.read_credentials_locations()
            if not credentials.get("completed"):
                self.logger.error("Provisioning completed but credentials are not ready.")
                return False
            
            self.clean_claim_certificates()
            self.logger.info("Provisioning completed successfully.")
            return credentials
        else:
            self.logger.error("Provisioning failed. Please check the logs for more details.")
            return False

