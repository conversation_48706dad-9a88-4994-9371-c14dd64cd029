

import os
import logging
import threading
import socket
import queue
import select
import subprocess


SOCKET_FILE = '/run/esc.sock'
SOCKET_BUFFER = 4096


class SocketListener:

    def __init__(self, logger=None, socket_timeout=1) -> None:
        self.logger = logger or logging.getLogger()

        self.server_socket = socket.socket(socket.AF_UNIX, socket.SOCK_DGRAM)
        self.time_to_stop = threading.Event()
        self.socket_timeout = socket_timeout

        self.socket_receive_thread = threading.Thread(
            target=self.__receive_from_socket
        )

        # 500 messages per second for 10 seconds should be enough
        self.receive_queue = queue.Queue(maxsize=500 * 10)

        self.in_queue_mode = False

    def configure(self):
        listen_pid = os.environ.get('LISTEN_PID')
        listen_fds = os.environ.get('LISTEN_FDS')

        # TODO:FIXME:workaround

        subprocess.run(
            ['sudo', 'chmod', '-R', '777', '/run'],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        server_socket: socket.socket = None

        if listen_pid and listen_fds:
            # Only accept if this process is the one systemd intended
            if int(listen_pid) == os.getpid() and int(listen_fds) > 0:
                # systemd passes fds starting at SD_LISTEN_FDS_START (3)
                fd = 3
                self.server_socket = socket.socket(fileno=fd)
                self.logger.info("Socket inherited from systemd (fd=%d)" % fd)
                return self.server_socket
            else:
                self.logger.warning("LISTEN_PID does not match current PID or LISTEN_FDS is 0")
        
        self.logger.info("No systemd socket activation detected, will create socket")

        if os.path.exists(SOCKET_FILE):
            os.remove(SOCKET_FILE)
            self.logger.info("Removed existing socket file: %s" % SOCKET_FILE)
        
        self.logger.info("Binding server socket to %s" % SOCKET_FILE)
        try:
            self.server_socket.bind(SOCKET_FILE)
        except Exception as e:
            self.logger.error("Failed to bind socket: %s" % e)
            raise RuntimeError(f"Failed to bind socket at {SOCKET_FILE}: {e}")
        
        self.logger.info("Configuring socket")

        os.chmod(SOCKET_FILE, 0o666)

        self.server_socket.settimeout(self.socket_timeout)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 1024 * 64)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 1024 * 64)

        self.logger.info(f"Unix domain socket bound and listening at {SOCKET_FILE} " )
    def __receive_from_socket(self):
        while not self.time_to_stop:
            try:
                data, client_address = self.server_socket.recvfrom(SOCKET_BUFFER)
                decoded_data = data.decode('ascii')

                # Send to queue immediately
                self.receive_queue.put_nowait(decoded_data)

            except socket.timeout:
                continue
            except queue.Full:
                self.logger.error("Receive queue full, dropping message!")
            except Exception as ex:
                self.logger.error("Error receiving from socket: %s" % ex)

    def get_message_from_queue(self, timeout=1) -> None | str:
        if not self.in_queue_mode:
            raise ResourceWarning(
                "Use start() if you want to use queued mode!"
            )
        try:
            return self.receive_queue.get(timeout=timeout)
        except queue.Empty:
            return None

    def get_message_from_socket(self, timeout=1) -> None | str:
        try:
            data, client_address = self.server_socket.recvfrom(SOCKET_BUFFER)
            decoded_data = data.decode('ascii')
            return decoded_data
        except socket.timeout:
            return None
        
    def get_message_from_socket_blocking(self) -> str:
        try:
            rlist, _, _ = select.select([self.server_socket], [], [], None)
            if rlist:
                data, client_address = self.server_socket.recvfrom(SOCKET_BUFFER)
                decoded_data = data.decode('ascii')
                return decoded_data
            else:
                return None
        except socket.timeout:
            return None

    def start(self):
        self.configure()
        self.in_queue_mode = True
        self.socket_receive_thread.start()

    def stop(self):
        """Clean shutdown of the node."""
        self.logger.info("Closing server socket.")
        self.time_to_stop.set()
        self.server_socket.close()
        if self.socket_receive_thread.is_alive():
            self.socket_receive_thread.join(timeout=1)


    

def main(args=None):
    """Main function."""
   
    # Create node
    ls = SocketListener()
    ls.configure()
    
    while True:
        data = ls.get_message_from_socket()
        if data:
            print(data)
        else:
            print("No data received!")

if __name__ == '__main__':
    main()