import awsiot.iotshadow
import rclpy
from rclpy.node import Node
from aqita_cloud.connector import Connector
from aqita_interfaces.srv import MissionStartAck
from aqita_interfaces.msg import ComponentStatus
from aqita_cloud.provisioning import Provisioner
from aqita_cloud.missions import MissionService
import or<PERSON>son
import awsiot

class AqitaCloud(Node):
    """
    ROS2 node with robust MQTT client integration.
    
    This replaces the original PubSub2 class with a more robust implementation
    that uses SQLite-based message buffering and automatic retry capabilities.
    """

    def __init__(self):
        super().__init__('aqita_cloud')
    
        self.declare_parameter('enable_server', False)
        self.declare_parameter('server_port', 8000)
        self.declare_parameter('enable_archives', False)
        self.declare_parameter('archive_duration', 600)
        self.declare_parameter('mqtt_batch_size', 500)
        self.declare_parameter('mqtt_batch_time', 1)

        
        self.logger = self.get_logger()

        self.provisioner = Provisioner(logger=self.logger)
        self.credentials = self.provisioner.provision()
        
        self.connector = Connector(
            logger=self.logger,
            credentials=self.credentials
        )

        self.mission_service = MissionService(
            logger=self.logger,
            credentials=self.credentials,
            connector=self.connector
        )

        self.mission_service.start()        
        self.connector.start()

        self.logger.info("Cloud connector node started ")

        self.connector.set_command_handler(self.command_handler)
        self.connector.set_shadow_handler(self.shadow_handler)
        self.connector.set_shadow_delta_handler(self.shadow_delta_handler)

        # Create the MissionStartAck service server
        self.mission_ack_service = self.create_service(
            MissionStartAck,
            'mission_start_ack',  # Service topic name
            self.mission_service.mission_ack_service_handler
        )

    def command_handler(self, topic: str, payload: bytes):
        """Handle incoming MQTT commands."""
        try:
            data = orjson.loads(payload)
            self.logger.info(f"Received message on topic {topic}: {data}")
            msg_type = data.get('message_type')
            if not msg_type:
                self.logger.warning("No message type detected!")

            if msg_type == 'MISSION_ACK_RESPONSE':
                self.mission_service.handle_mission_ack_from_aws(data)
        
        except Exception as e:
            self.logger.error(f"Error processing MQTT message: {e}")

    def shadow_handler(self, event: awsiot.iotshadow.ShadowUpdatedEvent):
        # TODO: capture the latest shadow with a mission
        # print(event)
        self.mission_service.handle_mission_from_shadow(event.current.state.desired)

    def shadow_delta_handler(self, event: awsiot.iotshadow.ShadowDeltaUpdatedEvent):
        self.mission_service.handle_mission_from_shadow(event.state)

    def destroy_node(self):
        """Clean shutdown of the node."""
        self.logger.info("Shutting down robust MQTT client...")
        if self.connector:
            self.connector.stop()
        if self.mission_service:
            self.mission_service.stop()
        super().destroy_node()



def main(args=None):
    """Main function."""
    # Initialize ROS2
    rclpy.init(args=args)
    
    # Create node
    node = AqitaCloud()
    
    try:
        # Spin the node
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        # Clean shutdown
        node.destroy_node()
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == '__main__':
    main()
