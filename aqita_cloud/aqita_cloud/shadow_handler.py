"""
AWS IoT Shadow Handler for managing shadow operations.

This module provides a dedicated handler for AWS IoT Device Shadow operations:
- Shadow update and delta subscriptions
- Shadow state management
- Event processing and callback handling
"""

import queue
import threading
import time
import logging
import datetime
from typing import Optional, Set, Callable
import concurrent.futures
import uuid

from awscrt import mqtt_request_response, mqtt
from awsiot import iotshadow, ServiceStreamOptions, V2ServiceException
from awsiot.mqtt_connection_builder import mtls_with_pkcs12
from aqita_cloud.provisioning import SHADOW_OUTPUT_CONTAINER
import orjson


class ShadowHandler:
    """
    Handles AWS IoT Device Shadow operations including subscriptions and updates.
    """

    def __init__(
        self,
        mqtt_connection: mqtt.Connection,
        thing_name: str,
        logger: Optional[logging.Logger] = None,
        shadow_name: str = 'aqita_shadow'
    ):
        """
        Initialize the shadow handler.
        
        Args:
            shadow_client: AWS IoT Shadow client instance
            thing_name: Name of the IoT thing
            logger: Optional logger instance
            shadow_name: Name of the shadow (default: 'aqita_shadow')
        """

        rr_options = mqtt_request_response.ClientOptions(
            max_request_response_subscriptions=10,
            max_streaming_subscriptions=10,
            operation_timeout_in_seconds=5,
        )

        self.shadow_client = iotshadow.IotShadowClientV2(mqtt_connection, rr_options)
       

        self.thing_name = thing_name
        self.shadow_name = shadow_name
        self.logger = logger or logging.getLogger(__name__)
        
        # Event management
        self.shutdown_event = threading.Event()
        
        # Subscription management
        self.shadow_subscriptions: Set[Callable] = set()
        self.shadow_delta_subscriptions: Set[Callable] = set()
        
        # Event queues
        self.shadow_updates_received = queue.Queue()
        self.shadow_deltas_received = queue.Queue()
        
        # Processing threads
        self.shadow_update_handler_thread: Optional[threading.Thread] = None
        self.shadow_delta_update_handler_thread: Optional[threading.Thread] = None
        
        # Streaming operations
        self.update_stream: Optional[mqtt_request_response.StreamingOperation] = None
        self.delta_update_stream: Optional[mqtt_request_response.StreamingOperation] = None
        
        # Correlation tracking
        self.correlation_ids = {}

    def start(self):
        """Start shadow handling threads and setup connections."""
        self._setup_shadow_connection()
        
        # Start processing threads
        self.shadow_update_handler_thread = threading.Thread(
            target=self._shadow_update_processor
        )
        self.shadow_update_handler_thread.start()
        
        self.shadow_delta_update_handler_thread = threading.Thread(
            target=self._shadow_deltas_update_processor
        )
        self.shadow_delta_update_handler_thread.start()
        
        self.logger.info("Shadow handler started")

    def stop(self):
        """Stop shadow handling and cleanup resources."""
        self.shutdown_event.set()

        # Wait for threads
        if self.shadow_update_handler_thread and self.shadow_update_handler_thread.is_alive():
            self.shadow_update_handler_thread.join(timeout=2)
        if self.shadow_delta_update_handler_thread and self.shadow_delta_update_handler_thread.is_alive():
            self.shadow_delta_update_handler_thread.join(timeout=2)
        
        self.logger.info("Shadow handler stopped")

    def subscribe_to_shadow(self, callback: Callable):
        """Subscribe to shadow update events."""
        self.shadow_subscriptions.add(callback)

    def subscribe_to_shadow_delta(self, callback: Callable):
        """Subscribe to shadow delta events."""
        self.shadow_delta_subscriptions.add(callback)

    def update_shadow(self, reported=None, desired=None, init=False):
        """Update the shadow state."""
        payload = {}
        
        if reported:
            payload['reported'] = reported
        if desired:
            payload['desired'] = desired

        if reported or desired or init:
            correlation_time = time.time()
            client_token = str(uuid.uuid4())
            self.correlation_ids[client_token] = correlation_time

            ftr = self.shadow_client.update_named_shadow(
                request=iotshadow.UpdateNamedShadowRequest(
                    state=iotshadow.ShadowState(**payload),
                    thing_name=self.thing_name,
                    shadow_name=self.shadow_name,
                    client_token=client_token
                )
            )

            try:
                result: iotshadow.UpdateNamedShadowResponse = ftr.result(timeout=3)
                if result.client_token:
                    self.correlation_ids.pop(result.client_token, None)
                return result
            except Exception as e:
                self.logger.error(f"Failed to update shadow: {e}")
                self.correlation_ids.pop(client_token, None)
                raise

    def _setup_shadow_connection(self):
        """Setup shadow subscriptions and streams."""
        # Create update stream
        self.update_stream = self.shadow_client.create_named_shadow_updated_stream(
            request=iotshadow.NamedShadowUpdatedSubscriptionRequest(
                thing_name=self.thing_name,
                shadow_name=self.shadow_name
            ),
            options=ServiceStreamOptions(
                incoming_event_listener=self.shadow_updates_received.put_nowait,
                subscription_status_listener=self._log_subscription_status,
                deserialization_failure_listener=self._log_deserialization_failure
            )
        )
        self.update_stream.open()

        # Create delta update stream
        self.delta_update_stream = self.shadow_client.create_named_shadow_delta_updated_stream(
            request=iotshadow.NamedShadowDeltaUpdatedSubscriptionRequest(
                thing_name=self.thing_name,
                shadow_name=self.shadow_name
            ),
            options=ServiceStreamOptions(
                incoming_event_listener=self.shadow_deltas_received.put_nowait
            )
        )
        self.delta_update_stream.open()

        # Load initial shadow state if available
        self._load_initial_shadow_state()

    def _load_initial_shadow_state(self):
        """Load initial shadow state from file."""
        try:
            with open(SHADOW_OUTPUT_CONTAINER) as fp:
                data = orjson.loads(fp.read())
                event = iotshadow.ShadowUpdatedEvent(
                    timestamp=datetime.datetime.fromtimestamp(data.get('timestamp')),
                    current=iotshadow.ShadowUpdatedSnapshot(
                        version=data.get('current').get('version'),
                        state=iotshadow.ShadowState(
                            desired=data.get('current').get('state').get('desired'),
                            reported=data.get('current').get('state').get('reported'),
                        ),
                        metadata=iotshadow.ShadowMetadata(
                            desired=data.get('current').get('metadata').get('desired'),
                            reported=data.get('current').get('metadata').get('reported')
                        )
                    ),
                    previous=iotshadow.ShadowUpdatedSnapshot(
                        version=data.get('previous').get('version'),
                        state=iotshadow.ShadowState(
                            desired=data.get('previous').get('state').get('desired'),
                            reported=data.get('previous').get('state').get('reported'),
                        ),
                        metadata=iotshadow.ShadowMetadata(
                            desired=data.get('previous').get('metadata').get('desired'),
                            reported=data.get('previous').get('metadata').get('reported')
                        )
                    )
                )
                self.shadow_updates_received.put(event)
        except Exception as e:
            self.logger.error(f"Could not load initial shadow state: {e}")

    def _shadow_update_processor(self):
        """Process shadow update events."""
        event = None
        waiting_for_latest_shadow = 1
        
        while not self.shutdown_event.is_set():
            do_trigger = False
            try:
                new_event: iotshadow.ShadowUpdatedEvent = self.shadow_updates_received.get(
                    timeout=waiting_for_latest_shadow
                )
                
                if not event or new_event.current.version > event.current.version:
                    event = new_event
            except queue.Empty:
                if event:
                    do_trigger = True
            
            if do_trigger:
                for fn in self.shadow_subscriptions:
                    try:
                        fn(event)
                    except Exception as e:
                        self.logger.error(f"Error in shadow update callback: {e}")
                event = None

    def _shadow_deltas_update_processor(self):
        """Process shadow delta events."""
        event = None
        waiting_for_latest_shadow = 0.5
        
        while not self.shutdown_event.is_set():
            do_trigger = False
            try:
                new_event: iotshadow.ShadowDeltaUpdatedEvent = self.shadow_deltas_received.get(
                    timeout=waiting_for_latest_shadow
                )
                
                if not event or new_event.current.version > event.current.version:
                    event = new_event
            except queue.Empty:
                if event:
                    do_trigger = True
            
            if do_trigger:
                for fn in self.shadow_delta_subscriptions:
                    try:
                        fn(event)
                    except Exception as e:
                        self.logger.error(f"Error in shadow delta callback: {e}")
                event = None

    def _log_subscription_status(self, status):
        """Log subscription status changes."""
        self.logger.debug(f"Shadow subscription status: {status}")

    def _log_deserialization_failure(self, error):
        """Log deserialization failures."""
        self.logger.error(f"Shadow deserialization failure: {error}")