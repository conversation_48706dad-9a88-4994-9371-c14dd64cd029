"""
SQLite-based message buffer for MQTT operations.

This module provides persistent message buffering with:
- Thread-safe database operations
- Batch processing for performance
- Message state management
- Subscription handling
"""

import sqlite3
import logging
import time
import queue
import threading
import glob
import os
from typing import Optional, List, Tuple, Set, Callable, Dict, Any
from threading import Lock


class MqttMessageBuffer:
    """
    SQLite-based message buffer for MQTT operations with batch processing.
    
    Features:
    - Persistent message storage using SQLite
    - Batch insert/update/delete operations
    - Thread-safe operations
    - Message state management
    - Subscription buffering
    """

    def __init__(
        self,
        db_path: str = "mqtt_buffer.db",
        logger: Optional[logging.Logger] = None,
        clear: bool = False,
        insert_batch_size: int = 1000,
        insert_batch_seconds: float = 1.0,
        update_batch_size: int = 1000,
        delete_batch_size: int = 1000,
        vacuum_interval: float = 60.0
    ):
        """
        Initialize the message buffer.

        Args:
            db_path: Path to SQLite database file
            logger: Optional logger instance
            clear: Whether to clear existing database
            insert_batch_size: Maximum batch size for inserts
            insert_batch_seconds: Maximum time to wait before flushing insert batch
            update_batch_size: Maximum batch size for updates
            delete_batch_size: Maximum batch size for deletes
            vacuum_interval: Interval in seconds between database vacuum operations
        """
        self.db_path = db_path
        self.logger = logger or logging.getLogger(__name__)

        # Batch configuration
        self.insert_batch_size = insert_batch_size
        self.insert_batch_seconds = insert_batch_seconds
        self._update_batch_size = update_batch_size
        self._delete_batch_size = delete_batch_size
        self.vacuum_interval = vacuum_interval
        
        # Thread synchronization
        self._update_lock = Lock()
        self._delete_lock = Lock()
        
        # Batch tracking sets
        self._ids_to_delete: Set[int] = set()
        self._ids_to_retry: Set[int] = set()
        
        # Insert queue for batch processing
        self.insert_queue = queue.Queue(maxsize=250)
        
        # Message rate tracking
        self._deleted_messages = 0
        self._last_rate_calculation = time.time()
        self._messages_per_second = 0.0
        self._rate_lock = Lock()
        
        # Callback storage for subscriptions
        self._callbacks: Dict[str, Callable[[str, bytes], None]] = {}

        # Vacuum thread management
        self.vacuum_thread: Optional[threading.Thread] = None
        self.shutdown_event = threading.Event()

        # Clear database if requested
        if clear:
            self.logger.warning("Wiping persistence DB! Do not run with clear=true in production!")
            for file_path in glob.glob(db_path + '*'):
                if os.path.exists(file_path):
                    os.remove(file_path)
                    self.logger.info(f"Removed database file: {file_path}")

        # Database connection (thread-safe with synchronized mode)
        self.db_connection = sqlite3.connect(self.db_path, check_same_thread=False, isolation_level=None)

        # Initialize database schema
        self._init_database()

    def _init_database(self):
        """Initialize SQLite database with required schema."""
        self.db_connection.executescript('''
            CREATE TABLE IF NOT EXISTS message_buffer (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                topic TEXT NOT NULL,
                payload TEXT NOT NULL,
                qos INTEGER NOT NULL DEFAULT 1,
                timestamp REAL NOT NULL,
                is_processing INTEGER NOT NULL DEFAULT 0,
                last_attempt_at REAL
            );
                                         
            CREATE INDEX IF NOT EXISTS idx_timestamp ON message_buffer(timestamp);
            CREATE INDEX IF NOT EXISTS idx_processing ON message_buffer(is_processing);

            CREATE TABLE IF NOT EXISTS subscription_buffer (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                topic TEXT NOT NULL,
                qos INTEGER NOT NULL DEFAULT 1,
                callback_name TEXT NOT NULL,
                timestamp REAL NOT NULL,
                is_processing INTEGER NOT NULL DEFAULT 0,
                last_attempt_at REAL
            );
            DELETE FROM subscription_buffer;
            UPDATE message_buffer SET is_processing = 0;
            UPDATE subscription_buffer SET is_processing = 0;
            CREATE INDEX IF NOT EXISTS idx_sub_timestamp ON subscription_buffer(timestamp);
            CREATE INDEX IF NOT EXISTS idx_sub_processing ON subscription_buffer(is_processing);
        ''')

    def queue_message_for_insert(self, topic: str, payload: str, qos: int) -> bool:
        """
        Queue a message for batch insertion.
        
        Args:
            topic: MQTT topic
            payload: Message payload
            qos: Quality of Service level
            
        Returns:
            bool: True if queued successfully, False otherwise
        """
        current_time = time.time()
        try:
            self.insert_queue.put((topic, payload, qos, current_time))
            return True
        except Exception as e:
            self.logger.error(f"Failed to queue message for DB: {e}")
            return False

    def process_insert_batch(self) -> bool:
        """
        Process a batch of insert operations.
        
        Returns:
            bool: True if batch was processed, False if no messages to process
        """
        batch_start_time = time.time()
        records = []
        
        while (time.time() - batch_start_time < self.insert_batch_seconds and 
               len(records) < self.insert_batch_size and 
               not self.insert_queue.empty()):
            try:
                record = self.insert_queue.get(timeout=1)
                records.append(record)
            except queue.Empty:
                time.sleep(0.01)

        if records:
            cursor = self.db_connection.cursor()
            self.db_connection.execute("BEGIN TRANSACTION;")
            cursor.executemany('''
                INSERT INTO message_buffer
                (topic, payload, qos, timestamp)
                VALUES (?, ?, ?, ?)
            ''', records)
            self.db_connection.execute("COMMIT;")
            
            self.logger.info(f"Inserted {len(records)} MQTT messages in batch")
            return True
        
        return False

    def get_pending_messages(self, limit: int = 100) -> List[Tuple]:
        """
        Get pending messages that are not being processed.
        
        Args:
            limit: Maximum number of messages to retrieve
            
        Returns:
            List of message tuples (id, topic, payload, qos, timestamp)
        """
        try:
            cursor = self.db_connection.cursor()
            cursor.execute('''
                SELECT id, topic, payload, qos, timestamp
                FROM message_buffer
                WHERE is_processing = 0
                ORDER BY timestamp ASC
                LIMIT ?
            ''', (limit,))
            return cursor.fetchall()
        except Exception as e:
            self.logger.error(f"Error getting pending messages: {e}")
            return []

    def mark_messages_processing(self, message_ids: List[int]) -> bool:
        """
        Mark messages as being processed.
        
        Args:
            message_ids: List of message IDs to mark
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            current_time = time.time()
            placeholders = ','.join('?' * len(message_ids))
            self.db_connection.execute(f'''
                UPDATE message_buffer
                SET is_processing = 1, last_attempt_at = ?
                WHERE id IN ({placeholders})
            ''', [current_time] + message_ids)
            return True
        except Exception as e:
            self.logger.error(f"Error marking messages as processing: {e}")
            return False

    def mark_message_for_deletion(self, msg_id: int):
        """Mark a message for deletion in the next batch."""
        with self._delete_lock:
            self._ids_to_delete.add(msg_id)

    def mark_message_for_retry(self, msg_id: int):
        """Mark a message for retry in the next batch."""
        with self._update_lock:
            self._ids_to_retry.add(msg_id)

    def process_delete_batch(self) -> bool:
        """
        Process a batch of delete operations.
        
        Returns:
            bool: True if batch was processed, False if no messages to delete
        """
        with self._delete_lock:
            ids_to_delete = list(self._ids_to_delete)[:self._delete_batch_size]
            if not ids_to_delete:
                return False
            self._ids_to_delete = self._ids_to_delete - set(ids_to_delete)
        
        placeholders = ','.join('?' * len(ids_to_delete))
        try:
            self.db_connection.execute(f'''
                DELETE FROM message_buffer 
                WHERE id IN ({placeholders})
            ''', ids_to_delete)
            
            # Update message rate tracking
            with self._rate_lock:
                self._deleted_messages += len(ids_to_delete)
                current_time = time.time()
                time_diff = current_time - self._last_rate_calculation
                
                if time_diff >= 5.0:
                    self._messages_per_second = self._deleted_messages / time_diff
                    self._deleted_messages = 0
                    self._last_rate_calculation = current_time
            
            return True
        except Exception as e:
            self.logger.error(f"Failed to delete messages from database: {e}")
            with self._delete_lock:
                self._ids_to_delete.update(ids_to_delete)
            return False

    def process_update_batch(self) -> bool:
        """
        Process a batch of update operations (mark for retry).
        
        Returns:
            bool: True if batch was processed, False if no messages to update
        """
        with self._update_lock:
            ids_to_update = list(self._ids_to_retry)[:self._update_batch_size]
            if not ids_to_update:
                return False
            self._ids_to_retry = self._ids_to_retry - set(ids_to_update)
        
        placeholders = ','.join('?' * len(ids_to_update))
        try:
            self.db_connection.execute(f'''
                UPDATE message_buffer
                SET is_processing = 0
                WHERE id IN ({placeholders})
            ''', ids_to_update)
            return True
        except Exception as e:
            self.logger.error(f"Failed to update messages in database: {e}")
            with self._update_lock:
                self._ids_to_retry.update(ids_to_update)
            return False

    def store_subscription(self, topic: str, qos: int, callback_name: str) -> Optional[int]:
        """
        Store a subscription request in the database.
        
        Args:
            topic: MQTT topic to subscribe to
            qos: Quality of Service level
            callback_name: Name of the callback function
            
        Returns:
            int: Subscription ID if successful, None otherwise
        """
        current_time = time.time()
        try:
            cursor = self.db_connection.cursor()
            cursor.execute('''
                INSERT INTO subscription_buffer
                (topic, qos, callback_name, timestamp)
                VALUES (?, ?, ?, ?)
            ''', (topic, qos, callback_name, current_time))
            
            cursor.execute('SELECT last_insert_rowid()')
            subscription_id = cursor.fetchone()[0]
            return subscription_id
        except Exception as e:
            self.logger.error(f"Failed to store subscription: {e}")
            return None

    def get_pending_subscriptions(self, limit: int = 100) -> List[Tuple]:
        """
        Get pending subscriptions that are not being processed.
        
        Args:
            limit: Maximum number of subscriptions to retrieve
            
        Returns:
            List of subscription tuples (id, topic, qos, callback_name)
        """
        try:
            cursor = self.db_connection.cursor()
            cursor.execute('''
                SELECT id, topic, qos, callback_name
                FROM subscription_buffer
                WHERE is_processing = 0
                ORDER BY timestamp ASC
                LIMIT ?
            ''', (limit,))
            return cursor.fetchall()
        except Exception as e:
            self.logger.error(f"Error getting pending subscriptions: {e}")
            return []

    def mark_subscriptions_processing(self, subscription_ids: List[int]) -> bool:
        """
        Mark subscriptions as being processed.
        
        Args:
            subscription_ids: List of subscription IDs to mark
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            current_time = time.time()
            placeholders = ','.join('?' * len(subscription_ids))
            self.db_connection.execute(f'''
                UPDATE subscription_buffer
                SET is_processing = 1, last_attempt_at = ?
                WHERE id IN ({placeholders})
            ''', [current_time] + subscription_ids)
            return True
        except Exception as e:
            self.logger.error(f"Error marking subscriptions as processing: {e}")
            return False

    def delete_subscription(self, subscription_id: int) -> bool:
        """
        Delete a subscription from the buffer.
        
        Args:
            subscription_id: ID of the subscription to delete
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.db_connection.execute('DELETE FROM subscription_buffer WHERE id = ?', (subscription_id,))
            return True
        except Exception as e:
            self.logger.error(f"Error deleting subscription: {e}")
            return False

    def reset_subscription_processing(self, subscription_id: int) -> bool:
        """
        Reset a subscription's processing status for retry.
        
        Args:
            subscription_id: ID of the subscription to reset
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.db_connection.execute('''
                UPDATE subscription_buffer
                SET is_processing = 0
                WHERE id = ?
            ''', (subscription_id,))
            return True
        except Exception as e:
            self.logger.error(f"Error resetting subscription processing: {e}")
            return False

    def get_pending_message_count(self) -> int:
        """
        Get the number of pending messages in the buffer.
        
        Returns:
            int: Number of pending messages
        """
        try:
            cursor = self.db_connection.cursor()
            cursor.execute('SELECT COUNT(id) FROM message_buffer')
            count = cursor.fetchone()[0]
            return count
        except Exception as e:
            self.logger.error(f"Failed to get pending message count: {e}")
            return 0

    def get_message_rate(self) -> float:
        """
        Get the current message processing rate in messages per second.
        
        Returns:
            float: Current message processing rate
        """
        with self._rate_lock:
            return self._messages_per_second

    def vacuum_database(self):
        """Perform database maintenance (VACUUM operation)."""
        try:
            self.db_connection.execute('VACUUM')
            self.logger.debug("Database vacuum completed")
        except Exception as e:
            self.logger.error(f"Failed to vacuum database: {e}")

    def store_callback(self, callback_name: str, callback: Callable[[str, bytes], None]):
        """Store a callback function."""
        self._callbacks[callback_name] = callback

    def get_callback(self, callback_name: str) -> Optional[Callable[[str, bytes], None]]:
        """Retrieve a callback function."""
        return self._callbacks.get(callback_name)

    def remove_callback(self, callback_name: str) -> Optional[Callable[[str, bytes], None]]:
        """Remove and return a callback function."""
        return self._callbacks.pop(callback_name, None)

    def clear_callbacks(self):
        """Clear all stored callbacks."""
        self._callbacks.clear()

    def start_vacuum_thread(self):
        """Start the vacuum thread for periodic database maintenance."""
        if self.vacuum_thread is not None and self.vacuum_thread.is_alive():
            self.logger.warning("Vacuum thread already running")
            return

        self.shutdown_event.clear()
        self.vacuum_thread = threading.Thread(target=self._vacuum_worker, daemon=True)
        self.vacuum_thread.start()
        self.logger.info("Vacuum thread started")

    def stop_vacuum_thread(self):
        """Stop the vacuum thread."""
        if self.vacuum_thread is not None:
            self.shutdown_event.set()
            self.vacuum_thread.join(timeout=2)
            self.vacuum_thread = None
            self.logger.info("Vacuum thread stopped")

    def _vacuum_worker(self):
        """Background worker for periodic database vacuum operations."""
        while not self.shutdown_event.is_set():
            self.shutdown_event.wait(timeout=self.vacuum_interval)
            if not self.shutdown_event.is_set():
                self.vacuum_database()

    def close(self):
        """Close the database connection and stop vacuum thread."""
        # Stop vacuum thread first
        self.stop_vacuum_thread()

        try:
            self.db_connection.close()
        except Exception as e:
            self.logger.error(f"Error closing database connection: {e}")