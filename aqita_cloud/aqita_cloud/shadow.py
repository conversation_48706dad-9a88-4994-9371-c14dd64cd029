import inotify
import inotify.adapters
# import inotify.calls
import inotify.constants
from aqita_cloud.provisioning import (
    SHADOW_INPUT_CONTAINER,
    SHADOW_OUTPUT_CONTAINER
)
import threading
import queue

class ShadowInterface:

    def __init__(self):
        self.notifier = inotify.adapters.Inotify(
            paths=[SHADOW_OUTPUT_CONTAINER]
        )

        self.time_to_stop = threading.Event()
        self.time_to_stop.clear()

        self.notifier_thread = threading.Thread(
            name="ShadowListener",
            target=self._handle_notifications
        )
        
        self.shadow_updates = queue.Queue()

    def _handle_notifications(self):
        
        while True:
            events = self.notifier.event_gen(yield_nones=False, timeout_s=1)
            events = list(events)
            for event in events:
                (_, type_names, _, _) = event

                if inotify.constants.IN_CLOSE_WRITE in type_names:
                    self.shadow_updates.put_nowait()
    
    def start(self):
        self.notifier_thread.start()

    def stop(self):
        self.time_to_stop.set()
    