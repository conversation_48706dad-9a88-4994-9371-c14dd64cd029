import orjson
import time
import logging
from aqita_cloud.iot import RobustMQTTClient, MQTTConfig, ProcessingConfig
import threading
from aqita_cloud.socket_listener import SocketListener
from aqita_cloud.message_batcher import MessageBatcher

class ProvisioningError(RuntimeError):
    """Custom exception for provisioning errors."""
    pass

class Connector:
    """
    ROS2 node with robust MQTT client integration.
    
    This replaces the original PubSub2 class with a more robust implementation
    that uses SQLite-based message buffering and automatic retry capabilities.
    """

    def __init__(self, logger=None, credentials: dict = {}):
        
        self.logger = logger or logging.getLogger()
        
        self.msgs_sent = 0

        self.credentials = credentials

        if not self.credentials:
            self.logger.error("Provisioning failed, cannot start connector.")
            raise ProvisioningError("Provisioning failed, cannot start connector.")
        
        # self.archiver = MetricArchiver()

        self.THING_NAME = self.credentials.get('thing-name', 'DUMMY')
        self.CLIENT_ID = f"{self.THING_NAME}-node"

        mqtt_config = MQTTConfig(
            endpoint=str(self.credentials['iot-endpoint']),
            client_id=self.CLIENT_ID,
            thing_name=self.THING_NAME,
            cert_filepath=str(self.credentials['cert']),
            pri_key_filepath=str(self.credentials['key']),
            ca_filepath=str(self.credentials['root-ca']),
            clean_session=False,
            keep_alive_secs=10,
            ping_timeout_ms=3000,
            protocol_operation_timeout_ms=5000,
            tcp_connect_timeout_ms=3000,
            reconnect_min_timeout_secs=1,
            reconnect_max_timeout_secs=5
        )
        
        self.TENANT = self.credentials.get('tenant', 'DUMMY')
        self.METRIC_TOPIC = f"drone/{self.TENANT}/{self.THING_NAME}/metrics"
        self.BARCODE_TOPIC = f"drone/{self.TENANT}/{self.THING_NAME}/barcode"
        
        # receive commands
        self.COMMAND_TOPIC = f"drone/{self.TENANT}/{self.THING_NAME}/commands"
        # write out events
        self.EVENTS_TOPIC = f"drone/{self.TENANT}/{self.THING_NAME}/events"


        # Configure message processing behavior
        processing_config = ProcessingConfig(
            processing_interval=1.0,  # Process messages every second
            vacuum_interval=60.0     # Vacuum database every minute
        )

        # Create and start robust MQTT client
        self.mqtt_client = RobustMQTTClient(
            mqtt_config=mqtt_config,
            processing_config=processing_config,
            db_path="ros_mqtt_buffer.db",
            logger=self.logger
        )

        if self.credentials['needs-create']:
            self.request_creation()

        self.socket_listener = SocketListener(
            logger=self.logger,
            socket_timeout=1
        )

        # create multirecord messages
        self.batcher = MessageBatcher(
            logger=self.logger,
            max_batch_size=500,
            max_interval=1
        )
        self.batcher.set_callback(
            self.transmit_metrics
        )

        self.time_to_stop = threading.Event()

        self.socket_listener_thread = threading.Thread(
            target=self.receive_from_socket
        )

    def start(self):
        
        self.mqtt_client.start()

        self.logger.info("Starting the socket listener")

        self.socket_listener.configure()
        self.socket_listener_thread.start()

    def set_command_handler(self, some_fn):
        self.logger.info("Subscribing to command topic")
        self.mqtt_client.subscribe(self.COMMAND_TOPIC, some_fn)

    def set_shadow_handler(self, some_fn):
        self.mqtt_client.subscribe_to_shadow(some_fn)

    def set_shadow_delta_handler(self, some_fn):
        self.mqtt_client.subscribe_to_shadow_delta(some_fn)

    def request_creation(self):
        self.logger.info("First run, sending MQTT message to request API object creation")

        payload = {
            'tenant': self.TENANT,
            'thing_name': self.THING_NAME,
            'message_type': 'REQUEST_CREATE',
            'payload': {
                'facility_id': self.credentials['facility-id']
            }
        }

        self.mqtt_client.publish(
            self.EVENTS_TOPIC,
            payload=payload
        )

    def publish(self, topic, payload):
        """Wrapper to publish a message to a given MQTT topic."""
        try:
            message_id = self.mqtt_client.publish(topic, payload)
            return message_id
        except Exception as ex:
            self.logger.error(f"Failed to publish to {topic}: {ex}")
            return None

    def publish_event(self, event_payload={}):
        """Wrapper to publish an event to the EVENTS_TOPIC."""
        return self.publish(self.EVENTS_TOPIC, event_payload)
    
    # def receive_from_queue(self):
    #     while not self.time_to_stop:
    #         data = self.socket_listener.get_message_from_queue(timeout=1)

    #         if data:
    #             print(data)

    def receive_from_socket(self):
        while not self.time_to_stop.is_set():

            try:
                data = self.socket_listener.get_message_from_socket()

                if not data:
                    continue

                ts = time.time()

                data = orjson.loads(data)

                for k, v in data.items():
                    self.batcher.add_message(
                        {
                            "name": str(k),
                            "value": float(v),
                            "timestamp": ts
                        }
                    )
            except Exception as ex:
                self.logger.error(f"Could not get data from socket! : {ex}")


    def transmit_metrics(self, metrics):
        try:
            self.logger.info(f"Queueing batch of {len(metrics)} records")
            aws_message = {
                "type": "DB_MULTIRECORD",
                "tenant": self.TENANT,
                # TODO:FIXME:HACK: this number needs to come from somewhere or be removed!
                "drone_id": 1,
                # "timestamp": ts,
                'values': metrics
            }

            # optionally add to server
            # optionally add to metrics

            message_id = self.mqtt_client.publish(self.METRIC_TOPIC, aws_message)

        except Exception as ex:
            self.logger.error(f"Could not send metrics: {ex}")


    def ros_listener_callback(self, msg):
        """Handle incoming ROS2 detection messages."""
        try:
            # Extract metric value (same as original)
            metric_value = msg.detections[0].bbox.size_x + msg.detections[0].bbox.center.position.x
            
            ts = time.time()
            
            self.batcher.add_message(
                {
                    "name": "foobar",
                    "value": metric_value,
                    "timestamp": ts
                }
            )

            # aws_message = {
            #     "type": "DB_MULTIVALUE",
            #     "tenant": TENANT,
            #     "drone_id": 1,
            #     "timestamp": ts,
            #     'values': {
            #         'foobar': metric_value
            #     }
            # }

            # Publish via robust MQTT client (returns message ID from database)
            # message_id = self.mqtt_client.publish(METRIC_TOPIC, orjson.dumps(aws_message).decode('utf-8'))
            # message_id = self.mqtt_client.publish(METRIC_TOPIC, orjson.dumps(aws_message).decode())
            # message_id = self.mqtt_client.publish(METRIC_TOPIC, aws_message)
            # self.archiver.archive_metric(
            #     aws_message
            # )

            self.msgs_sent += 1
            # self.logger.info(f"Sent metric to topic 'foobar_metric': {metric_value} (Message ID: {message_id})")
            
        except Exception as e:
            self.logger.error(f"Error processing ROS message: {e}")

    def log_status(self):
        """Periodically log connection and buffer status."""
        status = self.mqtt_client.get_connection_status()
        self.logger.info(f"MQTT: Connected={status['connected']}, "
                        f"Pending={status['pending_messages']}, "
                        f"Total={self.msgs_sent}, MPS: {status['rate']}")

    def stop(self):
        """Clean shutdown of the node."""
        self.logger.info("Closing server socket.")
        self.time_to_stop.set()

        self.socket_listener_thread.join(timeout=1)
        self.logger.info("Shutting down robust MQTT client...")
        self.mqtt_client.stop()
