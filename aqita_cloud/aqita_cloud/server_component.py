"""
Server component that provides FastAPI endpoints for the MQTT client.

This module provides a FastAPI server that:
- Serves static files and templates
- Provides SSE endpoint for real-time metrics
- Handles data queries and graph generation
"""

import os
from signal import SIGINT
import sqlite3
import orjson
import asyncio
import threading
import io
import time
import matplotlib.pyplot as plt
from typing import Optional
from queue import Queue
from fastapi import FastAPI, Request, Response, BackgroundTasks
import queue
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from sse_starlette.sse import EventSourceResponse
import uvicorn
import logging
import sys
from datetime import datetime
from collections import deque

import pytz

# class LogHandler(logging.Handler):
#     """Custom log handler that stores logs in a queue for real-time access."""
    
#     def __init__(self, log_queue: Queue, max_logs: int = 1000):
#         super().__init__()
#         self.log_queue = log_queue
#         self.log_buffer = deque(maxlen=max_logs)
        
        
#     def emit(self, record):
#         try:
#             log_entry = {
#                 'timestamp': datetime.fromtimestamp(record.created).isoformat(),
#                 'level': record.levelname,
#                 'message': self.format(record),
#                 'thread': record.threadName or str(record.thread)
#             }
#             self.log_buffer.append(log_entry)
#             self.log_queue.put(log_entry)
#         except Exception:
#             self.handleError(record)

class ServerComponent:
    """
    FastAPI server component that provides web endpoints for the MQTT client.
    
    Features:
    - Serves static files and templates
    - Provides SSE endpoint for real-time metrics
    - Handles data queries and graph generation
    """

    def __init__(
        self,
        data_queue: Queue,
        db_connection: sqlite3.Connection,
        logger: Optional[logging.Logger] = None,
        web_port: int = 8000,
        no_ros=False
    ):
        """
        Initialize the server component.
        
        Args:
            data_queue: Queue for real-time metrics data
            db_connection: SQLite database connection
            logger: Optional logger instance
            web_port: Port for FastAPI web server
        """
        self.data_queue = data_queue
        # Add this for pub-sub
        self._event_clients = set()
        self._event_clients_lock = threading.Lock()
        # Start broadcaster thread
        self.time_to_stop = threading.Event()
        self._broadcaster_thread = threading.Thread(target=self._broadcast_metrics, daemon=True)
        self._broadcaster_thread.start()
        self.db_connection = db_connection
        self.logger = logger or logging.getLogger(__name__)
        self.web_port = web_port
        

        desired_timezone_str = 'UTC'
        desired_timezone_str = 'Europe/Sofia'
        self.desired_tz = pytz.timezone(desired_timezone_str)
        
        # Initialize log queue and handler
        self.log_queue = Queue()
        # self.log_handler = LogHandler(self.log_queue)
        # self.log_handler.setFormatter(logging.Formatter('%(message)s'))
        # self.logger.addHandler(self.log_handler)
        
        # Initialize FastAPI app with optimized settings
        self.app = FastAPI(
            title="Real-time Metrics",
            docs_url=None,  # Disable Swagger UI
            redoc_url=None,  # Disable ReDoc
            openapi_url=None,  # Disable OpenAPI schema
            # default_response_class=JSONResponse,
            debug=False  # Disable debug mode for better performance
        )
        
        # Enable CORS with optimized settings
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
            max_age=3600,  # Cache preflight requests for 1 hour
        )
        
        if not no_ros:
            from ament_index_python.packages import get_package_share_directory
            package_share_directory = get_package_share_directory('aqita_cloud')
        else:
            package_share_directory = os.path.dirname(os.path.abspath(__file__))+'/..'

        # Static files
        static_dir = os.path.join(package_share_directory, "static")
        if os.path.exists(static_dir):
            self.app.mount(
                "/static", 
                StaticFiles(
                    directory=static_dir,
                    follow_symlink=True
                ), 
                name="static")
            self.logger.info(f"Static files mounted from: {static_dir}")
        
        # Templates
        templates_dir = os.path.join(package_share_directory, "templates")
        if os.path.exists(templates_dir):
            self.templates = Jinja2Templates(directory=templates_dir)
            self.logger.info(f"Templates loaded from: {templates_dir}")

        # Register routes
        self._register_routes()

    def _register_routes(self):
        """Register all API routes with FastAPI."""
        # Home routes
        self.app.get("/", response_class=HTMLResponse)(self.home)
        self.app.get("/archive", response_class=HTMLResponse)(self.archive)
        self.app.get("/download_archive/{filename}")(self.download_archive)
        self.app.get("/view_live")(self.view_live)
        # self.app.get("/logs", response_class=HTMLResponse)(self.logs)
        
        # Event routes
        self.app.get("/events")(self.events)
        # self.app.get("/log_current")(self.log_current)
        
        # Data routes
        self.app.post("/query")(self.query)
        # self.app.get("/log_historical")(self.log_historical)
        
        # Graph routes
        self.app.post("/graph")(self.graph)
        
        # Backup route
        self.app.get("/backup_db")(self.backup_db)
        # New endpoint for historical data
        self.app.get("/historical_data")(self.historical_data)

    async def home(self, request: Request):
        """Handle home page request."""
        return self.templates.TemplateResponse(
            "index.html",
            {"request": request}
        )
    
    async def archive(self, request: Request):
        """
        Handle archive page request.
        Lists all parquet files in the archive directory.
        """
        archive_dir = "debug_archives"
            
        files = await asyncio.to_thread(lambda: [f for f in os.listdir(archive_dir) if f.endswith(".parquet")])
        return self.templates.TemplateResponse("archive.html", {"request": request, "files": files})

    async def download_archive(self, filename: str):
        """
        Handle download request for archived parquet files.
        """
        archive_dir = "debug_archives"
        file_path = os.path.join(archive_dir, filename)
        if os.path.exists(file_path):
            return FileResponse(file_path, media_type="application/octet-stream", filename=filename)
        return Response(status_code=404, content="File not found")

    async def view_live(self):
        """Handle live view request."""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        html_path = os.path.join(current_dir, 'live.html')
        return FileResponse(html_path)

    def _broadcast_metrics(self):
        """Background thread: batch and broadcast metrics to all client queues."""
        batch = []
        batch_size = 10000  # You can adjust batch size
        batch_interval = 1  # seconds
        last_batch_time = time.time()
        while not self.time_to_stop.is_set():
            try:
                data = self.data_queue.get(timeout=0.1)
                data['timestamp'] = datetime.fromtimestamp(data['timestamp'], tz=self.desired_tz).isoformat()
                # Send individual metric immediately
                with self._event_clients_lock:
                    for q in list(self._event_clients):
                        try:
                            q.put_nowait({"type": "metric", "data": data})
                        except asyncio.queues.QueueFull:
                            pass

                # Also collect for batch
                batch.append(data)
                now = time.time()
                if len(batch) >= batch_size or (batch and now - last_batch_time >= batch_interval):
                    with self._event_clients_lock:
                        for q in list(self._event_clients):
                            try:
                                q.put_nowait({"type": "batch", "data": list(batch)})
                            except asyncio.queues.QueueFull:
                                pass
                    batch.clear()
                    last_batch_time = now
            except queue.Empty:
                # If time interval passed, send whatever is in batch
                now = time.time()
                if batch and now - last_batch_time >= batch_interval:
                    with self._event_clients_lock:
                        for q in list(self._event_clients):
                            try:
                                q.put_nowait({"type": "batch", "data": list(batch)})
                            except asyncio.queues.QueueFull:
                                pass
                    batch.clear()
                    last_batch_time = now
                continue

    async def events(self):
        """Handle SSE events for multiple clients, sending both individual metrics and batches."""
        client_queue = asyncio.Queue(maxsize=1000)
        with self._event_clients_lock:
            self._event_clients.add(client_queue)

        async def generate_metrics():
            try:
                while not self.time_to_stop.is_set():
                    try:
                        message = await asyncio.wait_for(client_queue.get(), timeout=5)
                        if message["type"] == "metric":
                            yield {
                                "event": "metric",
                                "data": orjson.dumps(message["data"]).decode()
                            }
                        else:  # type == "batch"
                            yield {
                                "event": "metric_batch",
                                "data": orjson.dumps(message["data"]).decode()
                            }
                    except asyncio.TimeoutError:
                        # Heartbeat every 5 seconds
                        yield {
                            "event": "heartbeat",
                            "data": orjson.dumps({"timestamp": time.time()}).decode()
                        }
            finally:
                with self._event_clients_lock:
                    self._event_clients.discard(client_queue)

        return EventSourceResponse(
            generate_metrics(),
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"
            }
        )

    async def query(self, request: Request):
        """Handle data query request."""
        params = await request.json()
        
        # Build query
        query = "SELECT name, value, timestamp FROM message_data WHERE 1=1"
        query_params = []
        
        if 'names' in params:
            placeholders = ','.join(['?'] * len(params['names']))
            query += f" AND name IN ({placeholders})"
            query_params.extend(params['names'])
        
        if 'start_timestamp' in params:
            query += " AND timestamp >= ?"
            query_params.append(params['start_timestamp'])
        
        if 'end_timestamp' in params:
            query += " AND timestamp <= ?"
            query_params.append(params['end_timestamp'])
        
        if 'start_created_at' in params:
            query += " AND created_at >= ?"
            query_params.append(params['start_created_at'])
        
        if 'end_created_at' in params:
            query += " AND created_at <= ?"
            query_params.append(params['end_created_at'])
        
        query += " ORDER BY timestamp"
        
        # Execute query
        cursor = self.db_connection.cursor()
        cursor.execute(query, query_params)
        results = cursor.fetchall()
        
        # Format results
        formatted_results = []
        for name, value, timestamp in results:
            formatted_results.append({
                'name': name,
                'value': value,
                'timestamp': timestamp
            })
        
        return JSONResponse(content=formatted_results)

    async def graph(self, request: Request):
        """Handle graph generation request."""
        params = await request.json()
        
        # Build query
        query = "SELECT name, value, timestamp FROM message_data WHERE 1=1"
        query_params = []
        
        if 'names' in params:
            placeholders = ','.join(['?'] * len(params['names']))
            query += f" AND name IN ({placeholders})"
            query_params.extend(params['names'])
        
        if 'start_timestamp' in params:
            query += " AND timestamp >= ?"
            query_params.append(params['start_timestamp'])
        
        if 'end_timestamp' in params:
            query += " AND timestamp <= ?"
            query_params.append(params['end_timestamp'])
        
        if 'start_created_at' in params:
            query += " AND created_at >= ?"
            query_params.append(params['start_created_at'])
        
        if 'end_created_at' in params:
            query += " AND created_at <= ?"
            query_params.append(params['end_created_at'])
        
        query += " ORDER BY timestamp"
        
        # Execute query
        cursor = self.db_connection.cursor()
        cursor.execute(query, query_params)
        results = cursor.fetchall()
        
        # Create plot
        plt.figure(figsize=(10, 6))
        data_by_name = {}
        
        for name, value, timestamp in results:
            if name not in data_by_name:
                data_by_name[name] = {'timestamps': [], 'values': []}
            data_by_name[name]['timestamps'].append(timestamp)
            data_by_name[name]['values'].append(value)
        
        for name, data in data_by_name.items():
            plt.plot(data['timestamps'], data['values'], label=name)
        
        plt.xlabel('Timestamp')
        plt.ylabel('Value')
        plt.title('Time Series Data')
        plt.legend()
        plt.grid(True)
        
        # Save plot to bytes
        buf = io.BytesIO()
        plt.savefig(buf, format='jpeg')
        buf.seek(0)
        plt.close()
        
        return Response(
            content=buf.getvalue(),
            media_type='image/jpeg',
            headers={
                'Content-Disposition': 'attachment; filename=graph.jpg'
            }
        )

    async def backup_db(self, background_tasks: BackgroundTasks):
        """Handle backup database request."""
        backup_path = "/tmp/backup.db"  # Temporary path for the backup file
        try:
            bck = sqlite3.connect(backup_path)
            self.db_connection.backup(bck, pages=-1)
            bck.close()
            background_tasks.add_task(os.remove, backup_path) # Schedule cleanup after response
            return FileResponse(
                backup_path,
                media_type='application/octet-stream',
                filename='backup.db'
            )
        except Exception as e:
            self.logger.error(f"Database backup failed: {e}", exc_info=True)
            return JSONResponse(content={"message": f"Database backup failed: {e}"}, status_code=500)

    async def historical_data(self, request: Request):
        """Handle historical data request with pagination and filtering."""
        # Get query parameters
        limit = int(request.query_params.get('limit', 10000))  # Default limit
        offset = int(request.query_params.get('offset', 0))
        start_time = request.query_params.get('start_time')
        end_time = request.query_params.get('end_time')
        metrics = request.query_params.get('metrics')  # Comma-separated metric names

        # Build query with filters
        query = "SELECT name, value, timestamp FROM message_data WHERE 1=1"
        params = []

        if start_time:
            query += " AND timestamp >= ?"
            params.append(float(start_time))

        if end_time:
            query += " AND timestamp <= ?"
            params.append(float(end_time))

        if metrics:
            metric_list = [m.strip() for m in metrics.split(',')]
            placeholders = ','.join(['?'] * len(metric_list))
            query += f" AND name IN ({placeholders})"
            params.extend(metric_list)

        query += " ORDER BY timestamp"

        # Add pagination
        query += " LIMIT ? OFFSET ?"
        params.extend([limit, offset])

        # Execute query in a thread to avoid blocking
        def execute_query():
            cursor = self.db_connection.cursor()
            cursor.execute(query, params)
            return cursor.fetchall()

        historical_data = await asyncio.to_thread(execute_query)

        # Format results efficiently using list comprehension
        formatted_historical_data = [
            {
                'name': name,
                'value': value,
                'timestamp': datetime.fromtimestamp(timestamp, tz=self.desired_tz).isoformat()
            }
            for name, value, timestamp in historical_data
        ]

        # Get total count for pagination info
        def get_count():
            count_query = "SELECT COUNT(*) FROM message_data WHERE 1=1"
            count_params = []

            if start_time:
                count_query += " AND timestamp >= ?"
                count_params.append(float(start_time))

            if end_time:
                count_query += " AND timestamp <= ?"
                count_params.append(float(end_time))

            if metrics:
                metric_list = [m.strip() for m in metrics.split(',')]
                placeholders = ','.join(['?'] * len(metric_list))
                count_query += f" AND name IN ({placeholders})"
                count_params.extend(metric_list)

            cursor = self.db_connection.cursor()
            cursor.execute(count_query, count_params)
            return cursor.fetchone()[0]

        total_count = await asyncio.to_thread(get_count)

        return JSONResponse(content={
            'data': formatted_historical_data,
            'pagination': {
                'limit': limit,
                'offset': offset,
                'total': total_count,
                'has_more': offset + len(formatted_historical_data) < total_count
            }
        })

    async def logs(self, request: Request):
        """Handle logs page request."""
        return self.templates.TemplateResponse(
            "logs.html",
            {"request": request}
        )

    async def log_current(self):
        """Handle SSE events for real-time logs."""
        async def generate_logs():
            try:
                while not self.time_to_stop.is_set():
                    try:
                        # Run the blocking queue.get in a separate thread to keep the event loop free
                        log = await asyncio.to_thread(self.log_queue.get, timeout=1)
                        yield {
                            "event": "log",
                            "data": orjson.dumps(log).decode()
                        }
                    except queue.Empty:
                        # Send heartbeat to keep connection alive if no data after timeout
                        yield {
                            "event": "heartbeat",
                            "data": ""
                        }

            except Exception as e:
                self.logger.error(f"Log SSE connection error: {str(e)}", exc_info=True)
                # The connection will be closed automatically when the generator exits
        
        return EventSourceResponse(
            generate_logs(),
        )

    async def log_historical(self):
        """Handle historical logs request."""
        return JSONResponse(content=list(self.log_handler.log_buffer))

    def start(self):
        """Start the FastAPI server."""
        # Set up detailed logging
        logging.getLogger("uvicorn").setLevel(logging.DEBUG)
        logging.getLogger("starlette").setLevel(logging.DEBUG)
        
        # Use the fastest event loop policy
        if sys.platform == 'linux':
            import uvloop
            asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())
        
        config = uvicorn.Config(
            self.app,
            host='0.0.0.0',
            port=self.web_port,
            log_level="debug",
            loop="uvloop" if sys.platform == 'linux' else "asyncio",
            limit_concurrency=None,  # No limit on concurrent connections
            # backlog=2048,  # Increase connection backlog
            workers=4,  # Single worker for SSE
            http="h11",  # Use h11 for better performance
            # ws="websockets",  # Use websockets for better performance
            # timeout_keep_alive=3,  # Keep connections alive longer
            timeout_graceful_shutdown=3,  # Give more time for graceful shutdown
            access_log=True,  # Enable access logging
            use_colors=True,  # Disable colors for better log parsing
        )

        self.uvicorn = uvicorn.Server(config)
        
        
        self.server_thread = threading.Thread(
            target=self.uvicorn.run,
            daemon=True,
            name="ServerThread"
        )
        self.server_thread.start()
        self.logger.info(f"FastAPI server started on port {self.web_port}")

    def stop(self):
        """Stop the FastAPI server."""
        # Note: FastAPI server will be stopped when the thread is terminated
        self.time_to_stop.set()
        self.uvicorn.handle_exit(SIGINT, None)
        self.server_thread.join(timeout=2)
        self.logger.info("FastAPI server stopped")