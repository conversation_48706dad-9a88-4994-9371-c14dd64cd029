from aqita_cloud.connector import Connector
from aqita_interfaces.srv import MissionStartAck
from aqita_cloud.busctl import Systemd, SystemdMissionTimerTemplate, SystemdMissionServiceTemplate
import uuid
import time
import threading
import requests

MISSION_LOCATION = '/workspace/mission.json'

class MissionService:

    def __init__(self, logger, credentials: dict, connector: Connector):
        self.logger = logger
        self.credentials = credentials
        self.connector = connector

        self.ack_id = -1
        self.mission_ack_received = threading.Event()
        self.mission_ack_response = False

        self.busy = threading.Event()
        self.busy.clear()

        self.mission_downloader_thread = threading.Thread(target=self.listen_for_missions)
        self.mission_to_download = None
        self.when_to_schedule = None

        self.time_to_stop = threading.Event()
        self.systemd = Systemd(self.logger)


    def start(self):
        self.mission_downloader_thread.start()

    def stop(self):
        self.time_to_stop.set()
        self.mission_downloader_thread.join()
        

    def listen_for_missions(self):
        while not self.time_to_stop.is_set():
            if self.busy.wait(timeout=1):
                success = self.download_mission_id(self.mission_to_download)
                # TODO:FIXME: test!
                # success = True
                if success:
                    self.schedule_mission()
                else:
                    self.disable_mission()
                # TODO: FIXME: make more precise
                # self.mission_to_download = None
                # self.when_to_schedule = None
                self.busy.clear()

    def disable_mission(self):
        self.systemd.daemon_reload()
        try:
            self.systemd.stop_service(SystemdMissionTimerTemplate.get_unit_name())
            self.systemd.disable_service(SystemdMissionTimerTemplate.get_unit_name())
        except Exception as e:
            self.logger.error(f"Could not stop and disable MissionTimer {e}")

    def schedule_mission(self):
        self.systemd.daemon_reload()

        self.disable_mission()

        sysd_service = SystemdMissionServiceTemplate(self.mission_to_download, self.credentials['thing-name'])
        sysd_timer = SystemdMissionTimerTemplate(self.when_to_schedule)

        sysd_service.render_and_save()
        sysd_timer.render_and_save()

        self.systemd.daemon_reload()
        self.systemd.enable_service(SystemdMissionTimerTemplate.get_unit_name())
        self.systemd.start_service(SystemdMissionTimerTemplate.get_unit_name())



    def download_mission_id(self, mission_id):
        mission_id = 1
        url = f"{self.credentials['api-endpoint']}/api/file/download/{mission_id}"
        try:
            # with requests.get(
            #     url, stream=True,
            #     cert=(self.credentials['cert'], self.credentials['key']),
            #     verify=self.credentials['root-ca']
            # ) as resp:
            with requests.get(url, stream=True, timeout=(5, 10)) as resp:
                if not resp.ok:
                    self.logger.error("Could not download mission!")
                    raise ValueError("Could not download mission")
                with open(MISSION_LOCATION, 'wb') as f:
                    for chunk in resp.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
            self.logger.info(f"Mission {mission_id} downloaded to {MISSION_LOCATION}")
            return True
        except Exception as e:
            self.logger.error(f"Error downloading mission: {e}")
            self.singnal_for_error(
                error_type="MISSION_DOWNLOAD_ERROR",
                error_message="Could not download mission",
                exception=e

            )
            return False
        # finally:
            
            
    def singnal_for_error(self, error_type, error_message="", exception=None):
        if not error_type and type(error_type) != str:
            raise Exception("ERROR MUST HAVE TYPE")
        
        self.connector.publish_event(
                {
                    "message_type": error_type,
                    "payload": {
                        "ex": str(exception),
                        "details": error_message
                    }
                }
            )

    def mission_ack_service_handler(self, request: MissionStartAck.Request, response: MissionStartAck.Response):
        """
        Publishes an ACK request with a unique ID in the events topic and waits for it to be received in the command topic
        """
        self.logger.info(f"MissionStartAck service called with request: {request}")
        self.mission_ack_answer = None

        ack_id = str(uuid.uuid4())

        ack_payload = {
            'ack_id': ack_id,
            'drone_id': request.drone_id,
            'mission_id': request.mission_id
        }

        self.connector.publish_event(
            {
                'message_type': "MISSION_ACK_REQUEST",
                'payload': ack_payload
            }
        )

        self.ack_id = ack_id
        responce_received = self.mission_ack_received.wait(timeout=3)
        response.ack = responce_received and self.mission_ack_response

        return response
    

    def handle_mission_ack_from_aws(self, data: dict):
        ack_id = data.get('payload', {}).get('ack_id')
        response = data.get('payload', {}).get('response', False)
        if ack_id == self.ack_id:
            # allow the mission!
            self.mission_ack_response = response
            self.mission_ack_received.set()


    def handle_mission_from_shadow(self, shadow_data):

        mission_payload = shadow_data.get('mission')
        when_payload = shadow_data.get('when')

        if mission_payload == self.mission_to_download and self.when_to_schedule == when_payload:
            self.logger.warning("Same mission already loaded!")
            return


        while self.busy.is_set():
            self.logger.warning("New mission requested while already downloading mission, waiting it out!")
            time.sleep(1)

        self.mission_to_download = mission_payload
        self.when_to_schedule = when_payload
        if not self.when_to_schedule:
            self.singnal_for_error(
                error_type="MALFORMED_MISSION_SHADOW",
                error_message="No when field in OnCalendar format"
            )
            return
        
        if not self.mission_to_download:
            self.singnal_for_error(
                error_type="MALFORMED_MISSION_SHADOW",
                error_message="No mission id"
            )
            return
        self.busy.set()

    