import time
import logging
import threading

class MessageBatcher:
    """
    Batches messages up to a maximum batch size or a maximum time interval, whichever comes first.
    """

    def __init__(self, max_batch_size=500, max_interval=1.0, logger=None):
        self.max_batch_size = max_batch_size
        self.max_interval = max_interval
        self.logger = logger or logging.getLogger(__name__)
        self._lock = threading.Lock()
        self._messages = []
        self._last_flush = time.time()
        self._timer = None
        self._callback = None

    def set_callback(self, callback):
        """
        Set a callback to be called with the batch when it is ready.
        The callback should accept a single argument: the list of messages.
        """
        self._callback = callback

    def add_message(self, message):
        """
        Add a message to the batcher. If the batch is full or the interval has passed,
        the batch is flushed and the callback is called.
        """
        with self._lock:
            self._messages.append(message)
            if len(self._messages) == 1:
                # Start timer on first message
                self._start_timer()
            if len(self._messages) >= self.max_batch_size:
                self._flush_locked()

    def _start_timer(self):
        if self._timer is not None:
            self._timer.cancel()
        self._timer = threading.Timer(self.max_interval, self._timer_flush)
        self._timer.daemon = True
        self._timer.start()

    def _timer_flush(self):
        with self._lock:
            self._flush_locked()

    def _flush_locked(self):
        if not self._messages:
            return
        batch = self._messages
        self._messages = []
        self._last_flush = time.time()
        if self._timer is not None:
            self._timer.cancel()
            self._timer = None
        if self._callback:
            try:
                self._callback(batch)
            except Exception as e:
                self.logger.error(f"Error in batch callback: {e}")

    def flush(self):
        """
        Manually flush the current batch.
        """
        with self._lock:
            self._flush_locked()

    def stop(self):
        """
        Stop the batcher and cancel any running timer.
        """
        with self._lock:
            if self._timer is not None:
                self._timer.cancel()
                self._timer = None
            self._flush_locked()