import subprocess
import logging
import os

SYSTEMD_UNIT_DIRECTORY = '/systemd'
INSTALL_TEMPLATE_DIR = "/workspace/install/aqita_cloud/share/aqita_cloud/systemd_templates"


def write_with_sudo(fname, content:str):
    proc = subprocess.run(
        ['sudo', 'tee', fname],
        input=content.encode(),
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
    )

    if proc.returncode != 0:
        print(f"Error: {proc.stderr.decode()}")
        raise ResourceWarning("Could not sudo write a file")

class SystemdMissionTimerTemplate:

    def __init__(self, time_component: str):
        if os.path.isdir(INSTALL_TEMPLATE_DIR):
            TIMER_TEMPLATE_LOCATION = f'{INSTALL_TEMPLATE_DIR}/mission_start.timer.template'
        else:
            TIMER_TEMPLATE_LOCATION = '/workspace/aqita_cloud/systemd_templates/mission_start.timer.template'
        
        with open(TIMER_TEMPLATE_LOCATION, 'r') as fp:
            self.timer = fp.read()

        self.timer = self.timer.replace("{{when}}", time_component)

    def render_and_save(self):
        TIMER_LOCATION = f"{SYSTEMD_UNIT_DIRECTORY}/mission_start.timer"

        write_with_sudo(TIMER_LOCATION, self.timer)

        # with open(TIMER_LOCATION, 'w') as fp:
        #     fp.write(self.timer)

    @staticmethod
    def get_unit_name():
        return "mission_start.timer"


class SystemdMissionServiceTemplate:

    def __init__(self, mission_id, drone_id):
        if os.path.isdir(INSTALL_TEMPLATE_DIR):
            SERVICE_TEMPLATE_LOCATION = f'{INSTALL_TEMPLATE_DIR}/aqita_scheduler.service.template'
        else:
            SERVICE_TEMPLATE_LOCATION = '/workspace/aqita_cloud/systemd_templates/aqita_scheduler.service.template'
        with open(SERVICE_TEMPLATE_LOCATION, 'r') as fp:
            self.service = fp.read()

        self.service = self.service.replace("{{mission_id}}", str(mission_id))
        self.service = self.service.replace("{{drone_id}}", drone_id)

    def render_and_save(self):
        SERVICE_LOCATION = f"{SYSTEMD_UNIT_DIRECTORY}/aqita_scheduler.service"

        write_with_sudo(SERVICE_LOCATION, self.service)
        # with open(SERVICE_LOCATION, 'w') as fp:
        #     fp.write(self.service)

    @staticmethod
    def get_unit_name():
        return "aqita_scheduler.service"


class Systemd:

    def __init__(self, logger=None):
        self.logger = logger or logging.getLogger(__name__)

    def _systemd_unit_to_dbus_path(self, unit_name: str) -> str:
        return unit_name.replace('_', '_5f').replace('.', '_2e')

    def enable_service(self, service_name):
        # busctl call org.freedesktop.systemd1 \
        #     /org/freedesktop/systemd1 \
        #     org.freedesktop.systemd1.Manager \
        #     EnableUnitFiles asbb \
        #     1 'aws_client.service' false true
        result = subprocess.run(
            [
                'sudo', 'busctl', 'call', 'org.freedesktop.systemd1',
                '/org/freedesktop/systemd1',
                'org.freedesktop.systemd1.Manager',
                'EnableUnitFiles', 'asbb',
                '1', service_name, 'false', 'true'
            ],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        if result.returncode != 0:
            self.logger.error(f"Failed to enable {service_name}: {result.stderr.decode()}")
            return False

        self.logger.info(f"Successfully enabled {service_name}")
        return True

    def start_service(self, service_name):
        # busctl call org.freedesktop.systemd1 \
        #     /org/freedesktop/systemd1 \
        #     org.freedesktop.systemd1.Manager \
        #     StartUnit ss \
        #     'aws_client.service' 'replace'
        result = subprocess.run(
            [
                'sudo', 'busctl', 'call', 'org.freedesktop.systemd1',
                '/org/freedesktop/systemd1',
                'org.freedesktop.systemd1.Manager',
                'StartUnit', 'ss',
                service_name, 'replace'
            ],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        if result.returncode != 0:
            self.logger.error(f"Failed to start {service_name}: {result.stderr.decode()}")
            return False

        self.logger.info(f"Successfully started {service_name}")
        return True
    
    def service_statuses(self, service_name):
        # both state and sub-state

        DBUS_SVC_UNIT = self._systemd_unit_to_dbus_path(service_name)

        # busctl get-property org.freedesktop.systemd1 \
        #     /org/freedesktop/systemd1/unit/aws_5fclient_2service \
        #     org.freedesktop.systemd1.Unit ActiveState
        # busctl get-property org.freedesktop.systemd1 \
        #     /org/freedesktop/systemd1/unit/aws_5fclient_2service \
        #     org.freedesktop.systemd1.Unit SubState
        
        result = subprocess.run(
            [
                'sudo', 'busctl', 'get-property', 'org.freedesktop.systemd1',
                f'/org/freedesktop/systemd1/unit/{DBUS_SVC_UNIT}',
                'org.freedesktop.systemd1.Unit', 'ActiveState'
            ],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        if result.returncode != 0:
            self.logger.error(f"Failed to get ActiveState for {service_name}: {result.stderr.decode()}")
            return ['failed', 'failed']

        active_state = result.stdout.decode().strip().split('"')[1]

        result = subprocess.run(
            [
                'sudo', 'busctl', 'get-property', 'org.freedesktop.systemd1',
                f'/org/freedesktop/systemd1/unit/{DBUS_SVC_UNIT}',
                'org.freedesktop.systemd1.Unit', 'SubState'
            ],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        if result.returncode != 0:
            self.logger.error(f"Failed to get SubState for {service_name}: {result.stderr.decode()}")
            return ['failed', 'failed']

        sub_state = result.stdout.decode().strip().split('"')[1]

        return [active_state, sub_state]

    def stop_service(self, service_name):
        # busctl call org.freedesktop.systemd1 \
        #     /org/freedesktop/systemd1 \
        #     org.freedesktop.systemd1.Manager \
        #     StopUnit ss \
        #     'aws_client.service' 'replace'
        result = subprocess.run(
            [
                'sudo', 'busctl', 'call', 'org.freedesktop.systemd1',
                '/org/freedesktop/systemd1',
                'org.freedesktop.systemd1.Manager',
                'StopUnit', 'ss',
                service_name, 'replace'
            ],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        if result.returncode != 0:
            self.logger.error(f"Failed to stop {service_name}: {result.stderr.decode()}")
            return False

        self.logger.info(f"Successfully stopped {service_name}")
        return True
    
    def daemon_reload(self):
        # busctl call org.freedesktop.systemd1 \
        #     /org/freedesktop/systemd1 \
        #     org.freedesktop.systemd1.Manager Reload
        result = subprocess.run(
            [
                'sudo', 'busctl', '--expect-reply=false', 'call', 'org.freedesktop.systemd1',
                '/org/freedesktop/systemd1',
                'org.freedesktop.systemd1.Manager',
                'Reload'
            ],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        if result.returncode != 0:
            self.logger.error(f"Failed to reload systemd: {result.stderr.decode()}")
            return False

        self.logger.info("Successfully reloaded systemd")
        return True
    
    def disable_service(self, service_name):
        # busctl call org.freedesktop.systemd1 \
        #     /org/freedesktop/systemd1 \
        #     org.freedesktop.systemd1.Manager \
        #     DisableUnitFiles asb \
        #     'aws_client.service' false
        result = subprocess.run(
            [
                'sudo', 'busctl', 'call', 'org.freedesktop.systemd1',
                '/org/freedesktop/systemd1',
                'org.freedesktop.systemd1.Manager',
                'DisableUnitFiles', 'asb', '1',
                service_name, 'false'
            ],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        if result.returncode != 0:
            self.logger.error(f"Failed to disable {service_name}: {result.stderr.decode()}")
            return False

        self.logger.info(f"Successfully disabled {service_name}")
        return True