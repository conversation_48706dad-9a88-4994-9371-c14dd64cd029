#!/bin/bash

echo "Trying to configure AWS DEVICE CLIENT......"

export DEVICE_CLIENT_PATH="/home/<USER>/aws_device_client"
export DEVICE_CLIENT_DIRECTORY="/home/<USER>/.aws-iot-device-client"
export CLAIM_CERT_LOCATION="$DEVICE_CLIENT_DIRECTORY/claim/certificate.pem"
export CLAIM_PKEY_LOCATION="$DEVICE_CLIENT_DIRECTORY/claim/private.key"
export ROOT_CA_LOCATION="$DEVICE_CLIENT_DIRECTORY/CA/root-CA.crt"
export SDK_LOG_FILE="$DEVICE_CLIENT_DIRECTORY/logs/sdk.log"
export CLIENT_LOG_FILE="$DEVICE_CLIENT_DIRECTORY/logs/client.log"
export CLIENT_CONFIG_FILE="$DEVICE_CLIENT_DIRECTORY/client_config.json"
export CLIENT_RUNTIME_CONFIG="$DEVICE_CLIENT_DIRECTORY/aws-iot-device-client-runtime.conf"
export ACTIVE_CERT="$DEVICE_CLIENT_DIRECTORY/keys/active-certificate.pem.crt"
export ACTIVE_PKEY="$DEVICE_CLIENT_DIRECTORY/keys/active-private.pem.key"


chmod 745 $DEVICE_CLIENT_DIRECTORY
chmod 640 $CLIENT_CONFIG_FILE
chmod 700 $DEVICE_CLIENT_DIRECTORY/claim
chmod 644 $CLAIM_CERT_LOCATION 
chmod 600 $CLAIM_PKEY_LOCATION 
chmod 644 $ROOT_CA_LOCATION 
chmod 745 $DEVICE_CLIENT_DIRECTORY/logs 
chmod 700 $DEVICE_CLIENT_DIRECTORY/CA 
chmod 700 $DEVICE_CLIENT_PATH

sudo systemctl start aws_client