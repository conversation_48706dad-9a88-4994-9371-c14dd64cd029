import requests
import json


from aqita_cloud.provisioning import (
    CLIENT_DRONE_CONFIG,
    CLIENT_RUNTIME_CONFIG,
    ACTIVE_CERT,
    ACTIVE_PKEY,
    ROOT_CA_LOCATION
)


with open(CLIENT_RUNTIME_CONFIG) as fp:
    runtime_cfg = json.load(fp)

with open(CLIENT_DRONE_CONFIG) as fp:
    drone_cfg = json.load(fp)


response = requests.get(
    url=f"{drone_cfg['api-endpoint']}",
    cert=(ACTIVE_CERT, ACTIVE_PKEY),
    verify=ROOT_CA_LOCATION,
)

print("Status:", response.status_code)
print("Response:", response.text)

