from setuptools import find_packages, setup
from glob import glob
import os

package_name = 'aqita_cloud'

setup(
    name=package_name,
    version='0.0.1',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages', ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        ('share/' + package_name + '/static/css', glob('static/css/*', recursive=True)),
        ('share/' + package_name + '/static/js', glob('static/js/*', recursive=True)),
        ('share/' + package_name + '/static/fonts', glob('static/fonts/*', recursive=True)),       
        ('share/' + package_name + '/templates', glob('templates/*')),
        ('share/' + package_name + '/systemd_templates', glob('systemd_templates/**')),

        # TODO:FIXME: ????
        ('lib/' + package_name, glob(f'{package_name}/*.py')),
        
        ('share/' + package_name, glob(f'{package_name}/*.py')),

        (os.path.join('share', package_name, 'launch'), glob(os.path.join('launch', '*launch.[pxy][yma]*')))
    ],
    install_requires=[
        'setuptools',
        'orjson',
        'awsiotsdk',
        'fastapi',
        'Jinja2',
        'requests',
        'sse-starlette',
        'uvloop',
        'uvicorn',
        'websockets',
        'h11',
    ],
    zip_safe=True,
    maintainer='Todor Markov',
    maintainer_email='<EMAIL>',
    description='AWS Cloud Connector package',
    license='PROPRIETARY',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'esc_server = aqita_cloud.local_graph:main',
            'synthetic_server = aqita_cloud.synthetic:main',
            'cloud_connector = aqita_cloud.main:main',
        ],
    },
)
