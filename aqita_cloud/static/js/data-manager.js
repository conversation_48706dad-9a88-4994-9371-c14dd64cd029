/**
 * Circular Buffer for efficient data storage
 */
class CircularBuffer {
    constructor(capacity) {
        this.capacity = capacity;
        this.buffer = new Array(capacity);
        this.head = 0;
        this.tail = 0;
        this.size = 0;
    }
    
    push(item) {
        this.buffer[this.tail] = item;
        this.tail = (this.tail + 1) % this.capacity;
        
        if (this.size < this.capacity) {
            this.size++;
        } else {
            this.head = (this.head + 1) % this.capacity;
        }
    }
    
    peek() {
        if (this.size === 0) return null;
        return this.buffer[this.head];
    }
    
    isFull() {
        return this.size === this.capacity;
    }
    
    toArray() {
        if (this.size === 0) return [];
        
        const result = [];
        for (let i = 0; i < this.size; i++) {
            const index = (this.head + i) % this.capacity;
            result.push(this.buffer[index]);
        }
        
        return result;
    }
}

/**
 * Efficient Data Manager Class
 * Handles large datasets with circular buffers and time indexing
 */
class DataManager {
    constructor(maxPointsPerMetric = 10000) {
        this.maxPointsPerMetric = maxPointsPerMetric;
        this.metrics = new Map(); // Map<metricName, CircularBuffer>
        this.activeMetrics = new Set();
        this.receivedMetrics = new Set();
        this.totalDataPoints = 0;
        
        // Time indexing for efficient range queries
        this.timeIndex = new Map(); // Map<timestamp, Set<metricNames>>
        this.minTimestamp = null;
        this.maxTimestamp = null;
    }
    
    addDataPoint(metricName, timestamp, value) {
        if (!this.metrics.has(metricName)) {
            this.metrics.set(metricName, new CircularBuffer(this.maxPointsPerMetric));
            this.receivedMetrics.add(metricName);
            this.activeMetrics.add(metricName);
        }
        
        const buffer = this.metrics.get(metricName);
        const point = { x: timestamp, y: value };
        
        // Remove old point from time index if buffer is full
        if (buffer.isFull()) {
            const oldPoint = buffer.peek();
            if (oldPoint) {
                this.removeFromTimeIndex(oldPoint.x.getTime(), metricName);
            }
        }
        
        buffer.push(point);
        this.addToTimeIndex(timestamp.getTime(), metricName);
        
        // Update timestamp bounds
        const timestampMs = timestamp.getTime();
        if (this.minTimestamp === null || timestampMs < this.minTimestamp) {
            this.minTimestamp = timestampMs;
        }
        if (this.maxTimestamp === null || timestampMs > this.maxTimestamp) {
            this.maxTimestamp = timestampMs;
        }
        
        this.totalDataPoints++;
    }
    
    addToTimeIndex(timestamp, metricName) {
        if (!this.timeIndex.has(timestamp)) {
            this.timeIndex.set(timestamp, new Set());
        }
        this.timeIndex.get(timestamp).add(metricName);
    }
    
    removeFromTimeIndex(timestamp, metricName) {
        if (this.timeIndex.has(timestamp)) {
            const metrics = this.timeIndex.get(timestamp);
            metrics.delete(metricName);
            if (metrics.size === 0) {
                this.timeIndex.delete(timestamp);
            }
        }
    }
    
    getDataInRange(metricName, startTime, endTime) {
        if (!this.metrics.has(metricName)) {
            return [];
        }

        const buffer = this.metrics.get(metricName);
        const data = buffer.toArray();

        if (data.length === 0) {
            return [];
        }

        // Use binary search for efficient range queries
        const startIdx = this.binarySearchStart(data, startTime);
        const endIdx = this.binarySearchEnd(data, endTime);

        const result = [];
        for (let i = startIdx; i <= endIdx && i < data.length; i++) {
            if (data[i] && data[i].x && data[i].y !== undefined) {
                result.push(data[i]);
            }
        }

        return result;
    }

    // Get all data for a metric (for scrolling back to any time period)
    getAllData(metricName) {
        if (!this.metrics.has(metricName)) {
            return [];
        }

        const buffer = this.metrics.get(metricName);
        return buffer.toArray().filter(point => point && point.x && point.y !== undefined);
    }
    
    binarySearchStart(data, targetTime) {
        if (data.length === 0) return 0;

        let left = 0;
        let right = data.length - 1;
        let result = data.length;

        while (left <= right) {
            const mid = Math.floor((left + right) / 2);
            const point = data[mid];

            if (!point || !point.x) {
                left = mid + 1;
                continue;
            }

            if (point.x >= targetTime) {
                result = mid;
                right = mid - 1;
            } else {
                left = mid + 1;
            }
        }

        return Math.max(0, Math.min(result, data.length - 1));
    }

    binarySearchEnd(data, targetTime) {
        if (data.length === 0) return -1;

        let left = 0;
        let right = data.length - 1;
        let result = -1;

        while (left <= right) {
            const mid = Math.floor((left + right) / 2);
            const point = data[mid];

            if (!point || !point.x) {
                right = mid - 1;
                continue;
            }

            if (point.x <= targetTime) {
                result = mid;
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }

        return Math.max(-1, Math.min(result, data.length - 1));
    }
    
    getAllMetrics() {
        return Array.from(this.receivedMetrics);
    }
    
    getActiveMetrics() {
        return Array.from(this.activeMetrics);
    }
    
    setMetricActive(metricName, active) {
        if (active) {
            this.activeMetrics.add(metricName);
        } else {
            this.activeMetrics.delete(metricName);
        }
    }
    
    getTotalDataPoints() {
        return this.totalDataPoints;
    }
    
    getTimeBounds() {
        return {
            min: this.minTimestamp ? new Date(this.minTimestamp) : null,
            max: this.maxTimestamp ? new Date(this.maxTimestamp) : null
        };
    }
}

// Export for use in other modules
window.DataManager = DataManager;
window.CircularBuffer = CircularBuffer;
