HOW 2 DEBUG:

python -m venv venv
source venv/bin/activate
pip install -r requirements.txt



HOW 2 BUILD :

cd /workspace

rm -rf /workspace/build && rm -rf /workspace/install

colcon build --symlink-install --packages-select aqita_interfaces
colcon build --symlink-install --packages-select aqita_cloud

source /workspace/install/setup.bash

rosdep update

rosdep install --from-paths /workspace/aqita_cloud/package.xml --ignore-src -r -y

cd /workspace/aqita_cloud 

pip3 install -e .

cd /workspace

ros2 run aqita_cloud synthetic_server
ros2 run aqita_cloud esc_server


ros2 launch aqita_cloud simulation.launch.py max_messages:=1000 rate:=10
ros2 launch aqita_cloud connect.launch.py

sudo journalctl -f -u cloud.service
sudo journalctl -f -u healthmond.service




cmake ../ -DEXCLUDE_DD=ON -DEXCLUDE_JOBS=ON -DEXCLUDE_SAMPLES=ON -DEXCLUDE_PUBSUB=ON -DEXCLUDE_SHADOW=ON -DEXCLUDE_CONFIG_SHADOW=ON -DEXCLUDE_SAMPLE_SHADOW=ON -DCMAKE_BUILD_TYPE=Release
cmake ../ -DEXCLUDE_DD=ON -DEXCLUDE_JOBS=ON -DEXCLUDE_PUBSUB=ON -DEXCLUDE_CONFIG_SHADOW=ON -DCMAKE_BUILD_TYPE=Release



cmake --build . --target aws-iot-device-client -- j16


sudo busctl call org.freedesktop.systemd1 /org/freedesktop/systemd1 org.freedesktop.systemd1.Manager StartUnit ss "ssh.service" "replace"

-v /run/dbus/system_bus_socket:/run/dbus/system_bus_socket
-v $HOME/.aws-iot-device-client:/aws_client_settings_dir
-v $HOME/aws_device_client:/aws-iot-device-client

DOCKER_ARGS+=("-v /run/dbus/system_bus_socket:/run/dbus/system_bus_socket")
DOCKER_ARGS+=("-v $HOME/.aws-iot-device-client:/aws_client_settings_dir")
DOCKER_ARGS+=("-v $HOME/aws_device_client:/aws-iot-device-client")


sudo journalctl -f -u aws_client.service