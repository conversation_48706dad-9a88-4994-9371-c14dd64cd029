launch:
- arg:
    name: "processing_interval"
    default: "0.5"
- arg:
    name: "enable_server"
    default: "false"
- arg:
    name: "server_port"
    default: "8000"
- arg:
    name: "enable_archives"
    default: "false"
- arg:
    name: "archive_duration"
    default: "600"

  
- node:
    pkg: "aqita_cloud"
    exec: "cloud_connector"
    name: "cloud_connector"
    param:
    -
        name: "processing_interval"
        value: "$(var processing_interval)"
    -
        name: "enable_server"
        value: "$(var enable_server)"
    -
        name: "server_port"
        value: "$(var server_port)"
    -
        name: "enable_archives"
        value: "$(var enable_archives)"
    -
        name: "archive_duration"
        value: "$(var archive_duration)"