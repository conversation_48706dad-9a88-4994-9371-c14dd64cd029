from launch import LaunchDescription
from launch_ros.actions import Node
from launch.substitutions import LaunchConfiguration
from launch.actions import DeclareLaunchArgument

def generate_launch_description():
    return LaunchDescription([
        # Declare launch arguments
        DeclareLaunchArgument('max_messages', default_value='10'),
        DeclareLaunchArgument('rate', default_value='10'),
        DeclareLaunchArgument('num_values', default_value='10'),
        
        Node(
            package='aqita_cloud',
            executable='synthetic_server',
            name='synthetic_server',
            parameters=[{
                'max_messages': LaunchConfiguration('max_messages', default=10),
                'rate': LaunchConfiguration('rate', default=10),
                'num_values': LaunchConfiguration('num_values', default=10),
            }],
            output='screen'
        )
    ])