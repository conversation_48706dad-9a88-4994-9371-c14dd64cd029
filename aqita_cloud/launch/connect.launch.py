from launch import LaunchDescription
from launch_ros.actions import Node
from launch.substitutions import LaunchConfiguration
from launch.actions import DeclareLaunchArgument

def generate_launch_description():
    return LaunchDescription([
        # Declare launch arguments
        DeclareLaunchArgument('processing_interval', default_value='0.5'),
        DeclareLaunchArgument('enable_server', default_value='false'),
        DeclareLaunchArgument('server_port', default_value='8000'),
        DeclareLaunchArgument('enable_archives', default_value='false'),
        DeclareLaunchArgument('archive_duration', default_value='600'),
        
        Node(
            package='aqita_cloud',
            executable='cloud_connector',
            name='cloud_connector',
            parameters=[{
                'processing_interval': LaunchConfiguration('processing_interval'),
                'enable_server': LaunchConfiguration('enable_server'),
                'enable_archives': LaunchConfiguration('enable_archives'),
                'archive_duration': LaunchConfiguration('archive_duration'),
            }],
            output='screen'
        )
    ])