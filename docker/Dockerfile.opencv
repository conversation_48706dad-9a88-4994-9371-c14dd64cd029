ARG PLATFORM=arm64
ARG BASE_IMAGE=aqita-arm:base

FROM ${BASE_IMAGE}

# Install OpenCV build dependencies with additional optimizations from the guide
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    unzip \
    pkg-config \
    libgtk2.0-dev \
    libgtk-3-dev \
    libcanberra-gtk* \
    # Image I/O libraries
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    # Video/Audio Libraries
    libavcodec-dev \
    libavformat-dev \
    libswscale-dev \
    libxvidcore-dev \
    libx264-dev \
    libxine2-dev \
    libv4l-dev \
    qv4l2 \
    libpostproc-dev \
    libvorbis-dev \
    libfaac-dev \
    libmp3lame-dev \
    libtheora-dev \
    libopus-dev \
    # Audio codec libraries
    libopencore-amrnb-dev \
    libopencore-amrwb-dev \
    # Camera interfaces
    libdc1394-dev \
    # Math optimization libraries
    libopenblas-dev \
    libatlas-base-dev \
    libeigen3-dev \
    # Additional optimization libraries
    libprotobuf-dev \
    protobuf-compiler \
    libgoogle-glog-dev \
    libgflags-dev \
    libgphoto2-dev \
    libhdf5-dev \
    # CUDA and cuDNN
    libcudnn9-dev-cuda-12 \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Create symbolic link for video device header as recommended in the guide
RUN ln -sf /usr/include/libv4l1-videodev.h /usr/include/linux/videodev.h 2>/dev/null || true

# Set architecture-specific variables
ARG PLATFORM=arm64
RUN if [ "${PLATFORM}" = "arm64" ]; then \
        echo "Building for ARM64 architecture"; \
        export CUDA_ARCH_BIN="8.6 8.7"; \
        export CMAKE_ARCH_FLAGS="-DENABLE_NEON=ON"; \
        export CUDNN_INCLUDE_DIR="/usr/include"; \
        export CUDNN_LIBRARY="/usr/lib/aarch64-linux-gnu/libcudnn.so"; \
        export TARGET_ARCH="aarch64-linux-gnu"; \
    else \
        echo "Building for x86_64 architecture"; \
        export CUDA_ARCH_BIN="7.0 7.5 8.0 8.6 8.9 9.0"; \
        export CMAKE_ARCH_FLAGS=""; \
        export CUDNN_INCLUDE_DIR="/usr/include"; \
        export CUDNN_LIBRARY="/usr/lib/x86_64-linux-gnu/libcudnn.so"; \
        export TARGET_ARCH="x86_64-linux-gnu"; \
    fi; \
    echo "CUDA_ARCH_BIN=${CUDA_ARCH_BIN}" > /tmp/build_vars.sh; \
    echo "CMAKE_ARCH_FLAGS=${CMAKE_ARCH_FLAGS}" >> /tmp/build_vars.sh; \
    echo "CUDNN_INCLUDE_DIR=${CUDNN_INCLUDE_DIR}" >> /tmp/build_vars.sh; \
    echo "CUDNN_LIBRARY=${CUDNN_LIBRARY}" >> /tmp/build_vars.sh; \
    echo "TARGET_ARCH=${TARGET_ARCH}" >> /tmp/build_vars.sh

# Manual installation of cuDNN header files if the package didn't provide them
RUN . /tmp/build_vars.sh && \
    if [ ! -f "/usr/include/cudnn.h" ] && [ ! -f "/usr/include/${TARGET_ARCH}/cudnn.h" ]; then \
    echo "cuDNN headers not found, downloading manually..." && \
    mkdir -p /tmp/cudnn && \
    cd /tmp/cudnn && \
    if [ "${PLATFORM}" = "arm64" ]; then \
        wget https://developer.nvidia.com/compute/cudnn/secure/8.9.7/local_installers/12.x/cudnn-linux-aarch64-8.9.7.29_cuda12-archive.tar.xz -O cudnn.tar.xz || \
        wget https://developer.nvidia.com/compute/cudnn/secure/8.8.1/local_installers/12.0/cudnn-linux-aarch64-8.8.1.3_cuda12-archive.tar.xz -O cudnn.tar.xz; \
    else \
        wget https://developer.nvidia.com/compute/cudnn/secure/8.9.7/local_installers/12.x/cudnn-linux-x86_64-8.9.7.29_cuda12-archive.tar.xz -O cudnn.tar.xz || \
        wget https://developer.nvidia.com/compute/cudnn/secure/8.8.1/local_installers/12.0/cudnn-linux-x86_64-8.8.1.3_cuda12-archive.tar.xz -O cudnn.tar.xz; \
    fi && \
    tar -xf cudnn.tar.xz && \
    mkdir -p /usr/include && \
    cp -a cudnn-*-archive/include/cudnn*.h /usr/include/ && \
    rm -rf /tmp/cudnn; \
    fi

# Create directory for OpenCV
WORKDIR /tmp/opencv_build

# Download and extract the latest OpenCV version and contrib
RUN wget -O opencv.zip https://github.com/opencv/opencv/archive/refs/tags/4.10.0.zip && \
    wget -O opencv_contrib.zip https://github.com/opencv/opencv_contrib/archive/refs/tags/4.10.0.zip && \
    unzip opencv.zip && \
    unzip opencv_contrib.zip && \
    mv opencv-4.10.0 opencv && \
    mv opencv_contrib-4.10.0 opencv_contrib

# Create a symbolic link for cuDNN library if needed
RUN . /tmp/build_vars.sh && \
    if [ ! -f "/usr/lib/${TARGET_ARCH}/libcudnn.so" ] && [ -f "/usr/lib/${TARGET_ARCH}/libcudnn.so.9" ]; then \
    ln -s /usr/lib/${TARGET_ARCH}/libcudnn.so.9 /usr/lib/${TARGET_ARCH}/libcudnn.so; \
    fi

# Build OpenCV with CUDA and cuDNN support
WORKDIR /tmp/opencv_build/opencv
RUN mkdir -p build && cd build && \
    . /tmp/build_vars.sh && \
    # Configure with CMake
    cmake -D CMAKE_BUILD_TYPE=RELEASE \
    -D CMAKE_INSTALL_PREFIX=/usr/local \
    -D WITH_TBB=ON \
    -D ENABLE_FAST_MATH=1 \
    -D CUDA_FAST_MATH=1 \
    -D WITH_CUBLAS=1 \
    -D WITH_CUDA=ON \
    -D BUILD_opencv_cudacodec=OFF \
    -D WITH_CUDNN=ON \
    -D CUDNN_INCLUDE_DIR=${CUDNN_INCLUDE_DIR} \
    -D CUDNN_LIBRARY=${CUDNN_LIBRARY} \
    -D OPENCV_DNN_CUDA=ON \
    -D CUDA_ARCH_BIN="${CUDA_ARCH_BIN}" \
    ${CMAKE_ARCH_FLAGS} \
    -D WITH_V4L=ON \
    -D WITH_QT=OFF \
    -D WITH_OPENGL=ON \
    -D WITH_GSTREAMER=ON \
    -D OPENCV_GENERATE_PKGCONFIG=ON \
    -D OPENCV_PC_FILE_NAME=opencv.pc \
    -D OPENCV_ENABLE_NONFREE=ON \
    -D BUILD_opencv_sfm=OFF \
    -D OPENCV_EXTRA_MODULES_PATH=/tmp/opencv_build/opencv_contrib/modules \
    -D INSTALL_PYTHON_EXAMPLES=OFF \
    -D INSTALL_C_EXAMPLES=OFF \
    -D BUILD_EXAMPLES=OFF \
    -D PYTHON_EXECUTABLE=$(which python3) \
    -D PYTHON3_EXECUTABLE=$(which python3) \
    -D PYTHON3_INCLUDE_DIR=$(python3 -c "from distutils.sysconfig import get_python_inc; print(get_python_inc())") \
    -D PYTHON3_PACKAGES_PATH=$(python3 -c "from distutils.sysconfig import get_python_lib; print(get_python_lib())") \
    .. && \
    # Save cmake output for debugging
    cmake -L | grep -i cuda > /tmp/cuda_config.txt && \
    cmake -L | grep -i cudnn >> /tmp/cuda_config.txt && \
    # Compile with all available cores
    make -j$(nproc) && \
    make install && \
    ldconfig && echo "/usr/local/lib/cmake/opencv4" > /tmp/opencv_dir.txt

# Set environment variables to ensure OpenCV 4.10 is used
ENV OpenCV_DIR=/usr/local/lib/cmake/opencv4
ENV PATH=/usr/local/bin:${PATH}
ENV LD_LIBRARY_PATH=/usr/local/lib:${LD_LIBRARY_PATH}

# Create OpenCV pkg-config file if it doesn't exist
RUN if [ ! -f "/usr/local/lib/pkgconfig/opencv4.pc" ]; then \
    echo "Creating pkg-config file for OpenCV" && \
    echo "prefix=/usr/local\n\
exec_prefix=\${prefix}\n\
libdir=\${prefix}/lib\n\
includedir=\${prefix}/include/opencv4\n\
\n\
Name: OpenCV\n\
Description: Open Source Computer Vision Library\n\
Version: 4.10.0\n\
Libs: -L\${libdir} -lopencv_core -lopencv_imgproc -lopencv_imgcodecs\n\
Libs.private: \${libdir}/opencv4/3rdparty\n\
Cflags: -I\${includedir}\n" > /usr/local/lib/pkgconfig/opencv4.pc; \
    fi

# Cleanup to reduce image size
RUN rm -rf /tmp/opencv_build

# Keep the original entrypoint from the base image
CMD ["bash"]