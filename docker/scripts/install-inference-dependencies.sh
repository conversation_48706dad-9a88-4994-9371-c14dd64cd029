#!/bin/bash
set -euo pipefail

echo "===== Inference dependencies installation script ====="
echo "This script will install all dependencies for running object detection and inference using TensorRT"

# --------- ENVIRONMENT VALIDATION ---------

# Check if running in Docker
if [[ ! -f /.dockerenv ]] && ! grep -qE '/docker/|/lxc/' /proc/1/cgroup 2>/dev/null; then
    echo "❌ Error: This script must be run inside a Docker container."
    exit 1
fi

# Check if ${WORKSPACE_DIR}/src/ exists
if [[ ! -d "${WORKSPACE_DIR}/src/" ]]; then
    echo "❌ Error: Directory ${WORKSPACE_DIR}/src/ does not exist."
    exit 1
fi

cd ${WORKSPACE_DIR}/src/
start_time=$(date +%s)
echo "✅ Environment validated. Starting setup..."

# --------- FUNCTIONS ---------

# Clone and replace directory
clone_repo() {
    local dir="$1"
    local repo="$2"
    local branch="$3"

    echo "---------------------------------------------"
    echo "Setting up: $dir"
    step_start=$(date +%s)

    if [[ -d "$dir" ]]; then
        echo "Removing existing directory: $dir"
        rm -rf "$dir"
    fi

    echo "Cloning $repo (branch: $branch)"
    git clone -b "$branch" "$repo"

    step_end=$(date +%s)
    echo "✔️ $dir setup completed in $((step_end - step_start)) seconds."
}

# Build and time function
build_and_time() {
    echo "---------------------------------------------"
    echo "Building: $*"
    step_start=$(date +%s)

    colcon build --symlink-install "$@"

    step_end=$(date +%s)
    echo "✔️ Build finished in $((step_end - step_start)) seconds."
    sleep 2
}

# --------- PACKAGE SETUP ---------

# Isaac ROS packages
# The isaac_ros_object_detection package is not needed for now. Uncomment the next line if this package is needed at a later stage
# clone_repo "isaac_ros_object_detection" "https://github.com/NVIDIA-ISAAC-ROS/isaac_ros_object_detection.git" "release-3.2"
clone_repo "isaac_ros_dnn_inference" "https://github.com/NVIDIA-ISAAC-ROS/isaac_ros_dnn_inference.git" "release-3.2"

# RQT packages
clone_repo "rqt" "https://github.com/ros-visualization/rqt.git" "humble"
clone_repo "rqt_image_view" "https://github.com/ros-visualization/rqt_image_view.git" "humble-devel"

# Image processing packages
clone_repo "image_common" "https://github.com/ros-perception/image_common.git" "humble"
clone_repo "image_pipeline" "https://github.com/ros-perception/image_pipeline.git" "humble"

echo -e "\nAll repositories cloned successfully.\n"

# --------- PACKAGE BUILD ---------

cd $WORKSPACE_DIR


build_and_time --packages-select isaac_ros_tensor_proc  --cmake-args -DBUILD_TESTING=OFF
build_and_time --packages-select isaac_ros_dnn_image_encoder    --cmake-args -DBUILD_TESTING=OFF
build_and_time --packages-select gxf_isaac_tensor_rt    --cmake-args -DBUILD_TESTING=OFF
build_and_time --packages-select isaac_ros_tensor_rt    --cmake-args -DBUILD_TESTING=OFF
build_and_time --packages-select-regex rqt*    --cmake-args -DBUILD_TESTING=OFF
build_and_time --packages-up-to camera_info_manager    --cmake-args -DBUILD_TESTING=OFF
build_and_time --packages-up-to image_publisher    --cmake-args -DBUILD_TESTING=OFF

end_time=$(date +%s)
echo "============================================="
echo "All packages have been build successfully."
echo "✅ Script completed in $((end_time - start_time)) seconds. Restart the container or manually source the environment (source ${WORKSPACE_DIR}/install/setup.bash) for the changes to take effect!"

