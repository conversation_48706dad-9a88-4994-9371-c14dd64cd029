#!/bin/bash
set -e  # Exit on first error

echo "===== MAVLink and MAVROS Installation Script for Jetson with ROS 2 Humble ====="
echo "This script will install MAVLink and MAVROS from source"

# Install dependencies
echo "Installing dependencies..."
sudo apt-get update
sudo apt-get install -y python3-pip python3-dev python3-setuptools python3-wheel
sudo apt-get install -y python3-future python3-lxml
sudo apt-get install -y ros-humble-rosidl-generator-c ros-humble-rosidl-generator-cpp
sudo apt-get install -y ros-humble-rosidl-typesupport-c ros-humble-rosidl-typesupport-cpp
sudo apt-get install -y ros-humble-rosidl-typesupport-introspection-c ros-humble-rosidl-typesupport-introspection-cpp
sudo apt-get install -y ros-humble-fastrtps ros-humble-fastcdr
sudo apt-get install -y ros-humble-rmw-fastrtps-cpp
sudo apt-get install -y ros-humble-geographic-msgs
sudo apt-get install -y google-mock

# Install colcon build tools
echo "Installing colcon build tools..."
sudo apt-get install -y python3-colcon-common-extensions

python3 -m pip install -U future lxml
python3 -m pip install rospkg 

# Setup workspace
echo "Setting up workspace..."
mkdir -p ~/mavros_ws/src
cd ~/mavros_ws/src

# Clone MAVLink
echo "Cloning MAVLink repository..."
git clone --recursive https://github.com/mavlink/mavlink.git --branch master
cd mavlink
git checkout master

# Generate C library
echo "Generating MAVLink C library..."
python3 -m pip install -U future lxml
mkdir -p build
cd build
cmake ..
sudo make install
cd ../..

# Clone MAVROS for ROS 2
echo "Cloning MAVROS repository (ROS 2 branch)..."
git clone https://github.com/mavlink/mavros.git --branch ros2
cd mavros
git checkout ros2

# Download GeographicLib datasets (required for MAVROS)
echo "Installing GeographicLib datasets..."
sudo ./mavros/scripts/install_geographiclib_datasets.sh

# Return to workspace root
cd ~/mavros_ws

# Install dependencies using rosdep
echo "Resolving dependencies with rosdep..."
sudo rosdep init || echo "rosdep already initialized"
rosdep update
rosdep install --from-paths src --ignore-src -r -y

# Build the workspace with merge install
echo "Building the workspace with colcon using merge install..."
source /opt/ros/humble/setup.bash
colcon build --merge-install --cmake-args -DBUILD_TESTING=OFF

# Setup environment variables
echo "Setting up environment variables..."
# echo "source ~/mavros_ws/install/setup.bash" >> ~/.bashrc
source ~/mavros_ws/install/setup.bash

echo "===== Installation Complete ====="
echo "MAVROS and MAVLink have been installed from source for ROS 2 Humble with merge install"
echo "Workspace location: ~/mavros_ws"