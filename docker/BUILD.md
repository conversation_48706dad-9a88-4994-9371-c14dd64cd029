# AQITA - Development Environment

## Overview

The AQITA development environment provides:

- Ubuntu 22.04 base
- ROS2 Humble
- OpenCV 4.10.0 with CUDA acceleration
- Various SLAM implementations (RTAB-Map, ORB-SLAM3)
- NVIDIA Isaac ROS packages for visual SLAM and 3D reconstruction
- Depth camera support (RealSense)
- Full GPU acceleration for deep learning and computer vision

*X86 build is not supported yet!*

## Prerequisites

- jetpack 6.2 (for jetson devices)
- Ubuntu 22.04
- NVIDIA GPU with CUDA support
- Docker installed
- NVIDIA Container Toolkit installed

### Installing Dependencies

```bash
# Install Docker
sudo apt-get update
sudo apt-get install -y docker.io

# Install NVIDIA Container Toolkit
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
sudo apt-get update
sudo apt-get install -y nvidia-container-toolkit

# Configure Docker to use the NVIDIA runtime
sudo tee /etc/docker/daemon.json <<EOF
{
    "runtimes": {
        "nvidia": {
            "path": "nvidia-container-runtime",
            "runtimeArgs": []
        }
    },
    "default-runtime": "nvidia"
}
EOF

# Restart Docker daemon
sudo systemctl restart docker

# Test NVIDIA runtime
sudo docker run --rm --gpus all nvidia/cuda:12.6.1-base-ubuntu22.04 nvidia-smi
```

### Docker Post-Setup (User Groups)

To use Docker without sudo, add your user to the docker group:

```bash
# Create the docker group if it doesn't exist
sudo groupadd docker

# Add your user to the docker group
sudo usermod -aG docker $USER

# Apply the new group membership
newgrp docker

# Verify that you can run docker commands without sudo
docker run hello-world
```

Note: You may need to log out and log back in for the group changes to take effect.

### GitHub Container Registry Setup

To access container images from GitHub Container Registry (ghcr.io):

```bash
# Generate a Personal Access Token (PAT) from GitHub
# Go to GitHub → Settings → Developer settings → Personal access tokens
# Create a token with the 'read:packages' scope

# Login to GitHub Container Registry
echo YOUR_PAT | docker login ghcr.io -u YOUR_GITHUB_USERNAME --password-stdin

# Verify login
docker pull ghcr.io/your-org/your-repo/image-name:tag
```

## Building the Container

To build the Docker container, simply run:

```bash
./docker/build_docker.sh
```

This will:
- Create the necessary directory structure
- Generate placeholder files if needed
- Build all Docker images in sequence
- Tag the images appropriately

The build process may take 1-2 hours depending on your hardware.

## Running the Container

To start the container:

```bash
./run_docker.sh
```

This will:
- Start the container with GPU support
- Mount the current directory to `/workspace`
- Create a data directory for persistent storage
- Set up X11 forwarding for GUI applications
- Create a user in the container that matches your host user ID
- Configure the environment for ROS2

If the container is already running, this script will open a new terminal in the existing container, connecting to the same session.

### Multiple Terminals

You can run the script multiple times to open additional terminals in the same container environment:

```bash
# First terminal
./run_docker.sh

# In another terminal window (connects to the same container)
./run_docker.sh
```

This is useful for running multiple ROS nodes simultaneously.

## Building Isaac ROS Packages

Inside the container, you can build NVIDIA Isaac ROS packages including nvblox:

```bash
./build_isaac_ros.sh
```

This will:
- Create a workspace at `thirdparty/isaac_ros_ws`
- Download necessary repositories
- Build the packages with CUDA support

## Key Components and Dependencies

### Base Image
- Ubuntu 22.04
- CUDA 12.6.1
- CuDNN 9
- Python 3.10
- Development tools (CMake, git, compilers)
- cuDSS (CUDA Data Structure Sharing)
- CV-CUDA
- Triton Server

### OpenCV Image
- OpenCV 4.10.0 with CUDA support
- Full GPU acceleration
- FFmpeg (custom build without GPL components)

### ROS2 Image
- ROS2 Humble with all core packages
- Navigation2 stack
- MoveIt2 and MoveIt Task Constructor
- RViz2 and visualization tools
- Vision packages and image processing
- Fixed packages with NVIDIA-specific improvements

### Core Packages
- RealSense SDK with udev rules
- RTAB-Map with CUDA acceleration
- ORB-SLAM3
- g2o (General Graph Optimization)
- Pangolin visualization library
- CudaSift for GPU-accelerated feature detection
- TensorFlow and PyTorch integration

### Isaac ROS Packages (optional build)
- isaac_ros_common - Core utilities and tools
- isaac_ros_nvblox - 3D reconstruction and mapping
- isaac_ros_nitros - NVIDIA tensor node format for ROS
- isaac_ros_compression - Hardware-accelerated image compression
- isaac_ros_image_pipeline - GPU-accelerated image processing
- isaac_ros_visual_slam - Visual SLAM implementation

## Using ROS2 in the Container

ROS2 Humble is automatically sourced when you enter the container. You can run ROS2 commands directly:

```bash
# List available packages
ros2 pkg list

# Run RTAB-Map with RealSense camera
ros2 launch rtabmap_launch rtabmap.launch.py
```

## Sharing Data

The `/workspace` directory in the container is mounted to the directory where you run the script from. Files created or modified in this directory will persist after the container is stopped.

## Troubleshooting

### GPU Issues
- Ensure your NVIDIA drivers are up to date (recommended driver version: 550.54.14 or newer)
- Verify NVIDIA Container Toolkit is properly installed and configured as the default runtime
- Check that your GPU is detected with `nvidia-smi`
- If using Wayland, you may need additional configuration for X11 forwarding

### X11 Forwarding Issues
- Make sure you have allowed X11 connection with `xhost +local:docker`
- Check if your DISPLAY environment variable is properly set
- If using Wayland, consider installing xwayland or using `--env=DISPLAY=$DISPLAY` explicitly
- If GUI applications crash with segmentation faults, try disabling hardware acceleration with `--env=QT_X11_NO_MITSHM=1`

### Docker Permission Issues
- If you get "permission denied" errors when running Docker commands:
  - Verify that you added your user to the docker group using `groups` command
  - Try logging out and logging back in
  - If issues persist, restart your system for group changes to fully apply
  - As a temporary solution, you can use `sudo` before Docker commands

### GitHub Container Registry Issues
- Ensure your Personal Access Token has appropriate permissions
- Check your token expiration date
- Use `docker login ghcr.io` to authenticate manually if needed
- If pulling fails, check if the repository is private and if you have access

### Build Failures
- Check the build log file created during the build process
- Ensure you have sufficient disk space (at least 30GB recommended)
- Make sure your internet connection is stable for downloading packages
- If a specific stage fails, you can modify the build script to continue from that stage

### RealSense Camera Issues
- Check that the udev rules are properly installed on the host
- Make sure the USB port has sufficient bandwidth (USB 3.0 or higher)
- Try connecting the camera before starting the container
- Use `rs-enumerate-devices` inside the container to verify camera detection

### Memory Issues During Build
- The build process requires significant memory, especially for OpenCV compilation
- If OOM (Out of Memory) errors occur, consider adding swap space:
  ```bash
  sudo fallocate -l 16G /swapfile
  sudo chmod 600 /swapfile
  sudo mkswap /swapfile
  sudo swapon /swapfile
  ```


  
# RealSense Camera Setup

## Setting Up RealSense Cameras

The AQITA development environment includes support for Intel RealSense depth cameras (D435, D435i, D455). To properly set up the cameras to work with the Docker container:

### Run Docker container

When running the container using `run_docker.sh`, the script will automatically:
- Mount the necessary udev rules
- Mount the hotplug script
- Configure the proper permissions for camera access

### Troubleshooting

#### Device Access Issues

If you encounter errors accessing the RealSense cameras from within the container, try:

1. **Unplug and replug the cameras**: This is often necessary to trigger the udev rules and properly set up device permissions.

2. Verify that the cameras are detected on the host:
   ```bash
   ls -l /dev/video*  # Should show devices with plugdev group
   ```

3. Check the RealSense hotplug log:
   ```bash
   cat /tmp/docker_usb.log
   ```

4. Make sure the plugdev group has the right permissions:
   ```bash
   ls -l /dev/video* | grep plugdev
   ```

#### Known Issues

- Sometimes cameras need to be physically unplugged and plugged back in after starting the container to be properly recognized.
- If you start the container before connecting the cameras, you might need to restart the container after connecting them.
- If the host system suspends or hibernates, you may need to reconnect the cameras afterward.

#### Using Multiple RealSense Cameras

When using multiple RealSense cameras:

1. Connect one camera at a time, waiting a few seconds between connections
2. Check that each camera is properly recognized before connecting the next one
3. Use the RealSense serial numbers in your configuration to distinguish between cameras