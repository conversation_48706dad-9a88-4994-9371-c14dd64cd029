import cv2
import sys
import subprocess
import os
import numpy as np
import time

# Check OpenCV version and CUDA info
print(f"OpenCV version: {cv2.__version__}")
print(f"Python version: {sys.version}")
print(f"CUDA devices count: {cv2.cuda.getCudaEnabledDeviceCount()}")

# Get detailed OpenCV build information
build_info = cv2.getBuildInformation()
print("\n--- OpenCV Build Information (CUDA sections) ---")
for line in build_info.split('\n'):
    if any(x in line for x in ['CUDA', 'cuda', 'GPU', 'Nvidia']):
        print(line)

# Try to get GPU information
try:
    nvidia_smi = subprocess.check_output("nvidia-smi", shell=True).decode()
    print("\n--- NVIDIA-SMI Output ---")
    print(nvidia_smi)
except:
    print("\nNVIDIA-SMI not available")

# Check CUDA version
try:
    nvcc_version = subprocess.check_output("nvcc --version", shell=True).decode()
    print("\n--- NVCC Version ---")
    print(nvcc_version)
except:
    print("\nNVCC not available")

# Check library paths
print("\n--- Library Paths ---")
for path in os.environ.get('LD_LIBRARY_PATH', '').split(':'):
    if path:
        print(path)


def test_basic_cuda():
    img = np.random.randint(0, 255, (2000, 2000), dtype=np.uint8)

    # Test uploading and downloading
    start = time.time()
    gpu_mat = cv2.cuda_GpuMat()
    gpu_mat.upload(img)
    result = gpu_mat.download()
    end = time.time()

    print(f"Basic CUDA memory transfer: {end - start:.4f} seconds")
    print(f"Image shape matches: {img.shape == result.shape}")
    print(f"Content matches: {np.array_equal(img, result)}")

    return "CUDA memory operations working correctly"


if __name__ == "__main__":
    if cv2.cuda.getCudaEnabledDeviceCount() > 0:
        result = test_basic_cuda()
        print(result)
    else:
        print("No CUDA devices available")
