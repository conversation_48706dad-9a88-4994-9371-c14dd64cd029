# Copyright (c) 2023-2024, NVIDIA CORPORATION.  All rights reserved.
#
# NVIDIA CORPORATION and its licensors retain all intellectual property
# and proprietary rights in and to this software, related documentation
# and any modifications thereto.  Any use, reproduction, disclosure or
# distribution of this software and related documentation without an express
# license agreement from NVIDIA CORPORATION is strictly prohibited.

ARG BASE_IMAGE
FROM $BASE_IMAGE

# Store list of packages (must be first)
RUN mkdir -p /opt/nvidia/isaac_ros_dev_base && dpkg-query -W | sort > /opt/nvidia/isaac_ros_dev_base/ros2_humble-start-packages.csv

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive \
    SHELL=/bin/bash \
    LANG=en_US.UTF-8 \
    ROS_PYTHON_VERSION=3 \
    ROS_DISTRO=humble \
    ROS_ROOT=/opt/ros/humble \
    RMW_IMPLEMENTATION=rmw_fastrtps_cpp \
    PYTHONWARNINGS=ignore:::setuptools.command.install,ignore:::setuptools.command.easy_install,ignore:::pkg_resources,ignore:::setuptools.command.develop

# Setup locale and add ROS 2 apt repository
RUN --mount=type=cache,target=/var/cache/apt \
    locale-gen en_US en_US.UTF-8 && update-locale LC_ALL=en_US.UTF-8 LANG=en_US.UTF-8 && \
    echo "Warning: Using the PYTHONWARNINGS environment variable to silence setup.py and easy_install deprecation warnings caused by colcon" && \
    curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key -o /usr/share/keyrings/ros-archive-keyring.gpg && \
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] http://packages.ros.org/ros2/ubuntu $(source /etc/os-release && echo $UBUNTU_CODENAME) main" | tee /etc/apt/sources.list.d/ros2.list > /dev/null

# Install ROS fundamentals, Python packages, and ROS 2 Humble packages
RUN --mount=type=cache,target=/var/cache/apt \
    apt-get update && apt-get install -y \
    devscripts \
    dh-make \
    fakeroot \
    libxtensor-dev \
    python3-bloom \
    python3-colcon-common-extensions \
    python3-pip \
    python3-pybind11 \
    python3-pytest-cov \
    python3-rosdep \
    python3-rosinstall-generator \
    python3-vcstool \
    liblz4-dev \
    python3-setuptools \
    quilt \
    ros-humble-ros-base \
    ros-humble-angles \
    ros-humble-behaviortree-cpp-v3 \
    ros-humble-bondcpp \
    ros-humble-diagnostics \
    ros-humble-diagnostic-aggregator \
    ros-humble-diagnostic-updater \
    ros-humble-ament-cmake \
    ros-humble-ament-cmake-gtest \
    ros-humble-joint-state-broadcaster \
    ros-humble-joint-state-publisher \
    ros-humble-robot-state-publisher \
    ros-humble-tf2-eigen \
    ros-humble-tf2-geometry-msgs \
    ros-humble-tf2-ros \
    ros-humble-launch-param-builder \
    ros-humble-pluginlib \
    ros-humble-py-binding-tools \
    ros-humble-ros2-control \
    ros-humble-control-msgs \
    ros-humble-controller-manager \
    ros-humble-topic-based-ros2-control \
    ros-humble-geometric-shapes \
    ros-humble-ur-description \
    ros-humble-ur-msgs \
    ros-humble-srdfdom \
    ros-humble-xacro \
    ros-humble-example-interfaces \
    ros-humble-foxglove-bridge \
    ros-humble-launch-xml \
    ros-humble-launch-yaml \
    ros-humble-launch-testing \
    ros-humble-launch-testing-ament-cmake \
    ros-humble-resource-retriever \
    ros-humble-rmw-cyclonedds-cpp \
    ros-humble-rmw-fastrtps-cpp \
    ros-humble-rosbag2 \
    ros-humble-rosbag2-compression-zstd \
    ros-humble-rosbag2-cpp \
    ros-humble-rosbag2-py \
    ros-humble-rosbag2-storage-mcap \
    ros-humble-rosbridge-suite \
    ros-humble-rosx-introspection \
    ros-humble-rviz2 \
    ros-humble-rviz-common \
    ros-humble-rviz-default-plugins \
    ros-humble-rviz-visual-tools \
    ros-humble-sensor-msgs \
    ros-humble-vision-msgs \
    ros-humble-vision-msgs-rviz-plugins \
    ros-humble-ompl \
    ros-humble-magic-enum \
    ros-humble-geographic-msgs \
    libxsimd-dev && \
    # Install Python packages 
    python3 -m pip install --upgrade pip && \
    python3 -m pip install -U \
    flake8-blind-except \
    flake8-builtins \
    flake8-class-newline \
    flake8-comprehensions \
    flake8-deprecated \
    flake8-docstrings \
    flake8-import-order \
    flake8-quotes \
    matplotlib \
    pandas \
    rosbags \
    transforms3d \
    paho-mqtt==1.6.1

# Setup rosdep and extra rosdeps
COPY rosdep/extra_rosdeps.yaml /etc/ros/rosdep/sources.list.d/nvidia-isaac.yaml
RUN --mount=type=cache,target=/var/cache/apt \
    rosdep init && \
    echo "yaml file:///etc/ros/rosdep/sources.list.d/nvidia-isaac.yaml" | tee /etc/ros/rosdep/sources.list.d/00-nvidia-isaac.list && \
    sed -i 's|gbpdistro https://raw.githubusercontent.com/ros/rosdistro/master/releases/fuerte.yaml fuerte||g' /etc/ros/rosdep/sources.list.d/20-default.list && \
    rosdep update

# Copy all patches at once
COPY patches/rclcpp-disable-tests.patch /tmp/

# Install negotiated from source and patched rclcpp package
RUN --mount=type=cache,target=/var/cache/apt \
    # Install negotiated from source
    mkdir -p ${ROS_ROOT}/src && cd ${ROS_ROOT}/src && \
    git clone https://github.com/osrf/negotiated && cd negotiated && git checkout master && \
    source ${ROS_ROOT}/setup.bash && \
    cd negotiated_interfaces && bloom-generate rosdebian && fakeroot debian/rules binary && \
    cd ../ && apt-get install -y ./*.deb && rm ./*.deb && \
    cd negotiated && bloom-generate rosdebian && fakeroot debian/rules binary && \
    cd ../ && apt-get install -y ./*.deb && rm ./*.deb && \
    # Install patched rclcpp package
    cd ${ROS_ROOT}/src && \
    export RCLCPP_VERSION="release/humble/rclcpp/$(apt-cache madison ros-humble-rclcpp | grep -m1 -oP 'ros-humble-rclcpp \| \K[^j]+(?=jammy)')" && \
    echo ${RCLCPP_VERSION} && \
    git clone https://github.com/ros2-gbp/rclcpp-release.git && cd rclcpp-release && git checkout ${RCLCPP_VERSION} && \
    patch -i /tmp/rclcpp-disable-tests.patch && \
    unset RCLCPP_VERSION && \
    git config user.email "<EMAIL>" && git config user.name "NVIDIA Builder" && \
    git remote add rclcpp https://github.com/ros2/rclcpp.git && git fetch rclcpp && \
    git cherry-pick 232262c02a1265830c7785b7547bd51e1124fcd8 && \
    source ${ROS_ROOT}/setup.bash && \
    cd ../ && rosdep install -i -r --from-paths rclcpp-release/ --rosdistro humble -y && \
    cd rclcpp-release && bloom-generate rosdebian && fakeroot debian/rules binary && \
    cd ../ && apt-get install -y --allow-downgrades ./*.deb && \
    echo "rclcpp https://github.com/ros2/rclcpp/commit/232262c02a1265830c7785b7547bd51e1124fcd8" >> ${ROS_ROOT}/VERSION && \
    cd ../ && rm -Rf src build log && \
    # Patch gtest to make it work with CXX 17
    sed -i '917i #ifdef GTEST_INTERNAL_NEED_REDUNDANT_CONSTEXPR_DECL' /usr/src/googletest/googletest/include/gtest/internal/gtest-internal.h && \
    sed -i '920i #endif' /usr/src/googletest/googletest/include/gtest/internal/gtest-internal.h && \
    sed -i '2392i #if defined(GTEST_INTERNAL_CPLUSPLUS_LANG) && \\\n    GTEST_INTERNAL_CPLUSPLUS_LANG < 201703L\n#define GTEST_INTERNAL_NEED_REDUNDANT_CONSTEXPR_DECL 1\n#endif' \
    /usr/src/googletest/googletest/include/gtest/internal/gtest-port.h

# Store list of packages (must be last)
RUN mkdir -p /opt/nvidia/isaac_ros_dev_base && dpkg-query -W | sort > /opt/nvidia/isaac_ros_dev_base/ros2_humble-end-packages.csv

SHELL ["/bin/bash", "-c"]