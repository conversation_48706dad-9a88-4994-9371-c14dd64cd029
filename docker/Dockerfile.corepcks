ARG BASE_IMAGE=aqita-arm:ros
FROM ${BASE_IMAGE}

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive \
    TZ=Etc/UTC \
    QT_X11_NO_MITSHM=1 \
    XDG_RUNTIME_DIR=/tmp \
    ISAAC_ROS_WS=/ros2_ws/src \
    OpenCV_DIR=/usr/local/lib/cmake/opencv4 \
    LD_LIBRARY_PATH=/usr/local/lib:${LD_LIBRARY_PATH}

# Get architecture
ARG PLATFORM=arm64

# Prevent installation of OpenCV 4.5 from apt
RUN apt-mark hold libopencv* python3-opencv

# Set RealSense variables
ARG LIBREALSENSE_SOURCE_VERSION=v2.55.1
ARG REALSENSE_ROS_GIT_URL=https://github.com/NVIDIA-ISAAC-ROS/realsense-ros.git
ARG REALSENSE_ROS_VERSION=release/4.51.1-isaac

# Set architecture-specific variables and install all dependencies in a single layer
RUN if [ "${PLATFORM}" = "arm64" ]; then \
        # ARM-specific settings
        echo "Configuring for ARM64 architecture" && \
        export CUDA_ARCH_BIN="8.6 8.7" && \
        export TARGET_ARCH="aarch64-linux-gnu" && \
        # For Jetson-specific packages
        export IS_JETSON=true; \
    else \
        # x86-specific settings
        echo "Configuring for x86_64 architecture" && \
        export CUDA_ARCH_BIN="7.0 7.5 8.0 8.6 8.9 9.0" && \
        export TARGET_ARCH="x86_64-linux-gnu" && \
        export IS_JETSON=false; \
    fi && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
        libsqlite3-dev libgoogle-glog-dev libgflags-dev libpcl-dev libboost-all-dev \
        libyaml-cpp-dev libflann-dev libsuitesparse-dev libprotobuf-dev liboctomap-dev \
        git git-lfs cmake build-essential python3-dev \
        libglew-dev libfftw3-dev \
        liblapack-dev libopenmpi-dev libeigen3-dev python3-pandas python3-numpy \
        libhdf5-dev libvtk9-dev python3-vtk9 libpng-dev libjpeg-dev libtiff-dev \
        libgl1-mesa-dev libwayland-dev libxkbcommon-dev wayland-protocols \
        libegl1-mesa-dev \
        ffmpeg libavcodec-dev libavutil-dev libavformat-dev libswscale-dev \
        libavdevice-dev libopenexr-dev && \
    if [ "${PLATFORM}" = "arm64" ]; then \
        # ARM-specific packages
        apt-get install -y --no-install-recommends libopenni-dev libopenni2-dev libuvc-dev; \
    fi && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* && \
    # Ensure OpenCV directory is correctly set
    echo "/usr/local/lib/cmake/opencv4" > /tmp/opencv_dir.txt && \
    # Add OpenCV to library path for all users
    echo "/usr/local/lib" > /etc/ld.so.conf.d/opencv.conf && \
    ldconfig

# Copy RealSense scripts and adjust them for multi-arch support
COPY scripts/build-librealsense.sh /opt/realsense/build-librealsense.sh
COPY scripts/install-realsense-dependencies.sh /opt/realsense/install-realsense-dependencies.sh
COPY scripts/hotplug-realsense.sh /opt/realsense/hotplug-realsense.sh
COPY udev_rules/99-realsense-libusb-custom.rules /etc/udev/rules.d/99-realsense-libusb-custom.rules

# Build and install RealSense with platform-specific adjustments
RUN chmod +x /opt/realsense/install-realsense-dependencies.sh && \
    /opt/realsense/install-realsense-dependencies.sh && \
    chmod +x /opt/realsense/build-librealsense.sh && \
    /opt/realsense/build-librealsense.sh -v ${LIBREALSENSE_SOURCE_VERSION} -j 8 && \
    # Create symlinks to help with library discovery on x86_64
    if [ "${PLATFORM}" != "arm64" ]; then \
        ln -sf /usr/local/lib/libopencv_* /usr/lib/x86_64-linux-gnu/ || true && \
        ln -sf /usr/local/lib/cmake/opencv4 /usr/share/cmake-3.*/Modules/opencv4 || true && \
        export PKG_CONFIG_PATH=/usr/local/lib/pkgconfig:$PKG_CONFIG_PATH; \
    fi

# Build cv_bridge and other OpenCV-dependent ROS packages
RUN mkdir -p /cv_bridge_ws/src && cd /cv_bridge_ws/src && \
    # Clone core vision packages
    git clone https://github.com/ros-perception/vision_opencv.git -b humble && \
    cd vision_opencv && \
    # Remove any unnecessary packages
    rm -rf opencv_tests && \
    # Add custom patches for OpenCV 4.10 compatibility
    cd cv_bridge && \
    sed -i 's/find_package(OpenCV 4 REQUIRED/find_package(OpenCV 4 REQUIRED PATHS ${OpenCV_DIR} NO_DEFAULT_PATH/g' CMakeLists.txt && \
    cd /cv_bridge_ws && \
    # Build in manageable stages, starting with core dependencies
    . /opt/ros/humble/setup.bash && \
    export OpenCV_DIR=/usr/local/lib/cmake/opencv4 && \
    colcon build --packages-select cv_bridge image_geometry \
        --cmake-args \
        -DCMAKE_BUILD_TYPE=Release \
        -DOpenCV_DIR=${OpenCV_DIR} \
        -DBUILD_TESTING=OFF && \
    # Make sure the workspace can be sourced
    chmod -R 755 /cv_bridge_ws/install && \
    # Create symlinks to ensure all ROS components can find our custom built libraries
    cp -r /cv_bridge_ws/install/*/lib/* /opt/ros/humble/lib/ || true && \
    cp -r /cv_bridge_ws/install/*/include/* /opt/ros/humble/include/ || true && \
    cp -r /cv_bridge_ws/install/*/share/* /opt/ros/humble/share/ || true && \
    # Update the ldconfig to find our libraries
    ldconfig

# After building cv_bridge, clone the rest of the packages for the full vision workspace
RUN cd /cv_bridge_ws/src && \
    git clone https://github.com/ros-perception/image_common.git -b humble && \
    git clone https://github.com/ros-perception/image_transport_plugins.git -b humble && \
    git clone https://github.com/ros-perception/image_pipeline.git -b humble && \
    # For rtabmap dependencies
    git clone https://github.com/octomap/octomap_msgs.git -b humble && \
    # Clone UI packages if needed
    git clone https://github.com/ros-visualization/rqt_image_view.git -b humble && \
    cd /cv_bridge_ws && \
    . /opt/ros/humble/setup.bash && \
    export OpenCV_DIR=/usr/local/lib/cmake/opencv4 && \
    export MAKEFLAGS="-j$(nproc)" && \
    colcon build --packages-up-to image_common image_transport_plugins image_pipeline octomap_msgs rqt_image_view \
        --cmake-args \
        -DCMAKE_BUILD_TYPE=Release \
        -DOpenCV_DIR=${OpenCV_DIR} \
        -DBUILD_TESTING=OFF && \
    # Create symlinks to ensure all ROS components can find our custom built libraries
    cp -r /cv_bridge_ws/install/lib/* /opt/ros/humble/lib/ || true && \
    cp -r /cv_bridge_ws/install/include/* /opt/ros/humble/include/ || true && \
    cp -r /cv_bridge_ws/install/share/* /opt/ros/humble/share/ || true && \
    # Update the ldconfig to find our libraries
    ldconfig

# Install realsense-ros ROS 2 package using the Isaac ROS approach but with modified dependencies
RUN mkdir -p ${ROS_ROOT}/src && cd ${ROS_ROOT}/src && \
    git clone ${REALSENSE_ROS_GIT_URL} -b ${REALSENSE_ROS_VERSION} && \
    cd realsense-ros && source ${ROS_ROOT}/setup.bash && \
    source /cv_bridge_ws/install/setup.bash && \
    # Build and install realsense2_camera_msgs
    cd realsense2_camera_msgs && bloom-generate rosdebian && \
    # Remove OpenCV dependencies from control file
    sed -i '/libopencv/d' debian/control && \
    sed -i '/python3-opencv/d' debian/control && \
    fakeroot debian/rules binary && \
    cd .. && apt-get update && apt-get install -y ./*.deb && rm ./*.deb && \
    # Build and install realsense2_description
    cd realsense2_description && bloom-generate rosdebian && \
    fakeroot debian/rules binary && \
    cd .. && apt-get install -y ./*.deb && rm ./*.deb && \
    # Build and install realsense2_camera with modified dependencies
    cd realsense2_camera && bloom-generate rosdebian && \
    # Modify to use locally built libraries and remove conflicting dependencies
    sed -i 's/dh_shlibdeps -/dh_shlibdeps --dpkg-shlibdeps-params=--ignore-missing-info -l\/usr\/local\/lib -/g' debian/rules && \
    sed -i 's/ros-humble-librealsense2, //g' debian/control && \
    sed -i '/libopencv/d' debian/control && \
    sed -i '/python3-opencv/d' debian/control && \
    sed -i '/ros-humble-cv-bridge/d' debian/control && \
    fakeroot debian/rules binary && \
    cd .. && apt-get install -y ./*.deb && rm ./*.deb && \
    cd .. && rm -rf realsense-ros

# Install dependencies for SLAM with architecture-specific settings in a single layer
RUN mkdir -p /tmp/slam_deps && cd /tmp/slam_deps && \
    # Install g2o
    git clone https://github.com/RainerKuemmerle/g2o.git && \
    cd g2o && mkdir build && cd build && \
    cmake -DCMAKE_BUILD_TYPE=Release \
        -DBUILD_WITH_MARCH_NATIVE=$(if [ "${PLATFORM}" = "arm64" ]; then echo "ON"; else echo "OFF"; fi) \
        -DG2O_BUILD_APPS=OFF \
        -DG2O_BUILD_EXAMPLES=OFF -DG2O_USE_OPENMP=ON -DBUILD_SHARED_LIBS=ON -DG2O_USE_CSPARSE=ON .. && \
    make -j$(nproc) && make install && ldconfig && \
    cd /tmp/slam_deps && \
    # Install CudaSift with architecture-specific settings
    git clone --depth 1 https://github.com/matlabbe/CudaSift.git && \
    cd CudaSift && mkdir build && cd build && \
    if [ "${PLATFORM}" = "arm64" ]; then \
        # ARM-specific CUDA arch
        cmake -DCMAKE_BUILD_TYPE=Release -DCUDA_ARCH_BIN="8.6 8.7" -DCMAKE_INSTALL_PREFIX=/usr/local ..; \
    else \
        # x86-specific CUDA arch
        cmake -DCMAKE_BUILD_TYPE=Release -DCUDA_ARCH_BIN="7.0 7.5 8.0 8.6 8.9" -DCMAKE_INSTALL_PREFIX=/usr/local ..; \
    fi && \
    make -j$(nproc) && make install && ldconfig && \
    cd /tmp/slam_deps && \
    # Install Pangolin with fixed parameters
    git clone --recursive https://github.com/stevenlovegrove/Pangolin.git && \
    cd Pangolin && mkdir build && cd build && \
    cmake -DCMAKE_BUILD_TYPE=Release -DBUILD_EXAMPLES=OFF -DBUILD_TOOLS=OFF -DBUILD_TESTS=OFF \
          -DCMAKE_INSTALL_PREFIX=/usr/local -DCMAKE_CXX_FLAGS="-std=c++14 -Wno-error=type-limits" \
          -DBUILD_PANGOLIN_OPENEXR=OFF .. && \
    make -j$(nproc) && make install && ldconfig && \
    rm -rf /tmp/slam_deps

# Build ORB-SLAM3 in a single layer
RUN mkdir -p /tmp/orb_slam && cd /tmp/orb_slam && \
    git clone --branch v1.0-release https://github.com/UZ-SLAMLab/ORB_SLAM3.git && \
    cd ORB_SLAM3 && \
    # Update C++11 to C++14
    sed -i 's/std=c++11/std=c++14/g' CMakeLists.txt && \
    # Build dependencies in sequence
    cd Thirdparty/DBoW2 && mkdir build && cd build && \
    cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_CXX_FLAGS="-std=c++14" && \
    make -j$(nproc) && \
    cd ../../g2o && mkdir build && cd build && \
    cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_CXX_FLAGS="-std=c++14" && \
    make -j$(nproc) && \
    cd ../../Sophus && mkdir build && cd build && \
    cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_CXX_FLAGS="-std=c++14" && \
    make -j$(nproc) && \
    cd ../../../ && cd Vocabulary && tar -xf ORBvoc.txt.tar.gz && cd .. && \
    echo 'export ORB_SLAM_ROOT_DIR=/tmp/orb_slam/ORB_SLAM3' >> /etc/bash.bashrc && \
    mkdir build && cd build && \
    cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_CXX_FLAGS="-std=c++14 -Wno-error=type-limits" && \
    make -j$(nproc) && \
    cd .. && \
    cp -r include/* /usr/local/include/ && \
    mkdir -p /usr/local/share/orbslam3/Vocabulary && \
    cp -r Vocabulary/* /usr/local/share/orbslam3/Vocabulary/ && \
    cp lib/libORB_SLAM3.so /usr/local/lib/ && \
    ldconfig && \
    rm -rf /tmp/orb_slam

# Set up ROS2 workspace and build RTAB-Map with specific CUDA settings
RUN mkdir -p /ros2_ws/src && cd /ros2_ws/src && \
    git clone https://github.com/introlab/rtabmap.git && \
    git clone -b ros2 https://github.com/introlab/rtabmap_ros.git && \
    git clone -b ros2 https://github.com/appliedAI-Initiative/orb_slam_2_ros.git && \
    # Build RTAB-Map with explicit OpenCV settings
    cd rtabmap && mkdir -p build && cd build && \
    # Set architecture-specific CUDA settings
    if [ "${PLATFORM}" = "arm64" ]; then \
        export CUDA_ARCH_BIN="8.6 8.7" && \
        export WITH_TEGRA="ON"; \
    else \
        export CUDA_ARCH_BIN="7.0 7.5 8.0 8.6 8.9 9.0" && \
        export WITH_TEGRA="OFF"; \
    fi && \
    # Use architecture-specific settings in build
    export OpenCV_DIR=/usr/local/lib/cmake/opencv4 && \
    cmake -DCMAKE_BUILD_TYPE=Release -DWITH_REALSENSE2=ON -DWITH_G2O=ON -DWITH_CUDA=ON \
          -DCUDA_ARCH_BIN="${CUDA_ARCH_BIN}" -DCUDA_TOOLKIT_ROOT_DIR=/usr/local/cuda \
          -DCUDA_NVCC_FLAGS="--expt-relaxed-constexpr" -DWITH_OPENMP=ON -DWITH_CUDNN=ON \
          -DWITH_TEGRA=${WITH_TEGRA} -DWITH_CCCORELIB=ON -DWITH_LOAM=ON -DWITH_FASTCV=ON -DWITH_TORCH=ON \
          -DWITH_ORB_SLAM=ON -DWITH_CUDASIFT=ON -DWITH_OCTOMAP=ON -DWITH_PDAL=ON \
          -DWITH_MARCHING_CUBES=ON -DCUDNN_INCLUDE_DIR=/usr/include \
          -DCUDNN_LIBRARY=/usr/lib/${PLATFORM}-linux-gnu/libcudnn.so -DWITH_PYTHON=ON \
          -DPYTHON_EXECUTABLE=$(which python3) -DOpenCV_DIR=${OpenCV_DIR} \
          -DWITH_RGBD_SYNC=ON -DWITH_STATIC_LIBS=ON -DBUILD_APP=ON -DBUILD_TOOLS=ON .. && \
    make -j$(nproc) && make install && ldconfig

# Build ROS packages with platform-specific adjustments
RUN cd /ros2_ws && \
    . /opt/ros/humble/setup.bash && \
    . /cv_bridge_ws/install/setup.bash && \
    export MAKEFLAGS="-j4" && \
    # Ensure OpenCV is used correctly
    export OpenCV_DIR=/usr/local/lib/cmake/opencv4 && \
    rosdep update && \
    rosdep install -i -r --from-paths src --rosdistro humble -y --skip-keys "libopencv-dev libopencv-contrib-dev libopencv-imgproc-dev ros-humble-gazebo-ros-pkgs python-opencv python3-opencv nvblox gazebo-ros-pkgs cv_bridge image_geometry image_transport image_publisher ros-humble-librealsense2 realsense2_camera realsense2_description realsense2_camera_msgs" && \
    mkdir -p /ros2_ws/src/perception_pcl/pcl_ros/include/message_filters/detail && \
    cp -R /opt/ros/humble/include/message_filters/* /ros2_ws/src/perception_pcl/pcl_ros/include/message_filters/ || true && \
    # Build in stages with proper flags
    colcon build --merge-install \
        --packages-select rtabmap_msgs rtabmap_python nav2_common \
        --cmake-args \
        -DOpenCV_DIR=${OpenCV_DIR} \
        -DCMAKE_BUILD_TYPE=Release \
        -DBUILD_TESTING=OFF && \
    touch src/perception_pcl/pcl_ros/COLCON_IGNORE || true && \
    colcon build --merge-install --packages-up-to rtabmap_ros \
        --packages-skip nav2_map_server nav2_msgs \
        --cmake-args \
        -DOpenCV_DIR=${OpenCV_DIR} \
        -DCMAKE_BUILD_TYPE=Release \
        -DWITH_RGBD_SYNC=ON \
        -DRTABMAP_SYNC_MULTI_RGBD=ON \
        -DRTABMAP_SYNC_USER_DATA=ON \
        -DBUILD_TESTING=OFF && \
    # Clean up any temporary files
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Clone and build the navigation stack and NVIDIA packages
RUN mkdir -p /ros2_ws/src && cd /ros2_ws/src && \
    git clone https://github.com/ros-navigation/navigation2.git -b humble && \
    git clone -b release-3.2 https://github.com/NVIDIA-ISAAC-ROS/isaac_ros_common.git && \
    git clone -b release-3.2 https://github.com/NVIDIA-ISAAC-ROS/isaac_ros_compression.git && \
    git clone -b release-3.2 https://github.com/NVIDIA-ISAAC-ROS/isaac_ros_image_pipeline.git && \
    git clone -b release-3.2 https://github.com/NVIDIA-ISAAC-ROS/isaac_ros_nitros.git && \
    git clone -b release-3.2 https://github.com/NVIDIA-ISAAC-ROS/isaac_ros_visual_slam.git && \
    git clone --recursive -b release-3.2 https://github.com/NVIDIA-ISAAC-ROS/isaac_ros_nvblox.git && \
    # Build navigation modules one by one to avoid memory issues
    cd /ros2_ws && \
    . /opt/ros/humble/setup.bash && \
    . /cv_bridge_ws/install/setup.bash && \
    # Build nav2 packages in sequence to avoid memory overload
    colcon build --merge-install --packages-select nav2_common && \
    colcon build --merge-install --packages-select nav2_msgs && \
    colcon build --merge-install --packages-up-to nav2_amcl && \
    colcon build --merge-install --packages-up-to nav2_behavior_tree && \
    colcon build --merge-install --packages-up-to nav2_behaviors && \
    colcon build --merge-install --packages-up-to nav2_bt_navigator && \
    colcon build --merge-install --packages-up-to nav2_collision_monitor && \
    colcon build --merge-install --packages-up-to nav2_constrained_smoother && \
    colcon build --merge-install --packages-up-to nav2_controller && \
    colcon build --merge-install --packages-up-to nav2_dwb_controller && \
    colcon build --merge-install --packages-up-to nav2_graceful_controller && \
    colcon build --merge-install --packages-up-to nav2_mppi_controller && \
    colcon build --merge-install --packages-up-to nav2_navfn_planner && \
    colcon build --merge-install --packages-up-to nav2_planner && \
    colcon build --merge-install --packages-up-to nav2_regulated_pure_pursuit_controller && \
    colcon build --merge-install --packages-up-to nav2_rotation_shim_controller && \
    colcon build --merge-install --packages-up-to nav2_simple_commander && \
    colcon build --merge-install --packages-up-to nav2_smac_planner && \
    colcon build --merge-install --packages-up-to nav2_smoother && \
    colcon build --merge-install --packages-up-to nav2_theta_star_planner && \
    colcon build --merge-install --packages-up-to nav2_velocity_smoother && \
    colcon build --merge-install --packages-up-to nav2_waypoint_follower && \
    colcon build --merge-install --packages-up-to nav_2d_msgs && \
    colcon build --merge-install --packages-up-to nav_2d_utils && \
    colcon build --merge-install --packages-up-to navigation2 && \
    colcon build --merge-install --packages-up-to nav2_rviz_plugins && \
    colcon build --merge-install --packages-up-to nav2_bringup && \
    # Build NVIDIA Isaac packages
    colcon build --merge-install --packages-up-to isaac_ros_visual_slam && \
    colcon build --merge-install --packages-up-to isaac_ros_nvblox && \
    colcon build --merge-install --packages-up-to nvblox_examples_bringup && \
    # Remove source files to reduce image size (optional)
    rm -rf /ros2_ws/src/* /ros2_ws/build/* && \
    # Clean up
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Create entrypoint script that works on all architectures
RUN echo '#!/bin/bash' > /entrypoint.sh \
    && echo 'source /opt/ros/humble/setup.bash || true' >> /entrypoint.sh \
    && echo 'if [ -f "/cv_bridge_ws/install/setup.bash" ]; then' >> /entrypoint.sh \
    && echo '  source /cv_bridge_ws/install/setup.bash || true' >> /entrypoint.sh \
    && echo 'fi' >> /entrypoint.sh \
    && echo 'if [ -f "/ros2_ws/install/setup.bash" ]; then' >> /entrypoint.sh \
    && echo '  source /ros2_ws/install/setup.bash || true' >> /entrypoint.sh \
    && echo 'fi' >> /entrypoint.sh \
    && echo 'if [ -f "/workspace/thirdparty/isaac_ros_ws/install/setup.bash" ]; then' >> /entrypoint.sh \
    && echo '  source /workspace/thirdparty/isaac_ros_ws/install/setup.bash || true' >> /entrypoint.sh \
    && echo 'fi' >> /entrypoint.sh \
    && echo 'exec "$@"' >> /entrypoint.sh \
    && chmod +x /entrypoint.sh \
    && echo 'source /opt/ros/humble/setup.bash' >> /etc/bash.bashrc \
    && echo 'if [ -f "/cv_bridge_ws/install/setup.bash" ]; then' >> /etc/bash.bashrc \
    && echo '  source /cv_bridge_ws/install/setup.bash' >> /etc/bash.bashrc \
    && echo 'fi' >> /etc/bash.bashrc \
    && echo 'if [ -f "/ros2_ws/install/setup.bash" ]; then' >> /etc/bash.bashrc \
    && echo '  source /ros2_ws/install/setup.bash' >> /etc/bash.bashrc \
    && echo 'fi' >> /etc/bash.bashrc \
    && echo 'echo "AQITA container environment ready. ROS2 Humble has been sourced."' >> /etc/bash.bashrc \
    && echo 'echo "Using OpenCV from /usr/local with custom cv_bridge"' >> /etc/bash.bashrc

# Ensure the entrypoint is properly set
WORKDIR /
ENTRYPOINT ["/entrypoint.sh"]
CMD ["bash"]