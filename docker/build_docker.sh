#!/bin/bash

# Exit on error
set -e

# Define variables
# Set repository name based on architecture
PLATFORM=$(uname -m)
if [ "$PLATFORM" = "x86_64" ]; then
    REPO_NAME="aqita-x86"
    PLATFORM_ARG="amd64"
elif [ "$PLATFORM" = "aarch64" ]; then
    REPO_NAME="aqita-arm"
    PLATFORM_ARG="arm64"
else
    echo "Unsupported platform: $PLATFORM"
    exit 1
fi

BASE_TAG="${REPO_NAME}:base"
OPENCV_TAG="${REPO_NAME}:opencv"
ROS_TAG="${REPO_NAME}:ros"
FINAL_TAG="${REPO_NAME}:latest"
USE_CACHE="no"  # Default to no cache

# Process command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --use-cache)
      USE_CACHE="yes"
      shift
      ;;
    --help)
      echo "Usage: $0 [--use-cache]"
      echo "  --use-cache    Use Docker cache during build"
      echo "  --help         Display this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Run '$0 --help' for usage information"
      exit 1
      ;;
  esac
done

# Print header
echo "Building ${REPO_NAME} Docker container"
if [ "$USE_CACHE" = "yes" ]; then
    echo "Cache: ENABLED"
else
    echo "Cache: DISABLED"
fi

# Set Docker directory path
DOCKER_DIR="docker"
if [ ! -d "$DOCKER_DIR" ]; then
    mkdir -p "$DOCKER_DIR"
fi

# Check if all required Dockerfiles exist
for DOCKERFILE in "Dockerfile.base" "Dockerfile.opencv" "Dockerfile.ros2_humble" "Dockerfile.corepcks"; do
    if [ ! -f "$DOCKER_DIR/$DOCKERFILE" ]; then
        echo "Error: $DOCKER_DIR/$DOCKERFILE not found!"
        exit 1
    fi
done

# Create necessary directories
mkdir -p "$DOCKER_DIR/scripts" "$DOCKER_DIR/udev_rules" "$DOCKER_DIR/rosdep" "$DOCKER_DIR/patches"

# Determine platform
echo "Building for platform: $PLATFORM_ARG"
echo "Using repository name: $REPO_NAME"

# Function to build each stage
build_stage() {
    local name=$1
    local dockerfile=$2
    local tag=$3
    local base_image=$4
    
    echo "Building $name image ($tag)..."
    
    # Determine cache flag based on global setting
    local cache_flag=""
    if [ "$USE_CACHE" = "no" ]; then
        cache_flag="--no-cache"
    fi
    
    # Create a temporary file for image ID
    local IID_FILE
    IID_FILE=$(mktemp)
    
    # Build the image
    if [ -z "$base_image" ]; then
        docker build $cache_flag \
            --build-arg PLATFORM="$PLATFORM_ARG" \
            -f "$DOCKER_DIR/$dockerfile" \
            -t "$tag" \
            --iidfile "$IID_FILE" "$DOCKER_DIR"
        
        # Check if build failed
        if [ $? -ne 0 ]; then
            echo "Error: Failed to build $name image!"
            rm -f "$IID_FILE"
            exit 1
        fi
    else
        docker build $cache_flag \
            --build-arg PLATFORM="$PLATFORM_ARG" \
            --build-arg BASE_IMAGE="$base_image" \
            -f "$DOCKER_DIR/$dockerfile" \
            -t "$tag" \
            --iidfile "$IID_FILE" "$DOCKER_DIR"
        
        # Check if build failed
        if [ $? -ne 0 ]; then
            echo "Error: Failed to build $name image!"
            rm -f "$IID_FILE"
            exit 1
        fi
    fi
    
    # Verify image ID exists and is valid
    if [ ! -f "$IID_FILE" ] || [ ! -s "$IID_FILE" ]; then
        echo "Error: Image ID file not found or empty!"
        [ -f "$IID_FILE" ] && rm -f "$IID_FILE"
        exit 1
    fi
    
    # Read the image ID
    local IMAGE_ID
    IMAGE_ID=$(cat "$IID_FILE")
    rm -f "$IID_FILE"
    
    echo "Built image ID: $IMAGE_ID"
    
    # Ensure the image was properly tagged
    if ! docker image inspect "$tag" > /dev/null 2>&1; then
        echo "Warning: Image ${tag} not found, tagging manually"
        docker tag "$IMAGE_ID" "$tag"
        if [ $? -ne 0 ]; then
            echo "Error: Failed to tag image $IMAGE_ID as $tag"
            exit 1
        fi
    fi
    
    echo "$name image built and tagged as $tag"
}

echo "Building in 4 stages..."

# Build base image
build_stage "base" "Dockerfile.base" "$BASE_TAG"

# Build OpenCV image
build_stage "OpenCV" "Dockerfile.opencv" "$OPENCV_TAG" "$BASE_TAG"

# Build ROS image
build_stage "ROS" "Dockerfile.ros2_humble" "$ROS_TAG" "$OPENCV_TAG"

# Build final image with core packages
build_stage "final" "Dockerfile.corepcks" "$FINAL_TAG" "$ROS_TAG"

echo "Build completed successfully!"
echo "Container tags:"
echo "- Base: $BASE_TAG"
echo "- OpenCV: $OPENCV_TAG"
echo "- ROS: $ROS_TAG"
echo "- Final: $FINAL_TAG"

# Show all built images
echo "All built images:"
docker images "$REPO_NAME" --format "{{.Repository}}:{{.Tag}} - {{.Size}}"

exit 0