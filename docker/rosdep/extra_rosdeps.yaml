# Copyright (c) 2024, NVIDIA CORPORATION.  All rights reserved.
#
# NVIDIA CORPORATION and its licensors retain all intellectual property
# and proprietary rights in and to this software, related documentation
# and any modifications thereto.  Any use, reproduction, disclosure or
# distribution of this software and related documentation without an express
# license agreement from NVIDIA CORPORATION is strictly prohibited.

# Reformat this file with `yq 'sort_keys(.)' -i <path/to/this/file>`

aandd_ekew_driver_py:
  ubuntu:
    focal: [ros-humble-aandd-ekew-driver-py]
    jammy: [ros-humble-aandd-ekew-driver-py]
acado_vendor:
  ubuntu:
    focal: [ros-humble-acado-vendor]
    jammy: [ros-humble-acado-vendor]
acado_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-acado-vendor-dbgsym]
    jammy: [ros-humble-acado-vendor-dbgsym]
ackermann_msgs:
  ubuntu:
    focal: [ros-humble-ackermann-msgs]
    jammy: [ros-humble-ackermann-msgs]
ackermann_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-ackermann-msgs-dbgsym]
    jammy: [ros-humble-ackermann-msgs-dbgsym]
ackermann_steering_controller:
  ubuntu:
    focal: [ros-humble-ackermann-steering-controller]
    jammy: [ros-humble-ackermann-steering-controller]
ackermann_steering_controller_dbgsym:
  ubuntu:
    focal: [ros-humble-ackermann-steering-controller-dbgsym]
    jammy: [ros-humble-ackermann-steering-controller-dbgsym]
action_msgs:
  ubuntu:
    focal: [ros-humble-action-msgs]
    jammy: [ros-humble-action-msgs]
action_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-action-msgs-dbgsym]
    jammy: [ros-humble-action-msgs-dbgsym]
action_tutorials_cpp:
  ubuntu:
    focal: [ros-humble-action-tutorials-cpp]
    jammy: [ros-humble-action-tutorials-cpp]
action_tutorials_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-action-tutorials-cpp-dbgsym]
    jammy: [ros-humble-action-tutorials-cpp-dbgsym]
action_tutorials_interfaces:
  ubuntu:
    focal: [ros-humble-action-tutorials-interfaces]
    jammy: [ros-humble-action-tutorials-interfaces]
action_tutorials_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-action-tutorials-interfaces-dbgsym]
    jammy: [ros-humble-action-tutorials-interfaces-dbgsym]
action_tutorials_py:
  ubuntu:
    focal: [ros-humble-action-tutorials-py]
    jammy: [ros-humble-action-tutorials-py]
actionlib_msgs:
  ubuntu:
    focal: [ros-humble-actionlib-msgs]
    jammy: [ros-humble-actionlib-msgs]
actionlib_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-actionlib-msgs-dbgsym]
    jammy: [ros-humble-actionlib-msgs-dbgsym]
actuator_msgs:
  ubuntu:
    focal: [ros-humble-actuator-msgs]
    jammy: [ros-humble-actuator-msgs]
actuator_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-actuator-msgs-dbgsym]
    jammy: [ros-humble-actuator-msgs-dbgsym]
adaptive_component:
  ubuntu:
    focal: [ros-humble-adaptive-component]
    jammy: [ros-humble-adaptive-component]
adaptive_component_dbgsym:
  ubuntu:
    focal: [ros-humble-adaptive-component-dbgsym]
    jammy: [ros-humble-adaptive-component-dbgsym]
admittance_controller:
  ubuntu:
    focal: [ros-humble-admittance-controller]
    jammy: [ros-humble-admittance-controller]
admittance_controller_dbgsym:
  ubuntu:
    focal: [ros-humble-admittance-controller-dbgsym]
    jammy: [ros-humble-admittance-controller-dbgsym]
affordance_primitives:
  ubuntu:
    focal: [ros-humble-affordance-primitives]
    jammy: [ros-humble-affordance-primitives]
affordance_primitives_dbgsym:
  ubuntu:
    focal: [ros-humble-affordance-primitives-dbgsym]
    jammy: [ros-humble-affordance-primitives-dbgsym]
ament_acceleration:
  ubuntu:
    focal: [ros-humble-ament-acceleration]
    jammy: [ros-humble-ament-acceleration]
ament_black:
  ubuntu:
    focal: [ros-humble-ament-black]
    jammy: [ros-humble-ament-black]
ament_clang_format:
  ubuntu:
    focal: [ros-humble-ament-clang-format]
    jammy: [ros-humble-ament-clang-format]
ament_clang_tidy:
  ubuntu:
    focal: [ros-humble-ament-clang-tidy]
    jammy: [ros-humble-ament-clang-tidy]
ament_cmake:
  ubuntu:
    focal: [ros-humble-ament-cmake]
    jammy: [ros-humble-ament-cmake]
ament_cmake_auto:
  ubuntu:
    focal: [ros-humble-ament-cmake-auto]
    jammy: [ros-humble-ament-cmake-auto]
ament_cmake_black:
  ubuntu:
    focal: [ros-humble-ament-cmake-black]
    jammy: [ros-humble-ament-cmake-black]
ament_cmake_catch2:
  ubuntu:
    focal: [ros-humble-ament-cmake-catch2]
    jammy: [ros-humble-ament-cmake-catch2]
ament_cmake_clang_format:
  ubuntu:
    focal: [ros-humble-ament-cmake-clang-format]
    jammy: [ros-humble-ament-cmake-clang-format]
ament_cmake_clang_tidy:
  ubuntu:
    focal: [ros-humble-ament-cmake-clang-tidy]
    jammy: [ros-humble-ament-cmake-clang-tidy]
ament_cmake_copyright:
  ubuntu:
    focal: [ros-humble-ament-cmake-copyright]
    jammy: [ros-humble-ament-cmake-copyright]
ament_cmake_core:
  ubuntu:
    focal: [ros-humble-ament-cmake-core]
    jammy: [ros-humble-ament-cmake-core]
ament_cmake_cppcheck:
  ubuntu:
    focal: [ros-humble-ament-cmake-cppcheck]
    jammy: [ros-humble-ament-cmake-cppcheck]
ament_cmake_cpplint:
  ubuntu:
    focal: [ros-humble-ament-cmake-cpplint]
    jammy: [ros-humble-ament-cmake-cpplint]
ament_cmake_export_definitions:
  ubuntu:
    focal: [ros-humble-ament-cmake-export-definitions]
    jammy: [ros-humble-ament-cmake-export-definitions]
ament_cmake_export_dependencies:
  ubuntu:
    focal: [ros-humble-ament-cmake-export-dependencies]
    jammy: [ros-humble-ament-cmake-export-dependencies]
ament_cmake_export_include_directories:
  ubuntu:
    focal: [ros-humble-ament-cmake-export-include-directories]
    jammy: [ros-humble-ament-cmake-export-include-directories]
ament_cmake_export_interfaces:
  ubuntu:
    focal: [ros-humble-ament-cmake-export-interfaces]
    jammy: [ros-humble-ament-cmake-export-interfaces]
ament_cmake_export_libraries:
  ubuntu:
    focal: [ros-humble-ament-cmake-export-libraries]
    jammy: [ros-humble-ament-cmake-export-libraries]
ament_cmake_export_link_flags:
  ubuntu:
    focal: [ros-humble-ament-cmake-export-link-flags]
    jammy: [ros-humble-ament-cmake-export-link-flags]
ament_cmake_export_targets:
  ubuntu:
    focal: [ros-humble-ament-cmake-export-targets]
    jammy: [ros-humble-ament-cmake-export-targets]
ament_cmake_flake8:
  ubuntu:
    focal: [ros-humble-ament-cmake-flake8]
    jammy: [ros-humble-ament-cmake-flake8]
ament_cmake_gen_version_h:
  ubuntu:
    focal: [ros-humble-ament-cmake-gen-version-h]
    jammy: [ros-humble-ament-cmake-gen-version-h]
ament_cmake_gmock:
  ubuntu:
    focal: [ros-humble-ament-cmake-gmock]
    jammy: [ros-humble-ament-cmake-gmock]
ament_cmake_google_benchmark:
  ubuntu:
    focal: [ros-humble-ament-cmake-google-benchmark]
    jammy: [ros-humble-ament-cmake-google-benchmark]
ament_cmake_gtest:
  ubuntu:
    focal: [ros-humble-ament-cmake-gtest]
    jammy: [ros-humble-ament-cmake-gtest]
ament_cmake_include_directories:
  ubuntu:
    focal: [ros-humble-ament-cmake-include-directories]
    jammy: [ros-humble-ament-cmake-include-directories]
ament_cmake_libraries:
  ubuntu:
    focal: [ros-humble-ament-cmake-libraries]
    jammy: [ros-humble-ament-cmake-libraries]
ament_cmake_lint_cmake:
  ubuntu:
    focal: [ros-humble-ament-cmake-lint-cmake]
    jammy: [ros-humble-ament-cmake-lint-cmake]
ament_cmake_mypy:
  ubuntu:
    focal: [ros-humble-ament-cmake-mypy]
    jammy: [ros-humble-ament-cmake-mypy]
ament_cmake_nose:
  ubuntu:
    focal: [ros-humble-ament-cmake-nose]
    jammy: [ros-humble-ament-cmake-nose]
ament_cmake_pclint:
  ubuntu:
    focal: [ros-humble-ament-cmake-pclint]
    jammy: [ros-humble-ament-cmake-pclint]
ament_cmake_pep257:
  ubuntu:
    focal: [ros-humble-ament-cmake-pep257]
    jammy: [ros-humble-ament-cmake-pep257]
ament_cmake_pycodestyle:
  ubuntu:
    focal: [ros-humble-ament-cmake-pycodestyle]
    jammy: [ros-humble-ament-cmake-pycodestyle]
ament_cmake_pyflakes:
  ubuntu:
    focal: [ros-humble-ament-cmake-pyflakes]
    jammy: [ros-humble-ament-cmake-pyflakes]
ament_cmake_pytest:
  ubuntu:
    focal: [ros-humble-ament-cmake-pytest]
    jammy: [ros-humble-ament-cmake-pytest]
ament_cmake_python:
  ubuntu:
    focal: [ros-humble-ament-cmake-python]
    jammy: [ros-humble-ament-cmake-python]
ament_cmake_ros:
  ubuntu:
    focal: [ros-humble-ament-cmake-ros]
    jammy: [ros-humble-ament-cmake-ros]
ament_cmake_target_dependencies:
  ubuntu:
    focal: [ros-humble-ament-cmake-target-dependencies]
    jammy: [ros-humble-ament-cmake-target-dependencies]
ament_cmake_test:
  ubuntu:
    focal: [ros-humble-ament-cmake-test]
    jammy: [ros-humble-ament-cmake-test]
ament_cmake_uncrustify:
  ubuntu:
    focal: [ros-humble-ament-cmake-uncrustify]
    jammy: [ros-humble-ament-cmake-uncrustify]
ament_cmake_vendor_package:
  ubuntu:
    focal: [ros-humble-ament-cmake-vendor-package]
    jammy: [ros-humble-ament-cmake-vendor-package]
ament_cmake_version:
  ubuntu:
    focal: [ros-humble-ament-cmake-version]
    jammy: [ros-humble-ament-cmake-version]
ament_cmake_xmllint:
  ubuntu:
    focal: [ros-humble-ament-cmake-xmllint]
    jammy: [ros-humble-ament-cmake-xmllint]
ament_copyright:
  ubuntu:
    focal: [ros-humble-ament-copyright]
    jammy: [ros-humble-ament-copyright]
ament_cppcheck:
  ubuntu:
    focal: [ros-humble-ament-cppcheck]
    jammy: [ros-humble-ament-cppcheck]
ament_cpplint:
  ubuntu:
    focal: [ros-humble-ament-cpplint]
    jammy: [ros-humble-ament-cpplint]
ament_download:
  ubuntu:
    focal: [ros-humble-ament-download]
    jammy: [ros-humble-ament-download]
ament_flake8:
  ubuntu:
    focal: [ros-humble-ament-flake8]
    jammy: [ros-humble-ament-flake8]
ament_index_cpp:
  ubuntu:
    focal: [ros-humble-ament-index-cpp]
    jammy: [ros-humble-ament-index-cpp]
ament_index_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-ament-index-cpp-dbgsym]
    jammy: [ros-humble-ament-index-cpp-dbgsym]
ament_index_python:
  ubuntu:
    focal: [ros-humble-ament-index-python]
    jammy: [ros-humble-ament-index-python]
ament_lint:
  ubuntu:
    focal: [ros-humble-ament-lint]
    jammy: [ros-humble-ament-lint]
ament_lint_auto:
  ubuntu:
    focal: [ros-humble-ament-lint-auto]
    jammy: [ros-humble-ament-lint-auto]
ament_lint_cmake:
  ubuntu:
    focal: [ros-humble-ament-lint-cmake]
    jammy: [ros-humble-ament-lint-cmake]
ament_lint_common:
  ubuntu:
    focal: [ros-humble-ament-lint-common]
    jammy: [ros-humble-ament-lint-common]
ament_mypy:
  ubuntu:
    focal: [ros-humble-ament-mypy]
    jammy: [ros-humble-ament-mypy]
ament_nodl:
  ubuntu:
    focal: [ros-humble-ament-nodl]
    jammy: [ros-humble-ament-nodl]
ament_package:
  ubuntu:
    focal: [ros-humble-ament-package]
    jammy: [ros-humble-ament-package]
ament_pclint:
  ubuntu:
    focal: [ros-humble-ament-pclint]
    jammy: [ros-humble-ament-pclint]
ament_pep257:
  ubuntu:
    focal: [ros-humble-ament-pep257]
    jammy: [ros-humble-ament-pep257]
ament_pycodestyle:
  ubuntu:
    focal: [ros-humble-ament-pycodestyle]
    jammy: [ros-humble-ament-pycodestyle]
ament_pyflakes:
  ubuntu:
    focal: [ros-humble-ament-pyflakes]
    jammy: [ros-humble-ament-pyflakes]
ament_python:
  ubuntu: []
ament_uncrustify:
  ubuntu:
    focal: [ros-humble-ament-uncrustify]
    jammy: [ros-humble-ament-uncrustify]
ament_vitis:
  ubuntu:
    focal: [ros-humble-ament-vitis]
    jammy: [ros-humble-ament-vitis]
ament_xmllint:
  ubuntu:
    focal: [ros-humble-ament-xmllint]
    jammy: [ros-humble-ament-xmllint]
angles:
  ubuntu:
    focal: [ros-humble-angles]
    jammy: [ros-humble-angles]
apex_containers:
  ubuntu:
    focal: [ros-humble-apex-containers]
    jammy: [ros-humble-apex-containers]
apex_containers_dbgsym:
  ubuntu:
    focal: [ros-humble-apex-containers-dbgsym]
    jammy: [ros-humble-apex-containers-dbgsym]
apex_test_tools:
  ubuntu:
    focal: [ros-humble-apex-test-tools]
    jammy: [ros-humble-apex-test-tools]
apriltag:
  ubuntu:
    focal: [ros-humble-apriltag]
    jammy: [ros-humble-apriltag]
apriltag_dbgsym:
  ubuntu:
    focal: [ros-humble-apriltag-dbgsym]
    jammy: [ros-humble-apriltag-dbgsym]
apriltag_msgs:
  ubuntu:
    focal: [ros-humble-apriltag-msgs]
    jammy: [ros-humble-apriltag-msgs]
apriltag_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-apriltag-msgs-dbgsym]
    jammy: [ros-humble-apriltag-msgs-dbgsym]
apriltag_ros:
  ubuntu:
    focal: [ros-humble-apriltag-ros]
    jammy: [ros-humble-apriltag-ros]
apriltag_ros_dbgsym:
  ubuntu:
    focal: [ros-humble-apriltag-ros-dbgsym]
    jammy: [ros-humble-apriltag-ros-dbgsym]
aruco:
  ubuntu:
    focal: [ros-humble-aruco]
    jammy: [ros-humble-aruco]
aruco_dbgsym:
  ubuntu:
    focal: [ros-humble-aruco-dbgsym]
    jammy: [ros-humble-aruco-dbgsym]
aruco_msgs:
  ubuntu:
    focal: [ros-humble-aruco-msgs]
    jammy: [ros-humble-aruco-msgs]
aruco_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-aruco-msgs-dbgsym]
    jammy: [ros-humble-aruco-msgs-dbgsym]
aruco_opencv:
  ubuntu:
    focal: [ros-humble-aruco-opencv]
    jammy: [ros-humble-aruco-opencv]
aruco_opencv_dbgsym:
  ubuntu:
    focal: [ros-humble-aruco-opencv-dbgsym]
    jammy: [ros-humble-aruco-opencv-dbgsym]
aruco_opencv_msgs:
  ubuntu:
    focal: [ros-humble-aruco-opencv-msgs]
    jammy: [ros-humble-aruco-opencv-msgs]
aruco_opencv_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-aruco-opencv-msgs-dbgsym]
    jammy: [ros-humble-aruco-opencv-msgs-dbgsym]
aruco_ros:
  ubuntu:
    focal: [ros-humble-aruco-ros]
    jammy: [ros-humble-aruco-ros]
aruco_ros_dbgsym:
  ubuntu:
    focal: [ros-humble-aruco-ros-dbgsym]
    jammy: [ros-humble-aruco-ros-dbgsym]
as2_alphanumeric_viewer:
  ubuntu:
    focal: [ros-humble-as2-alphanumeric-viewer]
    jammy: [ros-humble-as2-alphanumeric-viewer]
as2_alphanumeric_viewer_dbgsym:
  ubuntu:
    focal: [ros-humble-as2-alphanumeric-viewer-dbgsym]
    jammy: [ros-humble-as2-alphanumeric-viewer-dbgsym]
as2_behavior:
  ubuntu:
    focal: [ros-humble-as2-behavior]
    jammy: [ros-humble-as2-behavior]
as2_behavior_dbgsym:
  ubuntu:
    focal: [ros-humble-as2-behavior-dbgsym]
    jammy: [ros-humble-as2-behavior-dbgsym]
as2_behavior_tree:
  ubuntu:
    focal: [ros-humble-as2-behavior-tree]
    jammy: [ros-humble-as2-behavior-tree]
as2_behavior_tree_dbgsym:
  ubuntu:
    focal: [ros-humble-as2-behavior-tree-dbgsym]
    jammy: [ros-humble-as2-behavior-tree-dbgsym]
as2_behaviors_motion:
  ubuntu:
    focal: [ros-humble-as2-behaviors-motion]
    jammy: [ros-humble-as2-behaviors-motion]
as2_behaviors_motion_dbgsym:
  ubuntu:
    focal: [ros-humble-as2-behaviors-motion-dbgsym]
    jammy: [ros-humble-as2-behaviors-motion-dbgsym]
as2_behaviors_perception:
  ubuntu:
    focal: [ros-humble-as2-behaviors-perception]
    jammy: [ros-humble-as2-behaviors-perception]
as2_behaviors_perception_dbgsym:
  ubuntu:
    focal: [ros-humble-as2-behaviors-perception-dbgsym]
    jammy: [ros-humble-as2-behaviors-perception-dbgsym]
as2_behaviors_platform:
  ubuntu:
    focal: [ros-humble-as2-behaviors-platform]
    jammy: [ros-humble-as2-behaviors-platform]
as2_behaviors_platform_dbgsym:
  ubuntu:
    focal: [ros-humble-as2-behaviors-platform-dbgsym]
    jammy: [ros-humble-as2-behaviors-platform-dbgsym]
as2_behaviors_trajectory_generation:
  ubuntu:
    focal: [ros-humble-as2-behaviors-trajectory-generation]
    jammy: [ros-humble-as2-behaviors-trajectory-generation]
as2_behaviors_trajectory_generation_dbgsym:
  ubuntu:
    focal: [ros-humble-as2-behaviors-trajectory-generation-dbgsym]
    jammy: [ros-humble-as2-behaviors-trajectory-generation-dbgsym]
as2_cli:
  ubuntu:
    focal: [ros-humble-as2-cli]
    jammy: [ros-humble-as2-cli]
as2_core:
  ubuntu:
    focal: [ros-humble-as2-core]
    jammy: [ros-humble-as2-core]
as2_gazebo_classic_assets:
  ubuntu:
    focal: [ros-humble-as2-gazebo-classic-assets]
    jammy: [ros-humble-as2-gazebo-classic-assets]
as2_ign_gazebo_assets:
  ubuntu:
    focal: [ros-humble-as2-ign-gazebo-assets]
    jammy: [ros-humble-as2-ign-gazebo-assets]
as2_ign_gazebo_assets_dbgsym:
  ubuntu:
    focal: [ros-humble-as2-ign-gazebo-assets-dbgsym]
    jammy: [ros-humble-as2-ign-gazebo-assets-dbgsym]
as2_motion_controller:
  ubuntu:
    focal: [ros-humble-as2-motion-controller]
    jammy: [ros-humble-as2-motion-controller]
as2_motion_controller_dbgsym:
  ubuntu:
    focal: [ros-humble-as2-motion-controller-dbgsym]
    jammy: [ros-humble-as2-motion-controller-dbgsym]
as2_motion_reference_handlers:
  ubuntu:
    focal: [ros-humble-as2-motion-reference-handlers]
    jammy: [ros-humble-as2-motion-reference-handlers]
as2_msgs:
  ubuntu:
    focal: [ros-humble-as2-msgs]
    jammy: [ros-humble-as2-msgs]
as2_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-as2-msgs-dbgsym]
    jammy: [ros-humble-as2-msgs-dbgsym]
as2_platform_crazyflie:
  ubuntu:
    focal: [ros-humble-as2-platform-crazyflie]
    jammy: [ros-humble-as2-platform-crazyflie]
as2_platform_crazyflie_dbgsym:
  ubuntu:
    focal: [ros-humble-as2-platform-crazyflie-dbgsym]
    jammy: [ros-humble-as2-platform-crazyflie-dbgsym]
as2_platform_ign_gazebo:
  ubuntu:
    focal: [ros-humble-as2-platform-ign-gazebo]
    jammy: [ros-humble-as2-platform-ign-gazebo]
as2_platform_ign_gazebo_dbgsym:
  ubuntu:
    focal: [ros-humble-as2-platform-ign-gazebo-dbgsym]
    jammy: [ros-humble-as2-platform-ign-gazebo-dbgsym]
as2_platform_tello:
  ubuntu:
    focal: [ros-humble-as2-platform-tello]
    jammy: [ros-humble-as2-platform-tello]
as2_platform_tello_dbgsym:
  ubuntu:
    focal: [ros-humble-as2-platform-tello-dbgsym]
    jammy: [ros-humble-as2-platform-tello-dbgsym]
as2_realsense_interface:
  ubuntu:
    focal: [ros-humble-as2-realsense-interface]
    jammy: [ros-humble-as2-realsense-interface]
as2_realsense_interface_dbgsym:
  ubuntu:
    focal: [ros-humble-as2-realsense-interface-dbgsym]
    jammy: [ros-humble-as2-realsense-interface-dbgsym]
as2_state_estimator:
  ubuntu:
    focal: [ros-humble-as2-state-estimator]
    jammy: [ros-humble-as2-state-estimator]
as2_state_estimator_dbgsym:
  ubuntu:
    focal: [ros-humble-as2-state-estimator-dbgsym]
    jammy: [ros-humble-as2-state-estimator-dbgsym]
as2_usb_camera_interface:
  ubuntu:
    focal: [ros-humble-as2-usb-camera-interface]
    jammy: [ros-humble-as2-usb-camera-interface]
as2_usb_camera_interface_dbgsym:
  ubuntu:
    focal: [ros-humble-as2-usb-camera-interface-dbgsym]
    jammy: [ros-humble-as2-usb-camera-interface-dbgsym]
asio_cmake_module:
  ubuntu:
    focal: [ros-humble-asio-cmake-module]
    jammy: [ros-humble-asio-cmake-module]
async_web_server_cpp:
  ubuntu:
    focal: [ros-humble-async-web-server-cpp]
    jammy: [ros-humble-async-web-server-cpp]
async_web_server_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-async-web-server-cpp-dbgsym]
    jammy: [ros-humble-async-web-server-cpp-dbgsym]
automotive_autonomy_msgs:
  ubuntu:
    focal: [ros-humble-automotive-autonomy-msgs]
    jammy: [ros-humble-automotive-autonomy-msgs]
automotive_navigation_msgs:
  ubuntu:
    focal: [ros-humble-automotive-navigation-msgs]
    jammy: [ros-humble-automotive-navigation-msgs]
automotive_navigation_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-automotive-navigation-msgs-dbgsym]
    jammy: [ros-humble-automotive-navigation-msgs-dbgsym]
automotive_platform_msgs:
  ubuntu:
    focal: [ros-humble-automotive-platform-msgs]
    jammy: [ros-humble-automotive-platform-msgs]
automotive_platform_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-automotive-platform-msgs-dbgsym]
    jammy: [ros-humble-automotive-platform-msgs-dbgsym]
autoware_auto_msgs:
  ubuntu:
    focal: [ros-humble-autoware-auto-msgs]
    jammy: [ros-humble-autoware-auto-msgs]
autoware_auto_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-autoware-auto-msgs-dbgsym]
    jammy: [ros-humble-autoware-auto-msgs-dbgsym]
avt_vimba_camera:
  ubuntu:
    focal: [ros-humble-avt-vimba-camera]
    jammy: [ros-humble-avt-vimba-camera]
avt_vimba_camera_dbgsym:
  ubuntu:
    focal: [ros-humble-avt-vimba-camera-dbgsym]
    jammy: [ros-humble-avt-vimba-camera-dbgsym]
aws_sdk_cpp_vendor:
  ubuntu:
    focal: [ros-humble-aws-sdk-cpp-vendor]
    jammy: [ros-humble-aws-sdk-cpp-vendor]
aws_sdk_cpp_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-aws-sdk-cpp-vendor-dbgsym]
    jammy: [ros-humble-aws-sdk-cpp-vendor-dbgsym]
backward_ros:
  ubuntu:
    focal: [ros-humble-backward-ros]
    jammy: [ros-humble-backward-ros]
backward_ros_dbgsym:
  ubuntu:
    focal: [ros-humble-backward-ros-dbgsym]
    jammy: [ros-humble-backward-ros-dbgsym]
bag2_to_image:
  ubuntu:
    focal: [ros-humble-bag2-to-image]
    jammy: [ros-humble-bag2-to-image]
bag2_to_image_dbgsym:
  ubuntu:
    focal: [ros-humble-bag2-to-image-dbgsym]
    jammy: [ros-humble-bag2-to-image-dbgsym]
base2d_kinematics:
  ubuntu:
    focal: [ros-humble-base2d-kinematics]
    jammy: [ros-humble-base2d-kinematics]
base2d_kinematics_dbgsym:
  ubuntu:
    focal: [ros-humble-base2d-kinematics-dbgsym]
    jammy: [ros-humble-base2d-kinematics-dbgsym]
base2d_kinematics_msgs:
  ubuntu:
    focal: [ros-humble-base2d-kinematics-msgs]
    jammy: [ros-humble-base2d-kinematics-msgs]
base2d_kinematics_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-base2d-kinematics-msgs-dbgsym]
    jammy: [ros-humble-base2d-kinematics-msgs-dbgsym]
behaviortree_cpp:
  ubuntu:
    focal: [ros-humble-behaviortree-cpp]
    jammy: [ros-humble-behaviortree-cpp]
behaviortree_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-behaviortree-cpp-dbgsym]
    jammy: [ros-humble-behaviortree-cpp-dbgsym]
behaviortree_cpp_v3:
  ubuntu:
    focal: [ros-humble-behaviortree-cpp-v3]
    jammy: [ros-humble-behaviortree-cpp-v3]
behaviortree_cpp_v3_dbgsym:
  ubuntu:
    focal: [ros-humble-behaviortree-cpp-v3-dbgsym]
    jammy: [ros-humble-behaviortree-cpp-v3-dbgsym]
bicycle_steering_controller:
  ubuntu:
    focal: [ros-humble-bicycle-steering-controller]
    jammy: [ros-humble-bicycle-steering-controller]
bicycle_steering_controller_dbgsym:
  ubuntu:
    focal: [ros-humble-bicycle-steering-controller-dbgsym]
    jammy: [ros-humble-bicycle-steering-controller-dbgsym]
bno055:
  ubuntu:
    focal: [ros-humble-bno055]
    jammy: [ros-humble-bno055]
bond:
  ubuntu:
    focal: [ros-humble-bond]
    jammy: [ros-humble-bond]
bond_core:
  ubuntu:
    focal: [ros-humble-bond-core]
    jammy: [ros-humble-bond-core]
bond_dbgsym:
  ubuntu:
    focal: [ros-humble-bond-dbgsym]
    jammy: [ros-humble-bond-dbgsym]
bondcpp:
  ubuntu:
    focal: [ros-humble-bondcpp]
    jammy: [ros-humble-bondcpp]
bondcpp_dbgsym:
  ubuntu:
    focal: [ros-humble-bondcpp-dbgsym]
    jammy: [ros-humble-bondcpp-dbgsym]
boost_geometry_util:
  ubuntu:
    focal: [ros-humble-boost-geometry-util]
    jammy: [ros-humble-boost-geometry-util]
boost_geometry_util_dbgsym:
  ubuntu:
    focal: [ros-humble-boost-geometry-util-dbgsym]
    jammy: [ros-humble-boost-geometry-util-dbgsym]
boost_plugin_loader:
  ubuntu:
    focal: [ros-humble-boost-plugin-loader]
    jammy: [ros-humble-boost-plugin-loader]
boost_plugin_loader_dbgsym:
  ubuntu:
    focal: [ros-humble-boost-plugin-loader-dbgsym]
    jammy: [ros-humble-boost-plugin-loader-dbgsym]
builtin_interfaces:
  ubuntu:
    focal: [ros-humble-builtin-interfaces]
    jammy: [ros-humble-builtin-interfaces]
builtin_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-builtin-interfaces-dbgsym]
    jammy: [ros-humble-builtin-interfaces-dbgsym]
camera_calibration:
  ubuntu:
    focal: [ros-humble-camera-calibration]
    jammy: [ros-humble-camera-calibration]
camera_calibration_parsers:
  ubuntu:
    focal: [ros-humble-camera-calibration-parsers]
    jammy: [ros-humble-camera-calibration-parsers]
camera_calibration_parsers_dbgsym:
  ubuntu:
    focal: [ros-humble-camera-calibration-parsers-dbgsym]
    jammy: [ros-humble-camera-calibration-parsers-dbgsym]
camera_info_manager:
  ubuntu:
    focal: [ros-humble-camera-info-manager]
    jammy: [ros-humble-camera-info-manager]
camera_info_manager_dbgsym:
  ubuntu:
    focal: [ros-humble-camera-info-manager-dbgsym]
    jammy: [ros-humble-camera-info-manager-dbgsym]
camera_ros:
  ubuntu:
    focal: [ros-humble-camera-ros]
    jammy: [ros-humble-camera-ros]
camera_ros_dbgsym:
  ubuntu:
    focal: [ros-humble-camera-ros-dbgsym]
    jammy: [ros-humble-camera-ros-dbgsym]
can_msgs:
  ubuntu:
    focal: [ros-humble-can-msgs]
    jammy: [ros-humble-can-msgs]
can_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-can-msgs-dbgsym]
    jammy: [ros-humble-can-msgs-dbgsym]
can_utils:
  ubuntu:
    focal: [can-utils]
    jammy: [can-utils]
carter_navigation:
  ubuntu:
    focal: [ros-humble-carter-navigation]
    jammy: [ros-humble-carter-navigation]
cartographer:
  ubuntu:
    focal: [ros-humble-cartographer]
    jammy: [ros-humble-cartographer]
cartographer_dbgsym:
  ubuntu:
    focal: [ros-humble-cartographer-dbgsym]
    jammy: [ros-humble-cartographer-dbgsym]
cartographer_ros:
  ubuntu:
    focal: [ros-humble-cartographer-ros]
    jammy: [ros-humble-cartographer-ros]
cartographer_ros_dbgsym:
  ubuntu:
    focal: [ros-humble-cartographer-ros-dbgsym]
    jammy: [ros-humble-cartographer-ros-dbgsym]
cartographer_ros_msgs:
  ubuntu:
    focal: [ros-humble-cartographer-ros-msgs]
    jammy: [ros-humble-cartographer-ros-msgs]
cartographer_ros_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-cartographer-ros-msgs-dbgsym]
    jammy: [ros-humble-cartographer-ros-msgs-dbgsym]
cartographer_rviz:
  ubuntu:
    focal: [ros-humble-cartographer-rviz]
    jammy: [ros-humble-cartographer-rviz]
cartographer_rviz_dbgsym:
  ubuntu:
    focal: [ros-humble-cartographer-rviz-dbgsym]
    jammy: [ros-humble-cartographer-rviz-dbgsym]
cascade_lifecycle_msgs:
  ubuntu:
    focal: [ros-humble-cascade-lifecycle-msgs]
    jammy: [ros-humble-cascade-lifecycle-msgs]
cascade_lifecycle_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-cascade-lifecycle-msgs-dbgsym]
    jammy: [ros-humble-cascade-lifecycle-msgs-dbgsym]
catch_ros2:
  ubuntu:
    focal: [ros-humble-catch-ros2]
    jammy: [ros-humble-catch-ros2]
catch_ros2_dbgsym:
  ubuntu:
    focal: [ros-humble-catch-ros2-dbgsym]
    jammy: [ros-humble-catch-ros2-dbgsym]
catkin:
  ubuntu:
    focal: [ros-noetic-catkin]
    jammy: [ros-noetic-catkin]
chomp_motion_planner:
  ubuntu:
    focal: [ros-humble-chomp-motion-planner]
    jammy: [ros-humble-chomp-motion-planner]
chomp_motion_planner_dbgsym:
  ubuntu:
    focal: [ros-humble-chomp-motion-planner-dbgsym]
    jammy: [ros-humble-chomp-motion-planner-dbgsym]
class_loader:
  ubuntu:
    focal: [ros-humble-class-loader]
    jammy: [ros-humble-class-loader]
class_loader_dbgsym:
  ubuntu:
    focal: [ros-humble-class-loader-dbgsym]
    jammy: [ros-humble-class-loader-dbgsym]
classic_bags:
  ubuntu:
    focal: [ros-humble-classic-bags]
    jammy: [ros-humble-classic-bags]
clearpath_common:
  ubuntu:
    focal: [ros-humble-clearpath-common]
    jammy: [ros-humble-clearpath-common]
clearpath_config:
  ubuntu:
    focal: [ros-humble-clearpath-config]
    jammy: [ros-humble-clearpath-config]
clearpath_config_live:
  ubuntu:
    focal: [ros-humble-clearpath-config-live]
    jammy: [ros-humble-clearpath-config-live]
clearpath_control:
  ubuntu:
    focal: [ros-humble-clearpath-control]
    jammy: [ros-humble-clearpath-control]
clearpath_description:
  ubuntu:
    focal: [ros-humble-clearpath-description]
    jammy: [ros-humble-clearpath-description]
clearpath_desktop:
  ubuntu:
    focal: [ros-humble-clearpath-desktop]
    jammy: [ros-humble-clearpath-desktop]
clearpath_generator_common:
  ubuntu:
    focal: [ros-humble-clearpath-generator-common]
    jammy: [ros-humble-clearpath-generator-common]
clearpath_generator_gz:
  ubuntu:
    focal: [ros-humble-clearpath-generator-gz]
    jammy: [ros-humble-clearpath-generator-gz]
clearpath_gz:
  ubuntu:
    focal: [ros-humble-clearpath-gz]
    jammy: [ros-humble-clearpath-gz]
clearpath_mounts_description:
  ubuntu:
    focal: [ros-humble-clearpath-mounts-description]
    jammy: [ros-humble-clearpath-mounts-description]
clearpath_msgs:
  ubuntu:
    focal: [ros-humble-clearpath-msgs]
    jammy: [ros-humble-clearpath-msgs]
clearpath_nav2_demos:
  ubuntu:
    focal: [ros-humble-clearpath-nav2-demos]
    jammy: [ros-humble-clearpath-nav2-demos]
clearpath_platform:
  ubuntu:
    focal: [ros-humble-clearpath-platform]
    jammy: [ros-humble-clearpath-platform]
clearpath_platform_dbgsym:
  ubuntu:
    focal: [ros-humble-clearpath-platform-dbgsym]
    jammy: [ros-humble-clearpath-platform-dbgsym]
clearpath_platform_description:
  ubuntu:
    focal: [ros-humble-clearpath-platform-description]
    jammy: [ros-humble-clearpath-platform-description]
clearpath_platform_msgs:
  ubuntu:
    focal: [ros-humble-clearpath-platform-msgs]
    jammy: [ros-humble-clearpath-platform-msgs]
clearpath_platform_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-clearpath-platform-msgs-dbgsym]
    jammy: [ros-humble-clearpath-platform-msgs-dbgsym]
clearpath_sensors_description:
  ubuntu:
    focal: [ros-humble-clearpath-sensors-description]
    jammy: [ros-humble-clearpath-sensors-description]
clearpath_simulator:
  ubuntu:
    focal: [ros-humble-clearpath-simulator]
    jammy: [ros-humble-clearpath-simulator]
clearpath_viz:
  ubuntu:
    focal: [ros-humble-clearpath-viz]
    jammy: [ros-humble-clearpath-viz]
color_names:
  ubuntu:
    focal: [ros-humble-color-names]
    jammy: [ros-humble-color-names]
color_names_dbgsym:
  ubuntu:
    focal: [ros-humble-color-names-dbgsym]
    jammy: [ros-humble-color-names-dbgsym]
color_util:
  ubuntu:
    focal: [ros-humble-color-util]
    jammy: [ros-humble-color-util]
commander:
  ubuntu:
    focal: [ros-humble-commander]
    jammy: [ros-humble-commander]
common_interfaces:
  ubuntu:
    focal: [ros-humble-common-interfaces]
    jammy: [ros-humble-common-interfaces]
composition:
  ubuntu:
    focal: [ros-humble-composition]
    jammy: [ros-humble-composition]
composition_dbgsym:
  ubuntu:
    focal: [ros-humble-composition-dbgsym]
    jammy: [ros-humble-composition-dbgsym]
composition_interfaces:
  ubuntu:
    focal: [ros-humble-composition-interfaces]
    jammy: [ros-humble-composition-interfaces]
composition_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-composition-interfaces-dbgsym]
    jammy: [ros-humble-composition-interfaces-dbgsym]
compressed_depth_image_transport:
  ubuntu:
    focal: [ros-humble-compressed-depth-image-transport]
    jammy: [ros-humble-compressed-depth-image-transport]
compressed_depth_image_transport_dbgsym:
  ubuntu:
    focal: [ros-humble-compressed-depth-image-transport-dbgsym]
    jammy: [ros-humble-compressed-depth-image-transport-dbgsym]
compressed_image_transport:
  ubuntu:
    focal: [ros-humble-compressed-image-transport]
    jammy: [ros-humble-compressed-image-transport]
compressed_image_transport_dbgsym:
  ubuntu:
    focal: [ros-humble-compressed-image-transport-dbgsym]
    jammy: [ros-humble-compressed-image-transport-dbgsym]
console_bridge_vendor:
  ubuntu:
    focal: [ros-humble-console-bridge-vendor]
    jammy: [ros-humble-console-bridge-vendor]
console_bridge_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-console-bridge-vendor-dbgsym]
    jammy: [ros-humble-console-bridge-vendor-dbgsym]
control_box_rst:
  ubuntu:
    focal: [ros-humble-control-box-rst]
    jammy: [ros-humble-control-box-rst]
control_msgs:
  ubuntu:
    focal: [ros-humble-control-msgs]
    jammy: [ros-humble-control-msgs]
control_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-control-msgs-dbgsym]
    jammy: [ros-humble-control-msgs-dbgsym]
control_toolbox:
  ubuntu:
    focal: [ros-humble-control-toolbox]
    jammy: [ros-humble-control-toolbox]
control_toolbox_dbgsym:
  ubuntu:
    focal: [ros-humble-control-toolbox-dbgsym]
    jammy: [ros-humble-control-toolbox-dbgsym]
controller_interface:
  ubuntu:
    focal: [ros-humble-controller-interface]
    jammy: [ros-humble-controller-interface]
controller_interface_dbgsym:
  ubuntu:
    focal: [ros-humble-controller-interface-dbgsym]
    jammy: [ros-humble-controller-interface-dbgsym]
controller_manager:
  ubuntu:
    focal: [ros-humble-controller-manager]
    jammy: [ros-humble-controller-manager]
controller_manager_dbgsym:
  ubuntu:
    focal: [ros-humble-controller-manager-dbgsym]
    jammy: [ros-humble-controller-manager-dbgsym]
controller_manager_msgs:
  ubuntu:
    focal: [ros-humble-controller-manager-msgs]
    jammy: [ros-humble-controller-manager-msgs]
controller_manager_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-controller-manager-msgs-dbgsym]
    jammy: [ros-humble-controller-manager-msgs-dbgsym]
costmap_queue:
  ubuntu:
    focal: [ros-humble-costmap-queue]
    jammy: [ros-humble-costmap-queue]
crane_plus_moveit_config:
  ubuntu:
    focal: [ros-humble-crane-plus-moveit-config]
    jammy: [ros-humble-crane-plus-moveit-config]
create_bringup:
  ubuntu:
    focal: [ros-humble-create-bringup]
    jammy: [ros-humble-create-bringup]
create_description:
  ubuntu:
    focal: [ros-humble-create-description]
    jammy: [ros-humble-create-description]
create_driver:
  ubuntu:
    focal: [ros-humble-create-driver]
    jammy: [ros-humble-create-driver]
create_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-create-driver-dbgsym]
    jammy: [ros-humble-create-driver-dbgsym]
create_msgs:
  ubuntu:
    focal: [ros-humble-create-msgs]
    jammy: [ros-humble-create-msgs]
create_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-create-msgs-dbgsym]
    jammy: [ros-humble-create-msgs-dbgsym]
create_robot:
  ubuntu:
    focal: [ros-humble-create-robot]
    jammy: [ros-humble-create-robot]
cuda_python:
  ubuntu:
    focal: [ros-humble-cuda-python-placeholder]
    jammy: [ros-humble-cuda-python-placeholder]
cudnn_cmake_module:
  ubuntu:
    focal: [ros-humble-cudnn-cmake-module]
    jammy: [ros-humble-cudnn-cmake-module]
curobo_core:
  ubuntu:
    focal: [ros-humble-curobo-core]
    jammy: [ros-humble-curobo-core]
custom_nitros_dnn_image_encoder:
  ubuntu:
    focal: [ros-humble-custom-nitros-dnn-image-encoder]
    jammy: [ros-humble-custom-nitros-dnn-image-encoder]
custom_nitros_image:
  ubuntu:
    focal: [ros-humble-custom-nitros-image]
    jammy: [ros-humble-custom-nitros-image]
custom_nitros_message_filter:
  ubuntu:
    focal: [ros-humble-custom-nitros-message-filter]
    jammy: [ros-humble-custom-nitros-message-filter]
custom_nitros_message_filter_interfaces:
  ubuntu:
    focal: [ros-humble-custom-nitros-message-filter-interfaces]
    jammy: [ros-humble-custom-nitros-message-filter-interfaces]
custom_nitros_string:
  ubuntu:
    focal: [ros-humble-custom-nitros-string]
    jammy: [ros-humble-custom-nitros-string]
cv_bridge:
  ubuntu:
    focal: [ros-humble-cv-bridge]
    jammy: [ros-humble-cv-bridge]
cv_bridge_dbgsym:
  ubuntu:
    focal: [ros-humble-cv-bridge-dbgsym]
    jammy: [ros-humble-cv-bridge-dbgsym]
cyclonedds:
  ubuntu:
    focal: [ros-humble-cyclonedds]
    jammy: [ros-humble-cyclonedds]
cyclonedds_dbgsym:
  ubuntu:
    focal: [ros-humble-cyclonedds-dbgsym]
    jammy: [ros-humble-cyclonedds-dbgsym]
dataspeed_can:
  ubuntu:
    focal: [ros-humble-dataspeed-can]
    jammy: [ros-humble-dataspeed-can]
dataspeed_can_msg_filters:
  ubuntu:
    focal: [ros-humble-dataspeed-can-msg-filters]
    jammy: [ros-humble-dataspeed-can-msg-filters]
dataspeed_can_usb:
  ubuntu:
    focal: [ros-humble-dataspeed-can-usb]
    jammy: [ros-humble-dataspeed-can-usb]
dataspeed_can_usb_dbgsym:
  ubuntu:
    focal: [ros-humble-dataspeed-can-usb-dbgsym]
    jammy: [ros-humble-dataspeed-can-usb-dbgsym]
dataspeed_dbw_common:
  ubuntu:
    focal: [ros-humble-dataspeed-dbw-common]
    jammy: [ros-humble-dataspeed-dbw-common]
dataspeed_dbw_gateway:
  ubuntu:
    focal: [ros-humble-dataspeed-dbw-gateway]
    jammy: [ros-humble-dataspeed-dbw-gateway]
dataspeed_dbw_gateway_dbgsym:
  ubuntu:
    focal: [ros-humble-dataspeed-dbw-gateway-dbgsym]
    jammy: [ros-humble-dataspeed-dbw-gateway-dbgsym]
dataspeed_dbw_msgs:
  ubuntu:
    focal: [ros-humble-dataspeed-dbw-msgs]
    jammy: [ros-humble-dataspeed-dbw-msgs]
dataspeed_dbw_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-dataspeed-dbw-msgs-dbgsym]
    jammy: [ros-humble-dataspeed-dbw-msgs-dbgsym]
dataspeed_ulc:
  ubuntu:
    focal: [ros-humble-dataspeed-ulc]
    jammy: [ros-humble-dataspeed-ulc]
dataspeed_ulc_can:
  ubuntu:
    focal: [ros-humble-dataspeed-ulc-can]
    jammy: [ros-humble-dataspeed-ulc-can]
dataspeed_ulc_can_dbgsym:
  ubuntu:
    focal: [ros-humble-dataspeed-ulc-can-dbgsym]
    jammy: [ros-humble-dataspeed-ulc-can-dbgsym]
dataspeed_ulc_msgs:
  ubuntu:
    focal: [ros-humble-dataspeed-ulc-msgs]
    jammy: [ros-humble-dataspeed-ulc-msgs]
dataspeed_ulc_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-dataspeed-ulc-msgs-dbgsym]
    jammy: [ros-humble-dataspeed-ulc-msgs-dbgsym]
dbw_fca:
  ubuntu:
    focal: [ros-humble-dbw-fca]
    jammy: [ros-humble-dbw-fca]
dbw_fca_can:
  ubuntu:
    focal: [ros-humble-dbw-fca-can]
    jammy: [ros-humble-dbw-fca-can]
dbw_fca_can_dbgsym:
  ubuntu:
    focal: [ros-humble-dbw-fca-can-dbgsym]
    jammy: [ros-humble-dbw-fca-can-dbgsym]
dbw_fca_description:
  ubuntu:
    focal: [ros-humble-dbw-fca-description]
    jammy: [ros-humble-dbw-fca-description]
dbw_fca_joystick_demo:
  ubuntu:
    focal: [ros-humble-dbw-fca-joystick-demo]
    jammy: [ros-humble-dbw-fca-joystick-demo]
dbw_fca_joystick_demo_dbgsym:
  ubuntu:
    focal: [ros-humble-dbw-fca-joystick-demo-dbgsym]
    jammy: [ros-humble-dbw-fca-joystick-demo-dbgsym]
dbw_fca_msgs:
  ubuntu:
    focal: [ros-humble-dbw-fca-msgs]
    jammy: [ros-humble-dbw-fca-msgs]
dbw_fca_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-dbw-fca-msgs-dbgsym]
    jammy: [ros-humble-dbw-fca-msgs-dbgsym]
dbw_ford:
  ubuntu:
    focal: [ros-humble-dbw-ford]
    jammy: [ros-humble-dbw-ford]
dbw_ford_can:
  ubuntu:
    focal: [ros-humble-dbw-ford-can]
    jammy: [ros-humble-dbw-ford-can]
dbw_ford_can_dbgsym:
  ubuntu:
    focal: [ros-humble-dbw-ford-can-dbgsym]
    jammy: [ros-humble-dbw-ford-can-dbgsym]
dbw_ford_description:
  ubuntu:
    focal: [ros-humble-dbw-ford-description]
    jammy: [ros-humble-dbw-ford-description]
dbw_ford_joystick_demo:
  ubuntu:
    focal: [ros-humble-dbw-ford-joystick-demo]
    jammy: [ros-humble-dbw-ford-joystick-demo]
dbw_ford_joystick_demo_dbgsym:
  ubuntu:
    focal: [ros-humble-dbw-ford-joystick-demo-dbgsym]
    jammy: [ros-humble-dbw-ford-joystick-demo-dbgsym]
dbw_ford_msgs:
  ubuntu:
    focal: [ros-humble-dbw-ford-msgs]
    jammy: [ros-humble-dbw-ford-msgs]
dbw_ford_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-dbw-ford-msgs-dbgsym]
    jammy: [ros-humble-dbw-ford-msgs-dbgsym]
dbw_polaris:
  ubuntu:
    focal: [ros-humble-dbw-polaris]
    jammy: [ros-humble-dbw-polaris]
dbw_polaris_can:
  ubuntu:
    focal: [ros-humble-dbw-polaris-can]
    jammy: [ros-humble-dbw-polaris-can]
dbw_polaris_can_dbgsym:
  ubuntu:
    focal: [ros-humble-dbw-polaris-can-dbgsym]
    jammy: [ros-humble-dbw-polaris-can-dbgsym]
dbw_polaris_description:
  ubuntu:
    focal: [ros-humble-dbw-polaris-description]
    jammy: [ros-humble-dbw-polaris-description]
dbw_polaris_joystick_demo:
  ubuntu:
    focal: [ros-humble-dbw-polaris-joystick-demo]
    jammy: [ros-humble-dbw-polaris-joystick-demo]
dbw_polaris_joystick_demo_dbgsym:
  ubuntu:
    focal: [ros-humble-dbw-polaris-joystick-demo-dbgsym]
    jammy: [ros-humble-dbw-polaris-joystick-demo-dbgsym]
dbw_polaris_msgs:
  ubuntu:
    focal: [ros-humble-dbw-polaris-msgs]
    jammy: [ros-humble-dbw-polaris-msgs]
dbw_polaris_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-dbw-polaris-msgs-dbgsym]
    jammy: [ros-humble-dbw-polaris-msgs-dbgsym]
delphi_esr_msgs:
  ubuntu:
    focal: [ros-humble-delphi-esr-msgs]
    jammy: [ros-humble-delphi-esr-msgs]
delphi_esr_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-delphi-esr-msgs-dbgsym]
    jammy: [ros-humble-delphi-esr-msgs-dbgsym]
delphi_mrr_msgs:
  ubuntu:
    focal: [ros-humble-delphi-mrr-msgs]
    jammy: [ros-humble-delphi-mrr-msgs]
delphi_mrr_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-delphi-mrr-msgs-dbgsym]
    jammy: [ros-humble-delphi-mrr-msgs-dbgsym]
delphi_srr_msgs:
  ubuntu:
    focal: [ros-humble-delphi-srr-msgs]
    jammy: [ros-humble-delphi-srr-msgs]
delphi_srr_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-delphi-srr-msgs-dbgsym]
    jammy: [ros-humble-delphi-srr-msgs-dbgsym]
demo_nodes_cpp:
  ubuntu:
    focal: [ros-humble-demo-nodes-cpp]
    jammy: [ros-humble-demo-nodes-cpp]
demo_nodes_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-demo-nodes-cpp-dbgsym]
    jammy: [ros-humble-demo-nodes-cpp-dbgsym]
demo_nodes_cpp_native:
  ubuntu:
    focal: [ros-humble-demo-nodes-cpp-native]
    jammy: [ros-humble-demo-nodes-cpp-native]
demo_nodes_cpp_native_dbgsym:
  ubuntu:
    focal: [ros-humble-demo-nodes-cpp-native-dbgsym]
    jammy: [ros-humble-demo-nodes-cpp-native-dbgsym]
demo_nodes_py:
  ubuntu:
    focal: [ros-humble-demo-nodes-py]
    jammy: [ros-humble-demo-nodes-py]
depth_image_proc:
  ubuntu:
    focal: [ros-humble-depth-image-proc]
    jammy: [ros-humble-depth-image-proc]
depth_image_proc_dbgsym:
  ubuntu:
    focal: [ros-humble-depth-image-proc-dbgsym]
    jammy: [ros-humble-depth-image-proc-dbgsym]
depthai:
  ubuntu:
    focal: [ros-humble-depthai]
    jammy: [ros-humble-depthai]
depthai_bridge:
  ubuntu:
    focal: [ros-humble-depthai-bridge]
    jammy: [ros-humble-depthai-bridge]
depthai_bridge_dbgsym:
  ubuntu:
    focal: [ros-humble-depthai-bridge-dbgsym]
    jammy: [ros-humble-depthai-bridge-dbgsym]
depthai_dbgsym:
  ubuntu:
    focal: [ros-humble-depthai-dbgsym]
    jammy: [ros-humble-depthai-dbgsym]
depthai_descriptions:
  ubuntu:
    focal: [ros-humble-depthai-descriptions]
    jammy: [ros-humble-depthai-descriptions]
depthai_examples:
  ubuntu:
    focal: [ros-humble-depthai-examples]
    jammy: [ros-humble-depthai-examples]
depthai_examples_dbgsym:
  ubuntu:
    focal: [ros-humble-depthai-examples-dbgsym]
    jammy: [ros-humble-depthai-examples-dbgsym]
depthai_filters:
  ubuntu:
    focal: [ros-humble-depthai-filters]
    jammy: [ros-humble-depthai-filters]
depthai_filters_dbgsym:
  ubuntu:
    focal: [ros-humble-depthai-filters-dbgsym]
    jammy: [ros-humble-depthai-filters-dbgsym]
depthai_ros:
  ubuntu:
    focal: [ros-humble-depthai-ros]
    jammy: [ros-humble-depthai-ros]
depthai_ros_driver:
  ubuntu:
    focal: [ros-humble-depthai-ros-driver]
    jammy: [ros-humble-depthai-ros-driver]
depthai_ros_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-depthai-ros-driver-dbgsym]
    jammy: [ros-humble-depthai-ros-driver-dbgsym]
depthai_ros_msgs:
  ubuntu:
    focal: [ros-humble-depthai-ros-msgs]
    jammy: [ros-humble-depthai-ros-msgs]
depthai_ros_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-depthai-ros-msgs-dbgsym]
    jammy: [ros-humble-depthai-ros-msgs-dbgsym]
depthimage_to_laserscan:
  ubuntu:
    focal: [ros-humble-depthimage-to-laserscan]
    jammy: [ros-humble-depthimage-to-laserscan]
depthimage_to_laserscan_dbgsym:
  ubuntu:
    focal: [ros-humble-depthimage-to-laserscan-dbgsym]
    jammy: [ros-humble-depthimage-to-laserscan-dbgsym]
derived_object_msgs:
  ubuntu:
    focal: [ros-humble-derived-object-msgs]
    jammy: [ros-humble-derived-object-msgs]
derived_object_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-derived-object-msgs-dbgsym]
    jammy: [ros-humble-derived-object-msgs-dbgsym]
desktop:
  ubuntu:
    focal: [ros-humble-desktop]
    jammy: [ros-humble-desktop]
desktop_full:
  ubuntu:
    focal: [ros-humble-desktop-full]
    jammy: [ros-humble-desktop-full]
diagnostic_aggregator:
  ubuntu:
    focal: [ros-humble-diagnostic-aggregator]
    jammy: [ros-humble-diagnostic-aggregator]
diagnostic_aggregator_dbgsym:
  ubuntu:
    focal: [ros-humble-diagnostic-aggregator-dbgsym]
    jammy: [ros-humble-diagnostic-aggregator-dbgsym]
diagnostic_common_diagnostics:
  ubuntu:
    focal: [ros-humble-diagnostic-common-diagnostics]
    jammy: [ros-humble-diagnostic-common-diagnostics]
diagnostic_msgs:
  ubuntu:
    focal: [ros-humble-diagnostic-msgs]
    jammy: [ros-humble-diagnostic-msgs]
diagnostic_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-diagnostic-msgs-dbgsym]
    jammy: [ros-humble-diagnostic-msgs-dbgsym]
diagnostic_updater:
  ubuntu:
    focal: [ros-humble-diagnostic-updater]
    jammy: [ros-humble-diagnostic-updater]
diagnostic_updater_dbgsym:
  ubuntu:
    focal: [ros-humble-diagnostic-updater-dbgsym]
    jammy: [ros-humble-diagnostic-updater-dbgsym]
diagnostics:
  ubuntu:
    focal: [ros-humble-diagnostics]
    jammy: [ros-humble-diagnostics]
diff_drive_controller:
  ubuntu:
    focal: [ros-humble-diff-drive-controller]
    jammy: [ros-humble-diff-drive-controller]
diff_drive_controller_dbgsym:
  ubuntu:
    focal: [ros-humble-diff-drive-controller-dbgsym]
    jammy: [ros-humble-diff-drive-controller-dbgsym]
dolly_follow:
  ubuntu:
    focal: [ros-humble-dolly-follow]
    jammy: [ros-humble-dolly-follow]
dolly_follow_dbgsym:
  ubuntu:
    focal: [ros-humble-dolly-follow-dbgsym]
    jammy: [ros-humble-dolly-follow-dbgsym]
dolly_ignition:
  ubuntu:
    focal: [ros-humble-dolly-ignition]
    jammy: [ros-humble-dolly-ignition]
domain_bridge:
  ubuntu:
    focal: [ros-humble-domain-bridge]
    jammy: [ros-humble-domain-bridge]
domain_bridge_dbgsym:
  ubuntu:
    focal: [ros-humble-domain-bridge-dbgsym]
    jammy: [ros-humble-domain-bridge-dbgsym]
domain_coordinator:
  ubuntu:
    focal: [ros-humble-domain-coordinator]
    jammy: [ros-humble-domain-coordinator]
dummy_map_server:
  ubuntu:
    focal: [ros-humble-dummy-map-server]
    jammy: [ros-humble-dummy-map-server]
dummy_map_server_dbgsym:
  ubuntu:
    focal: [ros-humble-dummy-map-server-dbgsym]
    jammy: [ros-humble-dummy-map-server-dbgsym]
dummy_robot_bringup:
  ubuntu:
    focal: [ros-humble-dummy-robot-bringup]
    jammy: [ros-humble-dummy-robot-bringup]
dummy_sensors:
  ubuntu:
    focal: [ros-humble-dummy-sensors]
    jammy: [ros-humble-dummy-sensors]
dummy_sensors_dbgsym:
  ubuntu:
    focal: [ros-humble-dummy-sensors-dbgsym]
    jammy: [ros-humble-dummy-sensors-dbgsym]
dwb_core:
  ubuntu:
    focal: [ros-humble-dwb-core]
    jammy: [ros-humble-dwb-core]
dwb_core_dbgsym:
  ubuntu:
    focal: [ros-humble-dwb-core-dbgsym]
    jammy: [ros-humble-dwb-core-dbgsym]
dwb_critics:
  ubuntu:
    focal: [ros-humble-dwb-critics]
    jammy: [ros-humble-dwb-critics]
dwb_critics_dbgsym:
  ubuntu:
    focal: [ros-humble-dwb-critics-dbgsym]
    jammy: [ros-humble-dwb-critics-dbgsym]
dwb_msgs:
  ubuntu:
    focal: [ros-humble-dwb-msgs]
    jammy: [ros-humble-dwb-msgs]
dwb_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-dwb-msgs-dbgsym]
    jammy: [ros-humble-dwb-msgs-dbgsym]
dwb_plugins:
  ubuntu:
    focal: [ros-humble-dwb-plugins]
    jammy: [ros-humble-dwb-plugins]
dwb_plugins_dbgsym:
  ubuntu:
    focal: [ros-humble-dwb-plugins-dbgsym]
    jammy: [ros-humble-dwb-plugins-dbgsym]
dynamic_edt_3d:
  ubuntu:
    focal: [ros-humble-dynamic-edt-3d]
    jammy: [ros-humble-dynamic-edt-3d]
dynamic_edt_3d_dbgsym:
  ubuntu:
    focal: [ros-humble-dynamic-edt-3d-dbgsym]
    jammy: [ros-humble-dynamic-edt-3d-dbgsym]
dynamixel_sdk:
  ubuntu:
    focal: [ros-humble-dynamixel-sdk]
    jammy: [ros-humble-dynamixel-sdk]
dynamixel_sdk_custom_interfaces:
  ubuntu:
    focal: [ros-humble-dynamixel-sdk-custom-interfaces]
    jammy: [ros-humble-dynamixel-sdk-custom-interfaces]
dynamixel_sdk_custom_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-dynamixel-sdk-custom-interfaces-dbgsym]
    jammy: [ros-humble-dynamixel-sdk-custom-interfaces-dbgsym]
dynamixel_sdk_dbgsym:
  ubuntu:
    focal: [ros-humble-dynamixel-sdk-dbgsym]
    jammy: [ros-humble-dynamixel-sdk-dbgsym]
dynamixel_sdk_examples:
  ubuntu:
    focal: [ros-humble-dynamixel-sdk-examples]
    jammy: [ros-humble-dynamixel-sdk-examples]
dynamixel_sdk_examples_dbgsym:
  ubuntu:
    focal: [ros-humble-dynamixel-sdk-examples-dbgsym]
    jammy: [ros-humble-dynamixel-sdk-examples-dbgsym]
dynamixel_workbench:
  ubuntu:
    focal: [ros-humble-dynamixel-workbench]
    jammy: [ros-humble-dynamixel-workbench]
dynamixel_workbench_msgs:
  ubuntu:
    focal: [ros-humble-dynamixel-workbench-msgs]
    jammy: [ros-humble-dynamixel-workbench-msgs]
dynamixel_workbench_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-dynamixel-workbench-msgs-dbgsym]
    jammy: [ros-humble-dynamixel-workbench-msgs-dbgsym]
dynamixel_workbench_toolbox:
  ubuntu:
    focal: [ros-humble-dynamixel-workbench-toolbox]
    jammy: [ros-humble-dynamixel-workbench-toolbox]
dynamixel_workbench_toolbox_dbgsym:
  ubuntu:
    focal: [ros-humble-dynamixel-workbench-toolbox-dbgsym]
    jammy: [ros-humble-dynamixel-workbench-toolbox-dbgsym]
ecal:
  ubuntu:
    focal: [ros-humble-ecal]
    jammy: [ros-humble-ecal]
ecal_dbgsym:
  ubuntu:
    focal: [ros-humble-ecal-dbgsym]
    jammy: [ros-humble-ecal-dbgsym]
ecl_build:
  ubuntu:
    focal: [ros-humble-ecl-build]
    jammy: [ros-humble-ecl-build]
ecl_license:
  ubuntu:
    focal: [ros-humble-ecl-license]
    jammy: [ros-humble-ecl-license]
ecl_tools:
  ubuntu:
    focal: [ros-humble-ecl-tools]
    jammy: [ros-humble-ecl-tools]
effort_controllers:
  ubuntu:
    focal: [ros-humble-effort-controllers]
    jammy: [ros-humble-effort-controllers]
effort_controllers_dbgsym:
  ubuntu:
    focal: [ros-humble-effort-controllers-dbgsym]
    jammy: [ros-humble-effort-controllers-dbgsym]
eigen3_cmake_module:
  ubuntu:
    focal: [ros-humble-eigen3-cmake-module]
    jammy: [ros-humble-eigen3-cmake-module]
eigen_stl_containers:
  ubuntu:
    focal: [ros-humble-eigen-stl-containers]
    jammy: [ros-humble-eigen-stl-containers]
eigenpy:
  ubuntu:
    focal: [ros-humble-eigenpy]
    jammy: [ros-humble-eigenpy]
eigenpy_dbgsym:
  ubuntu:
    focal: [ros-humble-eigenpy-dbgsym]
    jammy: [ros-humble-eigenpy-dbgsym]
evaluator:
  ubuntu:
    focal: [ros-humble-evaluator]
    jammy: [ros-humble-evaluator]
event_camera_codecs:
  ubuntu:
    focal: [ros-humble-event-camera-codecs]
    jammy: [ros-humble-event-camera-codecs]
event_camera_codecs_dbgsym:
  ubuntu:
    focal: [ros-humble-event-camera-codecs-dbgsym]
    jammy: [ros-humble-event-camera-codecs-dbgsym]
event_camera_msgs:
  ubuntu:
    focal: [ros-humble-event-camera-msgs]
    jammy: [ros-humble-event-camera-msgs]
event_camera_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-event-camera-msgs-dbgsym]
    jammy: [ros-humble-event-camera-msgs-dbgsym]
event_camera_py:
  ubuntu:
    focal: [ros-humble-event-camera-py]
    jammy: [ros-humble-event-camera-py]
event_camera_renderer:
  ubuntu:
    focal: [ros-humble-event-camera-renderer]
    jammy: [ros-humble-event-camera-renderer]
event_camera_renderer_dbgsym:
  ubuntu:
    focal: [ros-humble-event-camera-renderer-dbgsym]
    jammy: [ros-humble-event-camera-renderer-dbgsym]
example_interfaces:
  ubuntu:
    focal: [ros-humble-example-interfaces]
    jammy: [ros-humble-example-interfaces]
example_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-example-interfaces-dbgsym]
    jammy: [ros-humble-example-interfaces-dbgsym]
examples_rclcpp_async_client:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-async-client]
    jammy: [ros-humble-examples-rclcpp-async-client]
examples_rclcpp_async_client_dbgsym:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-async-client-dbgsym]
    jammy: [ros-humble-examples-rclcpp-async-client-dbgsym]
examples_rclcpp_cbg_executor:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-cbg-executor]
    jammy: [ros-humble-examples-rclcpp-cbg-executor]
examples_rclcpp_cbg_executor_dbgsym:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-cbg-executor-dbgsym]
    jammy: [ros-humble-examples-rclcpp-cbg-executor-dbgsym]
examples_rclcpp_minimal_action_client:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-minimal-action-client]
    jammy: [ros-humble-examples-rclcpp-minimal-action-client]
examples_rclcpp_minimal_action_client_dbgsym:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-minimal-action-client-dbgsym]
    jammy: [ros-humble-examples-rclcpp-minimal-action-client-dbgsym]
examples_rclcpp_minimal_action_server:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-minimal-action-server]
    jammy: [ros-humble-examples-rclcpp-minimal-action-server]
examples_rclcpp_minimal_action_server_dbgsym:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-minimal-action-server-dbgsym]
    jammy: [ros-humble-examples-rclcpp-minimal-action-server-dbgsym]
examples_rclcpp_minimal_client:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-minimal-client]
    jammy: [ros-humble-examples-rclcpp-minimal-client]
examples_rclcpp_minimal_client_dbgsym:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-minimal-client-dbgsym]
    jammy: [ros-humble-examples-rclcpp-minimal-client-dbgsym]
examples_rclcpp_minimal_composition:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-minimal-composition]
    jammy: [ros-humble-examples-rclcpp-minimal-composition]
examples_rclcpp_minimal_composition_dbgsym:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-minimal-composition-dbgsym]
    jammy: [ros-humble-examples-rclcpp-minimal-composition-dbgsym]
examples_rclcpp_minimal_publisher:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-minimal-publisher]
    jammy: [ros-humble-examples-rclcpp-minimal-publisher]
examples_rclcpp_minimal_publisher_dbgsym:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-minimal-publisher-dbgsym]
    jammy: [ros-humble-examples-rclcpp-minimal-publisher-dbgsym]
examples_rclcpp_minimal_service:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-minimal-service]
    jammy: [ros-humble-examples-rclcpp-minimal-service]
examples_rclcpp_minimal_service_dbgsym:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-minimal-service-dbgsym]
    jammy: [ros-humble-examples-rclcpp-minimal-service-dbgsym]
examples_rclcpp_minimal_subscriber:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-minimal-subscriber]
    jammy: [ros-humble-examples-rclcpp-minimal-subscriber]
examples_rclcpp_minimal_subscriber_dbgsym:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-minimal-subscriber-dbgsym]
    jammy: [ros-humble-examples-rclcpp-minimal-subscriber-dbgsym]
examples_rclcpp_minimal_timer:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-minimal-timer]
    jammy: [ros-humble-examples-rclcpp-minimal-timer]
examples_rclcpp_minimal_timer_dbgsym:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-minimal-timer-dbgsym]
    jammy: [ros-humble-examples-rclcpp-minimal-timer-dbgsym]
examples_rclcpp_multithreaded_executor:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-multithreaded-executor]
    jammy: [ros-humble-examples-rclcpp-multithreaded-executor]
examples_rclcpp_multithreaded_executor_dbgsym:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-multithreaded-executor-dbgsym]
    jammy: [ros-humble-examples-rclcpp-multithreaded-executor-dbgsym]
examples_rclcpp_wait_set:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-wait-set]
    jammy: [ros-humble-examples-rclcpp-wait-set]
examples_rclcpp_wait_set_dbgsym:
  ubuntu:
    focal: [ros-humble-examples-rclcpp-wait-set-dbgsym]
    jammy: [ros-humble-examples-rclcpp-wait-set-dbgsym]
examples_rclpy_executors:
  ubuntu:
    focal: [ros-humble-examples-rclpy-executors]
    jammy: [ros-humble-examples-rclpy-executors]
examples_rclpy_guard_conditions:
  ubuntu:
    focal: [ros-humble-examples-rclpy-guard-conditions]
    jammy: [ros-humble-examples-rclpy-guard-conditions]
examples_rclpy_minimal_action_client:
  ubuntu:
    focal: [ros-humble-examples-rclpy-minimal-action-client]
    jammy: [ros-humble-examples-rclpy-minimal-action-client]
examples_rclpy_minimal_action_server:
  ubuntu:
    focal: [ros-humble-examples-rclpy-minimal-action-server]
    jammy: [ros-humble-examples-rclpy-minimal-action-server]
examples_rclpy_minimal_client:
  ubuntu:
    focal: [ros-humble-examples-rclpy-minimal-client]
    jammy: [ros-humble-examples-rclpy-minimal-client]
examples_rclpy_minimal_publisher:
  ubuntu:
    focal: [ros-humble-examples-rclpy-minimal-publisher]
    jammy: [ros-humble-examples-rclpy-minimal-publisher]
examples_rclpy_minimal_service:
  ubuntu:
    focal: [ros-humble-examples-rclpy-minimal-service]
    jammy: [ros-humble-examples-rclpy-minimal-service]
examples_rclpy_minimal_subscriber:
  ubuntu:
    focal: [ros-humble-examples-rclpy-minimal-subscriber]
    jammy: [ros-humble-examples-rclpy-minimal-subscriber]
examples_rclpy_pointcloud_publisher:
  ubuntu:
    focal: [ros-humble-examples-rclpy-pointcloud-publisher]
    jammy: [ros-humble-examples-rclpy-pointcloud-publisher]
examples_tf2_py:
  ubuntu:
    focal: [ros-humble-examples-tf2-py]
    jammy: [ros-humble-examples-tf2-py]
executive_smach:
  ubuntu:
    focal: [ros-humble-executive-smach]
    jammy: [ros-humble-executive-smach]
fadecandy_driver:
  ubuntu:
    focal: [ros-humble-fadecandy-driver]
    jammy: [ros-humble-fadecandy-driver]
fadecandy_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-fadecandy-driver-dbgsym]
    jammy: [ros-humble-fadecandy-driver-dbgsym]
fadecandy_msgs:
  ubuntu:
    focal: [ros-humble-fadecandy-msgs]
    jammy: [ros-humble-fadecandy-msgs]
fadecandy_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-fadecandy-msgs-dbgsym]
    jammy: [ros-humble-fadecandy-msgs-dbgsym]
fastcdr:
  ubuntu:
    focal: [ros-humble-fastcdr]
    jammy: [ros-humble-fastcdr]
fastcdr_dbgsym:
  ubuntu:
    focal: [ros-humble-fastcdr-dbgsym]
    jammy: [ros-humble-fastcdr-dbgsym]
fastrtps:
  ubuntu:
    focal: [ros-humble-fastrtps]
    jammy: [ros-humble-fastrtps]
fastrtps_cmake_module:
  ubuntu:
    focal: [ros-humble-fastrtps-cmake-module]
    jammy: [ros-humble-fastrtps-cmake-module]
fastrtps_dbgsym:
  ubuntu:
    focal: [ros-humble-fastrtps-dbgsym]
    jammy: [ros-humble-fastrtps-dbgsym]
filters:
  ubuntu:
    focal: [ros-humble-filters]
    jammy: [ros-humble-filters]
filters_dbgsym:
  ubuntu:
    focal: [ros-humble-filters-dbgsym]
    jammy: [ros-humble-filters-dbgsym]
find_object_2d:
  ubuntu:
    focal: [ros-humble-find-object-2d]
    jammy: [ros-humble-find-object-2d]
find_object_2d_dbgsym:
  ubuntu:
    focal: [ros-humble-find-object-2d-dbgsym]
    jammy: [ros-humble-find-object-2d-dbgsym]
flexbe_behavior_engine:
  ubuntu:
    focal: [ros-humble-flexbe-behavior-engine]
    jammy: [ros-humble-flexbe-behavior-engine]
flexbe_core:
  ubuntu:
    focal: [ros-humble-flexbe-core]
    jammy: [ros-humble-flexbe-core]
flexbe_input:
  ubuntu:
    focal: [ros-humble-flexbe-input]
    jammy: [ros-humble-flexbe-input]
flexbe_mirror:
  ubuntu:
    focal: [ros-humble-flexbe-mirror]
    jammy: [ros-humble-flexbe-mirror]
flexbe_msgs:
  ubuntu:
    focal: [ros-humble-flexbe-msgs]
    jammy: [ros-humble-flexbe-msgs]
flexbe_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-flexbe-msgs-dbgsym]
    jammy: [ros-humble-flexbe-msgs-dbgsym]
flexbe_onboard:
  ubuntu:
    focal: [ros-humble-flexbe-onboard]
    jammy: [ros-humble-flexbe-onboard]
flexbe_states:
  ubuntu:
    focal: [ros-humble-flexbe-states]
    jammy: [ros-humble-flexbe-states]
flexbe_testing:
  ubuntu:
    focal: [ros-humble-flexbe-testing]
    jammy: [ros-humble-flexbe-testing]
flexbe_widget:
  ubuntu:
    focal: [ros-humble-flexbe-widget]
    jammy: [ros-humble-flexbe-widget]
flir_camera_description:
  ubuntu:
    focal: [ros-humble-flir-camera-description]
    jammy: [ros-humble-flir-camera-description]
flir_camera_msgs:
  ubuntu:
    focal: [ros-humble-flir-camera-msgs]
    jammy: [ros-humble-flir-camera-msgs]
flir_camera_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-flir-camera-msgs-dbgsym]
    jammy: [ros-humble-flir-camera-msgs-dbgsym]
fluent_rviz:
  ubuntu:
    focal: [ros-humble-fluent-rviz]
    jammy: [ros-humble-fluent-rviz]
fmi_adapter:
  ubuntu:
    focal: [ros-humble-fmi-adapter]
    jammy: [ros-humble-fmi-adapter]
fmi_adapter_dbgsym:
  ubuntu:
    focal: [ros-humble-fmi-adapter-dbgsym]
    jammy: [ros-humble-fmi-adapter-dbgsym]
fmi_adapter_examples:
  ubuntu:
    focal: [ros-humble-fmi-adapter-examples]
    jammy: [ros-humble-fmi-adapter-examples]
fmilibrary_vendor:
  ubuntu:
    focal: [ros-humble-fmilibrary-vendor]
    jammy: [ros-humble-fmilibrary-vendor]
fmilibrary_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-fmilibrary-vendor-dbgsym]
    jammy: [ros-humble-fmilibrary-vendor-dbgsym]
fogros2:
  ubuntu:
    focal: [ros-humble-fogros2]
    jammy: [ros-humble-fogros2]
fogros2_examples:
  ubuntu:
    focal: [ros-humble-fogros2-examples]
    jammy: [ros-humble-fogros2-examples]
foonathan_memory_vendor:
  ubuntu:
    focal: [ros-humble-foonathan-memory-vendor]
    jammy: [ros-humble-foonathan-memory-vendor]
force_torque_sensor_broadcaster:
  ubuntu:
    focal: [ros-humble-force-torque-sensor-broadcaster]
    jammy: [ros-humble-force-torque-sensor-broadcaster]
force_torque_sensor_broadcaster_dbgsym:
  ubuntu:
    focal: [ros-humble-force-torque-sensor-broadcaster-dbgsym]
    jammy: [ros-humble-force-torque-sensor-broadcaster-dbgsym]
foros:
  ubuntu:
    focal: [ros-humble-foros]
    jammy: [ros-humble-foros]
foros_dbgsym:
  ubuntu:
    focal: [ros-humble-foros-dbgsym]
    jammy: [ros-humble-foros-dbgsym]
foros_examples:
  ubuntu:
    focal: [ros-humble-foros-examples]
    jammy: [ros-humble-foros-examples]
foros_examples_dbgsym:
  ubuntu:
    focal: [ros-humble-foros-examples-dbgsym]
    jammy: [ros-humble-foros-examples-dbgsym]
foros_inspector:
  ubuntu:
    focal: [ros-humble-foros-inspector]
    jammy: [ros-humble-foros-inspector]
foros_inspector_dbgsym:
  ubuntu:
    focal: [ros-humble-foros-inspector-dbgsym]
    jammy: [ros-humble-foros-inspector-dbgsym]
foros_msgs:
  ubuntu:
    focal: [ros-humble-foros-msgs]
    jammy: [ros-humble-foros-msgs]
foros_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-foros-msgs-dbgsym]
    jammy: [ros-humble-foros-msgs-dbgsym]
forward_command_controller:
  ubuntu:
    focal: [ros-humble-forward-command-controller]
    jammy: [ros-humble-forward-command-controller]
forward_command_controller_dbgsym:
  ubuntu:
    focal: [ros-humble-forward-command-controller-dbgsym]
    jammy: [ros-humble-forward-command-controller-dbgsym]
four_wheel_steering_msgs:
  ubuntu:
    focal: [ros-humble-four-wheel-steering-msgs]
    jammy: [ros-humble-four-wheel-steering-msgs]
four_wheel_steering_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-four-wheel-steering-msgs-dbgsym]
    jammy: [ros-humble-four-wheel-steering-msgs-dbgsym]
foxglove_bridge:
  ubuntu:
    focal: [ros-humble-foxglove-bridge]
    jammy: [ros-humble-foxglove-bridge]
foxglove_bridge_dbgsym:
  ubuntu:
    focal: [ros-humble-foxglove-bridge-dbgsym]
    jammy: [ros-humble-foxglove-bridge-dbgsym]
foxglove_msgs:
  ubuntu:
    focal: [ros-humble-foxglove-msgs]
    jammy: [ros-humble-foxglove-msgs]
foxglove_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-foxglove-msgs-dbgsym]
    jammy: [ros-humble-foxglove-msgs-dbgsym]
gazebo_dev:
  ubuntu:
    focal: [ros-humble-gazebo-dev]
    jammy: [ros-humble-gazebo-dev]
gazebo_msgs:
  ubuntu:
    focal: [ros-humble-gazebo-msgs]
    jammy: [ros-humble-gazebo-msgs]
gazebo_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-gazebo-msgs-dbgsym]
    jammy: [ros-humble-gazebo-msgs-dbgsym]
gc_spl_2022:
  ubuntu:
    focal: [ros-humble-gc-spl-2022]
    jammy: [ros-humble-gc-spl-2022]
generate_parameter_library:
  ubuntu:
    focal: [ros-humble-generate-parameter-library]
    jammy: [ros-humble-generate-parameter-library]
generate_parameter_library_example:
  ubuntu:
    focal: [ros-humble-generate-parameter-library-example]
    jammy: [ros-humble-generate-parameter-library-example]
generate_parameter_library_example_dbgsym:
  ubuntu:
    focal: [ros-humble-generate-parameter-library-example-dbgsym]
    jammy: [ros-humble-generate-parameter-library-example-dbgsym]
generate_parameter_library_py:
  ubuntu:
    focal: [ros-humble-generate-parameter-library-py]
    jammy: [ros-humble-generate-parameter-library-py]
generate_parameter_module_example:
  ubuntu:
    focal: [ros-humble-generate-parameter-module-example]
    jammy: [ros-humble-generate-parameter-module-example]
geodesy:
  ubuntu:
    focal: [ros-humble-geodesy]
    jammy: [ros-humble-geodesy]
geographic_info:
  ubuntu:
    focal: [ros-humble-geographic-info]
    jammy: [ros-humble-geographic-info]
geographic_msgs:
  ubuntu:
    focal: [ros-humble-geographic-msgs]
    jammy: [ros-humble-geographic-msgs]
geographic_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-geographic-msgs-dbgsym]
    jammy: [ros-humble-geographic-msgs-dbgsym]
geometric_shapes:
  ubuntu:
    focal: [ros-humble-geometric-shapes]
    jammy: [ros-humble-geometric-shapes]
geometric_shapes_dbgsym:
  ubuntu:
    focal: [ros-humble-geometric-shapes-dbgsym]
    jammy: [ros-humble-geometric-shapes-dbgsym]
geometry2:
  ubuntu:
    focal: [ros-humble-geometry2]
    jammy: [ros-humble-geometry2]
geometry_msgs:
  ubuntu:
    focal: [ros-humble-geometry-msgs]
    jammy: [ros-humble-geometry-msgs]
geometry_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-geometry-msgs-dbgsym]
    jammy: [ros-humble-geometry-msgs-dbgsym]
geometry_tutorials:
  ubuntu:
    focal: [ros-humble-geometry-tutorials]
    jammy: [ros-humble-geometry-tutorials]
gmock_vendor:
  ubuntu:
    focal: [ros-humble-gmock-vendor]
    jammy: [ros-humble-gmock-vendor]
google_benchmark_vendor:
  ubuntu:
    focal: [ros-humble-google-benchmark-vendor]
    jammy: [ros-humble-google-benchmark-vendor]
google_benchmark_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-google-benchmark-vendor-dbgsym]
    jammy: [ros-humble-google-benchmark-vendor-dbgsym]
gps_msgs:
  ubuntu:
    focal: [ros-humble-gps-msgs]
    jammy: [ros-humble-gps-msgs]
gps_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-gps-msgs-dbgsym]
    jammy: [ros-humble-gps-msgs-dbgsym]
gps_tools:
  ubuntu:
    focal: [ros-humble-gps-tools]
    jammy: [ros-humble-gps-tools]
gps_tools_dbgsym:
  ubuntu:
    focal: [ros-humble-gps-tools-dbgsym]
    jammy: [ros-humble-gps-tools-dbgsym]
gps_umd:
  ubuntu:
    focal: [ros-humble-gps-umd]
    jammy: [ros-humble-gps-umd]
gpsd_client:
  ubuntu:
    focal: [ros-humble-gpsd-client]
    jammy: [ros-humble-gpsd-client]
gpsd_client_dbgsym:
  ubuntu:
    focal: [ros-humble-gpsd-client-dbgsym]
    jammy: [ros-humble-gpsd-client-dbgsym]
graph_msgs:
  ubuntu:
    focal: [ros-humble-graph-msgs]
    jammy: [ros-humble-graph-msgs]
graph_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-graph-msgs-dbgsym]
    jammy: [ros-humble-graph-msgs-dbgsym]
grasping_msgs:
  ubuntu:
    focal: [ros-humble-grasping-msgs]
    jammy: [ros-humble-grasping-msgs]
grasping_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-grasping-msgs-dbgsym]
    jammy: [ros-humble-grasping-msgs-dbgsym]
grbl_msgs:
  ubuntu:
    focal: [ros-humble-grbl-msgs]
    jammy: [ros-humble-grbl-msgs]
grbl_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-grbl-msgs-dbgsym]
    jammy: [ros-humble-grbl-msgs-dbgsym]
grbl_ros:
  ubuntu:
    focal: [ros-humble-grbl-ros]
    jammy: [ros-humble-grbl-ros]
grid_map:
  ubuntu:
    focal: [ros-humble-grid-map]
    jammy: [ros-humble-grid-map]
grid_map_cmake_helpers:
  ubuntu:
    focal: [ros-humble-grid-map-cmake-helpers]
    jammy: [ros-humble-grid-map-cmake-helpers]
grid_map_core:
  ubuntu:
    focal: [ros-humble-grid-map-core]
    jammy: [ros-humble-grid-map-core]
grid_map_costmap_2d:
  ubuntu:
    focal: [ros-humble-grid-map-costmap-2d]
    jammy: [ros-humble-grid-map-costmap-2d]
grid_map_cv:
  ubuntu:
    focal: [ros-humble-grid-map-cv]
    jammy: [ros-humble-grid-map-cv]
grid_map_cv_dbgsym:
  ubuntu:
    focal: [ros-humble-grid-map-cv-dbgsym]
    jammy: [ros-humble-grid-map-cv-dbgsym]
grid_map_demos:
  ubuntu:
    focal: [ros-humble-grid-map-demos]
    jammy: [ros-humble-grid-map-demos]
grid_map_demos_dbgsym:
  ubuntu:
    focal: [ros-humble-grid-map-demos-dbgsym]
    jammy: [ros-humble-grid-map-demos-dbgsym]
grid_map_filters:
  ubuntu:
    focal: [ros-humble-grid-map-filters]
    jammy: [ros-humble-grid-map-filters]
grid_map_filters_dbgsym:
  ubuntu:
    focal: [ros-humble-grid-map-filters-dbgsym]
    jammy: [ros-humble-grid-map-filters-dbgsym]
grid_map_loader:
  ubuntu:
    focal: [ros-humble-grid-map-loader]
    jammy: [ros-humble-grid-map-loader]
grid_map_loader_dbgsym:
  ubuntu:
    focal: [ros-humble-grid-map-loader-dbgsym]
    jammy: [ros-humble-grid-map-loader-dbgsym]
grid_map_msgs:
  ubuntu:
    focal: [ros-humble-grid-map-msgs]
    jammy: [ros-humble-grid-map-msgs]
grid_map_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-grid-map-msgs-dbgsym]
    jammy: [ros-humble-grid-map-msgs-dbgsym]
grid_map_octomap:
  ubuntu:
    focal: [ros-humble-grid-map-octomap]
    jammy: [ros-humble-grid-map-octomap]
grid_map_pcl:
  ubuntu:
    focal: [ros-humble-grid-map-pcl]
    jammy: [ros-humble-grid-map-pcl]
grid_map_pcl_dbgsym:
  ubuntu:
    focal: [ros-humble-grid-map-pcl-dbgsym]
    jammy: [ros-humble-grid-map-pcl-dbgsym]
grid_map_ros:
  ubuntu:
    focal: [ros-humble-grid-map-ros]
    jammy: [ros-humble-grid-map-ros]
grid_map_ros_dbgsym:
  ubuntu:
    focal: [ros-humble-grid-map-ros-dbgsym]
    jammy: [ros-humble-grid-map-ros-dbgsym]
grid_map_rviz_plugin:
  ubuntu:
    focal: [ros-humble-grid-map-rviz-plugin]
    jammy: [ros-humble-grid-map-rviz-plugin]
grid_map_rviz_plugin_dbgsym:
  ubuntu:
    focal: [ros-humble-grid-map-rviz-plugin-dbgsym]
    jammy: [ros-humble-grid-map-rviz-plugin-dbgsym]
grid_map_sdf:
  ubuntu:
    focal: [ros-humble-grid-map-sdf]
    jammy: [ros-humble-grid-map-sdf]
grid_map_visualization:
  ubuntu:
    focal: [ros-humble-grid-map-visualization]
    jammy: [ros-humble-grid-map-visualization]
grid_map_visualization_dbgsym:
  ubuntu:
    focal: [ros-humble-grid-map-visualization-dbgsym]
    jammy: [ros-humble-grid-map-visualization-dbgsym]
gripper_controllers:
  ubuntu:
    focal: [ros-humble-gripper-controllers]
    jammy: [ros-humble-gripper-controllers]
gripper_controllers_dbgsym:
  ubuntu:
    focal: [ros-humble-gripper-controllers-dbgsym]
    jammy: [ros-humble-gripper-controllers-dbgsym]
gscam:
  ubuntu:
    focal: [ros-humble-gscam]
    jammy: [ros-humble-gscam]
gscam_dbgsym:
  ubuntu:
    focal: [ros-humble-gscam-dbgsym]
    jammy: [ros-humble-gscam-dbgsym]
gtest_vendor:
  ubuntu:
    focal: [ros-humble-gtest-vendor]
    jammy: [ros-humble-gtest-vendor]
gtsam:
  ubuntu:
    focal: [ros-humble-gtsam]
    jammy: [ros-humble-gtsam]
gtsam_dbgsym:
  ubuntu:
    focal: [ros-humble-gtsam-dbgsym]
    jammy: [ros-humble-gtsam-dbgsym]
gxf_isaac_argus:
  ubuntu:
    focal: [ros-humble-gxf-isaac-argus]
    jammy: [ros-humble-gxf-isaac-argus]
gxf_isaac_atlas:
  ubuntu:
    focal: [ros-humble-gxf-isaac-atlas]
    jammy: [ros-humble-gxf-isaac-atlas]
gxf_isaac_bi3d:
  ubuntu:
    focal: [ros-humble-gxf-isaac-bi3d]
    jammy: [ros-humble-gxf-isaac-bi3d]
gxf_isaac_bmi088_imu:
  ubuntu:
    focal: [ros-humble-gxf-isaac-bmi088-imu]
    jammy: [ros-humble-gxf-isaac-bmi088-imu]
gxf_isaac_camera_utils:
  ubuntu:
    focal: [ros-humble-gxf-isaac-camera-utils]
    jammy: [ros-humble-gxf-isaac-camera-utils]
gxf_isaac_centerpose:
  ubuntu:
    focal: [ros-humble-gxf-isaac-centerpose]
    jammy: [ros-humble-gxf-isaac-centerpose]
gxf_isaac_cuda:
  ubuntu:
    focal: [ros-humble-gxf-isaac-cuda]
    jammy: [ros-humble-gxf-isaac-cuda]
gxf_isaac_depth_image_proc:
  ubuntu:
    focal: [ros-humble-gxf-isaac-depth-image-proc]
    jammy: [ros-humble-gxf-isaac-depth-image-proc]
gxf_isaac_detectnet:
  ubuntu:
    focal: [ros-humble-gxf-isaac-detectnet]
    jammy: [ros-humble-gxf-isaac-detectnet]
gxf_isaac_dope:
  ubuntu:
    focal: [ros-humble-gxf-isaac-dope]
    jammy: [ros-humble-gxf-isaac-dope]
gxf_isaac_ess:
  ubuntu:
    focal: [ros-humble-gxf-isaac-ess]
    jammy: [ros-humble-gxf-isaac-ess]
gxf_isaac_fiducials:
  ubuntu:
    focal: [ros-humble-gxf-isaac-fiducials]
    jammy: [ros-humble-gxf-isaac-fiducials]
gxf_isaac_flatscan_localization:
  ubuntu:
    focal: [ros-humble-gxf-isaac-flatscan-localization]
    jammy: [ros-humble-gxf-isaac-flatscan-localization]
gxf_isaac_foundationpose:
  ubuntu:
    focal: [ros-humble-gxf-isaac-foundationpose]
    jammy: [ros-humble-gxf-isaac-foundationpose]
gxf_isaac_gems:
  ubuntu:
    focal: [ros-humble-gxf-isaac-gems]
    jammy: [ros-humble-gxf-isaac-gems]
gxf_isaac_gxf_helpers:
  ubuntu:
    focal: [ros-humble-gxf-isaac-gxf-helpers]
    jammy: [ros-humble-gxf-isaac-gxf-helpers]
gxf_isaac_hesai:
  ubuntu:
    focal: [ros-humble-gxf-isaac-hesai]
    jammy: [ros-humble-gxf-isaac-hesai]
gxf_isaac_image_flip:
  ubuntu:
    focal: [ros-humble-gxf-isaac-image-flip]
    jammy: [ros-humble-gxf-isaac-image-flip]
gxf_isaac_imu_utils:
  ubuntu:
    focal: [ros-humble-gxf-isaac-imu-utils]
    jammy: [ros-humble-gxf-isaac-imu-utils]
gxf_isaac_localization:
  ubuntu:
    focal: [ros-humble-gxf-isaac-localization]
    jammy: [ros-humble-gxf-isaac-localization]
gxf_isaac_message_compositor:
  ubuntu:
    focal: [ros-humble-gxf-isaac-message-compositor]
    jammy: [ros-humble-gxf-isaac-message-compositor]
gxf_isaac_messages:
  ubuntu:
    focal: [ros-humble-gxf-isaac-messages]
    jammy: [ros-humble-gxf-isaac-messages]
gxf_isaac_messages_throttler:
  ubuntu:
    focal: [ros-humble-gxf-isaac-messages-throttler]
    jammy: [ros-humble-gxf-isaac-messages-throttler]
gxf_isaac_occupancy_grid_projector:
  ubuntu:
    focal: [ros-humble-gxf-isaac-occupancy-grid-projector]
    jammy: [ros-humble-gxf-isaac-occupancy-grid-projector]
gxf_isaac_optimizer:
  ubuntu:
    focal: [ros-humble-gxf-isaac-optimizer]
    jammy: [ros-humble-gxf-isaac-optimizer]
gxf_isaac_point_cloud:
  ubuntu:
    focal: [ros-humble-gxf-isaac-point-cloud]
    jammy: [ros-humble-gxf-isaac-point-cloud]
gxf_isaac_range_scan_processing:
  ubuntu:
    focal: [ros-humble-gxf-isaac-range-scan-processing]
    jammy: [ros-humble-gxf-isaac-range-scan-processing]
gxf_isaac_rectify:
  ubuntu:
    focal: [ros-humble-gxf-isaac-rectify]
    jammy: [ros-humble-gxf-isaac-rectify]
gxf_isaac_ros_cuda:
  ubuntu:
    focal: [ros-humble-gxf-isaac-ros-cuda]
    jammy: [ros-humble-gxf-isaac-ros-cuda]
gxf_isaac_ros_image_segmentation:
  ubuntu:
    focal: [ros-humble-gxf-isaac-ros-image-segmentation]
    jammy: [ros-humble-gxf-isaac-ros-image-segmentation]
gxf_isaac_ros_messages:
  ubuntu:
    focal: [ros-humble-gxf-isaac-ros-messages]
    jammy: [ros-humble-gxf-isaac-ros-messages]
gxf_isaac_ros_segment_anything:
  ubuntu:
    focal: [ros-humble-gxf-isaac-ros-segment-anything]
    jammy: [ros-humble-gxf-isaac-ros-segment-anything]
gxf_isaac_ros_unet:
  ubuntu:
    focal: [ros-humble-gxf-isaac-ros-unet]
    jammy: [ros-humble-gxf-isaac-ros-unet]
gxf_isaac_segway:
  ubuntu:
    focal: [ros-humble-gxf-isaac-segway]
    jammy: [ros-humble-gxf-isaac-segway]
gxf_isaac_sgm:
  ubuntu:
    focal: [ros-humble-gxf-isaac-sgm]
    jammy: [ros-humble-gxf-isaac-sgm]
gxf_isaac_sight:
  ubuntu:
    focal: [ros-humble-gxf-isaac-sight]
    jammy: [ros-humble-gxf-isaac-sight]
gxf_isaac_synchronization:
  ubuntu:
    focal: [ros-humble-gxf-isaac-synchronization]
    jammy: [ros-humble-gxf-isaac-synchronization]
gxf_isaac_tensor_rt:
  ubuntu:
    focal: [ros-humble-gxf-isaac-tensor-rt]
    jammy: [ros-humble-gxf-isaac-tensor-rt]
gxf_isaac_tensorops:
  ubuntu:
    focal: [ros-humble-gxf-isaac-tensorops]
    jammy: [ros-humble-gxf-isaac-tensorops]
gxf_isaac_timestamp_correlator:
  ubuntu:
    focal: [ros-humble-gxf-isaac-timestamp-correlator]
    jammy: [ros-humble-gxf-isaac-timestamp-correlator]
gxf_isaac_triton:
  ubuntu:
    focal: [ros-humble-gxf-isaac-triton]
    jammy: [ros-humble-gxf-isaac-triton]
gxf_isaac_utils:
  ubuntu:
    focal: [ros-humble-gxf-isaac-utils]
    jammy: [ros-humble-gxf-isaac-utils]
gxf_isaac_video_buffer_utils:
  ubuntu:
    focal: [ros-humble-gxf-isaac-video-buffer-utils]
    jammy: [ros-humble-gxf-isaac-video-buffer-utils]
hardware_interface:
  ubuntu:
    focal: [ros-humble-hardware-interface]
    jammy: [ros-humble-hardware-interface]
hardware_interface_dbgsym:
  ubuntu:
    focal: [ros-humble-hardware-interface-dbgsym]
    jammy: [ros-humble-hardware-interface-dbgsym]
hash_library_vendor:
  ubuntu:
    focal: [ros-humble-hash-library-vendor]
    jammy: [ros-humble-hash-library-vendor]
hash_library_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-hash-library-vendor-dbgsym]
    jammy: [ros-humble-hash-library-vendor-dbgsym]
hawk_description:
  ubuntu:
    focal: [ros-humble-hawk-description]
    jammy: [ros-humble-hawk-description]
heaphook:
  ubuntu:
    focal: [ros-humble-heaphook]
    jammy: [ros-humble-heaphook]
heaphook_dbgsym:
  ubuntu:
    focal: [ros-humble-heaphook-dbgsym]
    jammy: [ros-humble-heaphook-dbgsym]
hesai_ros_driver:
  ubuntu:
    focal: [ros-humble-hesai-ros-driver]
    jammy: [ros-humble-hesai-ros-driver]
hey5_description:
  ubuntu:
    focal: [ros-humble-hey5-description]
    jammy: [ros-humble-hey5-description]
hls_lfcd_lds_driver:
  ubuntu:
    focal: [ros-humble-hls-lfcd-lds-driver]
    jammy: [ros-humble-hls-lfcd-lds-driver]
hls_lfcd_lds_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-hls-lfcd-lds-driver-dbgsym]
    jammy: [ros-humble-hls-lfcd-lds-driver-dbgsym]
hpp_fcl:
  ubuntu:
    focal: [ros-humble-hpp-fcl]
    jammy: [ros-humble-hpp-fcl]
hpp_fcl_dbgsym:
  ubuntu:
    focal: [ros-humble-hpp-fcl-dbgsym]
    jammy: [ros-humble-hpp-fcl-dbgsym]
ibeo_msgs:
  ubuntu:
    focal: [ros-humble-ibeo-msgs]
    jammy: [ros-humble-ibeo-msgs]
ibeo_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-ibeo-msgs-dbgsym]
    jammy: [ros-humble-ibeo-msgs-dbgsym]
iceoryx_binding_c:
  ubuntu:
    focal: [ros-humble-iceoryx-binding-c]
    jammy: [ros-humble-iceoryx-binding-c]
iceoryx_binding_c_dbgsym:
  ubuntu:
    focal: [ros-humble-iceoryx-binding-c-dbgsym]
    jammy: [ros-humble-iceoryx-binding-c-dbgsym]
iceoryx_hoofs:
  ubuntu:
    focal: [ros-humble-iceoryx-hoofs]
    jammy: [ros-humble-iceoryx-hoofs]
iceoryx_hoofs_dbgsym:
  ubuntu:
    focal: [ros-humble-iceoryx-hoofs-dbgsym]
    jammy: [ros-humble-iceoryx-hoofs-dbgsym]
iceoryx_posh:
  ubuntu:
    focal: [ros-humble-iceoryx-posh]
    jammy: [ros-humble-iceoryx-posh]
iceoryx_posh_dbgsym:
  ubuntu:
    focal: [ros-humble-iceoryx-posh-dbgsym]
    jammy: [ros-humble-iceoryx-posh-dbgsym]
ifm3d_core:
  ubuntu:
    focal: [ros-humble-ifm3d-core]
    jammy: [ros-humble-ifm3d-core]
ign_ros2_control:
  ubuntu:
    focal: [ros-humble-ign-ros2-control]
    jammy: [ros-humble-ign-ros2-control]
ign_ros2_control_dbgsym:
  ubuntu:
    focal: [ros-humble-ign-ros2-control-dbgsym]
    jammy: [ros-humble-ign-ros2-control-dbgsym]
ign_ros2_control_demos:
  ubuntu:
    focal: [ros-humble-ign-ros2-control-demos]
    jammy: [ros-humble-ign-ros2-control-demos]
ign_ros2_control_demos_dbgsym:
  ubuntu:
    focal: [ros-humble-ign-ros2-control-demos-dbgsym]
    jammy: [ros-humble-ign-ros2-control-demos-dbgsym]
ignition_cmake2_vendor:
  ubuntu:
    focal: [ros-humble-ignition-cmake2-vendor]
    jammy: [ros-humble-ignition-cmake2-vendor]
ignition_math6_vendor:
  ubuntu:
    focal: [ros-humble-ignition-math6-vendor]
    jammy: [ros-humble-ignition-math6-vendor]
image_common:
  ubuntu:
    focal: [ros-humble-image-common]
    jammy: [ros-humble-image-common]
image_geometry:
  ubuntu:
    focal: [ros-humble-image-geometry]
    jammy: [ros-humble-image-geometry]
image_geometry_dbgsym:
  ubuntu:
    focal: [ros-humble-image-geometry-dbgsym]
    jammy: [ros-humble-image-geometry-dbgsym]
image_pipeline:
  ubuntu:
    focal: [ros-humble-image-pipeline]
    jammy: [ros-humble-image-pipeline]
image_proc:
  ubuntu:
    focal: [ros-humble-image-proc]
    jammy: [ros-humble-image-proc]
image_proc_dbgsym:
  ubuntu:
    focal: [ros-humble-image-proc-dbgsym]
    jammy: [ros-humble-image-proc-dbgsym]
image_publisher:
  ubuntu:
    focal: [ros-humble-image-publisher]
    jammy: [ros-humble-image-publisher]
image_publisher_dbgsym:
  ubuntu:
    focal: [ros-humble-image-publisher-dbgsym]
    jammy: [ros-humble-image-publisher-dbgsym]
image_rotate:
  ubuntu:
    focal: [ros-humble-image-rotate]
    jammy: [ros-humble-image-rotate]
image_rotate_dbgsym:
  ubuntu:
    focal: [ros-humble-image-rotate-dbgsym]
    jammy: [ros-humble-image-rotate-dbgsym]
image_tools:
  ubuntu:
    focal: [ros-humble-image-tools]
    jammy: [ros-humble-image-tools]
image_tools_dbgsym:
  ubuntu:
    focal: [ros-humble-image-tools-dbgsym]
    jammy: [ros-humble-image-tools-dbgsym]
image_transport:
  ubuntu:
    focal: [ros-humble-image-transport]
    jammy: [ros-humble-image-transport]
image_transport_dbgsym:
  ubuntu:
    focal: [ros-humble-image-transport-dbgsym]
    jammy: [ros-humble-image-transport-dbgsym]
image_transport_plugins:
  ubuntu:
    focal: [ros-humble-image-transport-plugins]
    jammy: [ros-humble-image-transport-plugins]
image_view:
  ubuntu:
    focal: [ros-humble-image-view]
    jammy: [ros-humble-image-view]
image_view_dbgsym:
  ubuntu:
    focal: [ros-humble-image-view-dbgsym]
    jammy: [ros-humble-image-view-dbgsym]
imu_complementary_filter:
  ubuntu:
    focal: [ros-humble-imu-complementary-filter]
    jammy: [ros-humble-imu-complementary-filter]
imu_complementary_filter_dbgsym:
  ubuntu:
    focal: [ros-humble-imu-complementary-filter-dbgsym]
    jammy: [ros-humble-imu-complementary-filter-dbgsym]
imu_filter_madgwick:
  ubuntu:
    focal: [ros-humble-imu-filter-madgwick]
    jammy: [ros-humble-imu-filter-madgwick]
imu_filter_madgwick_dbgsym:
  ubuntu:
    focal: [ros-humble-imu-filter-madgwick-dbgsym]
    jammy: [ros-humble-imu-filter-madgwick-dbgsym]
imu_sensor_broadcaster:
  ubuntu:
    focal: [ros-humble-imu-sensor-broadcaster]
    jammy: [ros-humble-imu-sensor-broadcaster]
imu_sensor_broadcaster_dbgsym:
  ubuntu:
    focal: [ros-humble-imu-sensor-broadcaster-dbgsym]
    jammy: [ros-humble-imu-sensor-broadcaster-dbgsym]
imu_tools:
  ubuntu:
    focal: [ros-humble-imu-tools]
    jammy: [ros-humble-imu-tools]
interactive_marker_twist_server:
  ubuntu:
    focal: [ros-humble-interactive-marker-twist-server]
    jammy: [ros-humble-interactive-marker-twist-server]
interactive_marker_twist_server_dbgsym:
  ubuntu:
    focal: [ros-humble-interactive-marker-twist-server-dbgsym]
    jammy: [ros-humble-interactive-marker-twist-server-dbgsym]
interactive_markers:
  ubuntu:
    focal: [ros-humble-interactive-markers]
    jammy: [ros-humble-interactive-markers]
interactive_markers_dbgsym:
  ubuntu:
    focal: [ros-humble-interactive-markers-dbgsym]
    jammy: [ros-humble-interactive-markers-dbgsym]
intra_process_demo:
  ubuntu:
    focal: [ros-humble-intra-process-demo]
    jammy: [ros-humble-intra-process-demo]
intra_process_demo_dbgsym:
  ubuntu:
    focal: [ros-humble-intra-process-demo-dbgsym]
    jammy: [ros-humble-intra-process-demo-dbgsym]
io_context:
  ubuntu:
    focal: [ros-humble-io-context]
    jammy: [ros-humble-io-context]
io_context_dbgsym:
  ubuntu:
    focal: [ros-humble-io-context-dbgsym]
    jammy: [ros-humble-io-context-dbgsym]
irobot_create_common_bringup:
  ubuntu:
    focal: [ros-humble-irobot-create-common-bringup]
    jammy: [ros-humble-irobot-create-common-bringup]
irobot_create_control:
  ubuntu:
    focal: [ros-humble-irobot-create-control]
    jammy: [ros-humble-irobot-create-control]
irobot_create_description:
  ubuntu:
    focal: [ros-humble-irobot-create-description]
    jammy: [ros-humble-irobot-create-description]
irobot_create_ignition_bringup:
  ubuntu:
    focal: [ros-humble-irobot-create-ignition-bringup]
    jammy: [ros-humble-irobot-create-ignition-bringup]
irobot_create_ignition_plugins:
  ubuntu:
    focal: [ros-humble-irobot-create-ignition-plugins]
    jammy: [ros-humble-irobot-create-ignition-plugins]
irobot_create_ignition_plugins_dbgsym:
  ubuntu:
    focal: [ros-humble-irobot-create-ignition-plugins-dbgsym]
    jammy: [ros-humble-irobot-create-ignition-plugins-dbgsym]
irobot_create_ignition_sim:
  ubuntu:
    focal: [ros-humble-irobot-create-ignition-sim]
    jammy: [ros-humble-irobot-create-ignition-sim]
irobot_create_ignition_toolbox:
  ubuntu:
    focal: [ros-humble-irobot-create-ignition-toolbox]
    jammy: [ros-humble-irobot-create-ignition-toolbox]
irobot_create_ignition_toolbox_dbgsym:
  ubuntu:
    focal: [ros-humble-irobot-create-ignition-toolbox-dbgsym]
    jammy: [ros-humble-irobot-create-ignition-toolbox-dbgsym]
irobot_create_msgs:
  ubuntu:
    focal: [ros-humble-irobot-create-msgs]
    jammy: [ros-humble-irobot-create-msgs]
irobot_create_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-irobot-create-msgs-dbgsym]
    jammy: [ros-humble-irobot-create-msgs-dbgsym]
irobot_create_nodes:
  ubuntu:
    focal: [ros-humble-irobot-create-nodes]
    jammy: [ros-humble-irobot-create-nodes]
irobot_create_nodes_dbgsym:
  ubuntu:
    focal: [ros-humble-irobot-create-nodes-dbgsym]
    jammy: [ros-humble-irobot-create-nodes-dbgsym]
irobot_create_toolbox:
  ubuntu:
    focal: [ros-humble-irobot-create-toolbox]
    jammy: [ros-humble-irobot-create-toolbox]
irobot_create_toolbox_dbgsym:
  ubuntu:
    focal: [ros-humble-irobot-create-toolbox-dbgsym]
    jammy: [ros-humble-irobot-create-toolbox-dbgsym]
isaac_common:
  ubuntu:
    focal: [ros-humble-isaac-common]
    jammy: [ros-humble-isaac-common]
isaac_common_py:
  ubuntu:
    focal: [ros-humble-isaac-common-py]
    jammy: [ros-humble-isaac-common-py]
isaac_manipulator_bringup:
  ubuntu:
    focal: [ros-humble-isaac-manipulator-bringup]
    jammy: [ros-humble-isaac-manipulator-bringup]
isaac_manipulator_interfaces:
  ubuntu:
    focal: [ros-humble-isaac-manipulator-interfaces]
    jammy: [ros-humble-isaac-manipulator-interfaces]
isaac_manipulator_pick_and_place:
  ubuntu:
    focal: [ros-humble-isaac-manipulator-pick-and-place]
    jammy: [ros-humble-isaac-manipulator-pick-and-place]
isaac_manipulator_ros_python_utils:
  ubuntu:
    focal: [ros-humble-isaac-manipulator-ros-python-utils]
    jammy: [ros-humble-isaac-manipulator-ros-python-utils]
isaac_manipulator_servers:
  ubuntu:
    focal: [ros-humble-isaac-manipulator-servers]
    jammy: [ros-humble-isaac-manipulator-servers]
isaac_mapping_and_localization_ros:
  ubuntu:
    focal: [ros-humble-isaac-mapping-and-localization-ros]
    jammy: [ros-humble-isaac-mapping-and-localization-ros]
isaac_mapping_ros:
  ubuntu:
    focal: [ros-humble-isaac-mapping-ros]
    jammy: [ros-humble-isaac-mapping-ros]
isaac_ros_apriltag:
  ubuntu:
    focal: [ros-humble-isaac-ros-apriltag]
    jammy: [ros-humble-isaac-ros-apriltag]
isaac_ros_apriltag_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-apriltag-benchmark]
    jammy: [ros-humble-isaac-ros-apriltag-benchmark]
isaac_ros_apriltag_interfaces:
  ubuntu:
    focal: [ros-humble-isaac-ros-apriltag-interfaces]
    jammy: [ros-humble-isaac-ros-apriltag-interfaces]
isaac_ros_apriltag_msgs:
  ubuntu:
    focal: [ros-noetic-isaac-ros-apriltag-msgs]
    jammy: [ros-noetic-isaac-ros-apriltag-msgs]
isaac_ros_argus_camera:
  ubuntu:
    focal: [ros-humble-isaac-ros-argus-camera]
    jammy: [ros-humble-isaac-ros-argus-camera]
isaac_ros_argus_camera_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-argus-camera-benchmark]
    jammy: [ros-humble-isaac-ros-argus-camera-benchmark]
isaac_ros_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-benchmark]
    jammy: [ros-humble-isaac-ros-benchmark]
isaac_ros_bi3d:
  ubuntu:
    focal: [ros-humble-isaac-ros-bi3d]
    jammy: [ros-humble-isaac-ros-bi3d]
isaac_ros_bi3d_freespace:
  ubuntu:
    focal: [ros-humble-isaac-ros-bi3d-freespace]
    jammy: [ros-humble-isaac-ros-bi3d-freespace]
isaac_ros_bi3d_freespace_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-bi3d-freespace-benchmark]
    jammy: [ros-humble-isaac-ros-bi3d-freespace-benchmark]
isaac_ros_bi3d_interfaces:
  ubuntu:
    focal: [ros-humble-isaac-ros-bi3d-interfaces]
    jammy: [ros-humble-isaac-ros-bi3d-interfaces]
isaac_ros_bi3d_msgs:
  ubuntu:
    focal: [ros-noetic-isaac-ros-bi3d-msgs]
    jammy: [ros-noetic-isaac-ros-bi3d-msgs]
isaac_ros_calibration_validation:
  ubuntu:
    focal: [ros-humble-isaac-ros-calibration-validation]
    jammy: [ros-humble-isaac-ros-calibration-validation]
isaac_ros_centerpose:
  ubuntu:
    focal: [ros-humble-isaac-ros-centerpose]
    jammy: [ros-humble-isaac-ros-centerpose]
isaac_ros_centerpose_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-centerpose-benchmark]
    jammy: [ros-humble-isaac-ros-centerpose-benchmark]
isaac_ros_common:
  ubuntu:
    focal: [ros-humble-isaac-ros-common]
    jammy: [ros-humble-isaac-ros-common]
isaac_ros_correlated_timestamp_driver:
  ubuntu:
    focal: [ros-humble-isaac-ros-correlated-timestamp-driver]
    jammy: [ros-humble-isaac-ros-correlated-timestamp-driver]
isaac_ros_cumotion:
  ubuntu:
    focal: [ros-humble-isaac-ros-cumotion]
    jammy: [ros-humble-isaac-ros-cumotion]
isaac_ros_cumotion_examples:
  ubuntu:
    focal: [ros-humble-isaac-ros-cumotion-examples]
    jammy: [ros-humble-isaac-ros-cumotion-examples]
isaac_ros_cumotion_interfaces:
  ubuntu:
    focal: [ros-humble-isaac-ros-cumotion-interfaces]
    jammy: [ros-humble-isaac-ros-cumotion-interfaces]
isaac_ros_cumotion_moveit:
  ubuntu:
    focal: [ros-humble-isaac-ros-cumotion-moveit]
    jammy: [ros-humble-isaac-ros-cumotion-moveit]
isaac_ros_cumotion_object_attachment:
  ubuntu:
    focal: [ros-humble-isaac-ros-cumotion-object-attachment]
    jammy: [ros-humble-isaac-ros-cumotion-object-attachment]
isaac_ros_cumotion_python_utils:
  ubuntu:
    focal: [ros-humble-isaac-ros-cumotion-python-utils]
    jammy: [ros-humble-isaac-ros-cumotion-python-utils]
isaac_ros_cumotion_robot_description:
  ubuntu:
    focal: [ros-humble-isaac-ros-cumotion-robot-description]
    jammy: [ros-humble-isaac-ros-cumotion-robot-description]
isaac_ros_data_recorder:
  ubuntu:
    focal: [ros-humble-isaac-ros-data-recorder]
    jammy: [ros-humble-isaac-ros-data-recorder]
isaac_ros_data_recorder_nova_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-data-recorder-nova-benchmark]
    jammy: [ros-humble-isaac-ros-data-recorder-nova-benchmark]
isaac_ros_data_replayer:
  ubuntu:
    focal: [ros-humble-isaac-ros-data-replayer]
    jammy: [ros-humble-isaac-ros-data-replayer]
isaac_ros_data_validation:
  ubuntu:
    focal: [ros-humble-isaac-ros-data-validation]
    jammy: [ros-humble-isaac-ros-data-validation]
isaac_ros_deepmap_data_converter:
  ubuntu:
    focal: [ros-humble-isaac-ros-deepmap-data-converter]
    jammy: [ros-humble-isaac-ros-deepmap-data-converter]
isaac_ros_deepmap_data_converter:
  ubuntu:
    focal: [ros-humble-isaac-ros-deepmap-data-converter]
    jammy: [ros-humble-isaac-ros-deepmap-data-converter]
isaac_ros_depth_image_proc:
  ubuntu:
    focal: [ros-humble-isaac-ros-depth-image-proc]
    jammy: [ros-humble-isaac-ros-depth-image-proc]
isaac_ros_detectnet:
  ubuntu:
    focal: [ros-humble-isaac-ros-detectnet]
    jammy: [ros-humble-isaac-ros-detectnet]
isaac_ros_detectnet_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-detectnet-benchmark]
    jammy: [ros-humble-isaac-ros-detectnet-benchmark]
isaac_ros_dnn_image_encoder:
  ubuntu:
    focal: [ros-humble-isaac-ros-dnn-image-encoder]
    jammy: [ros-humble-isaac-ros-dnn-image-encoder]
isaac_ros_dnn_image_encoder_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-dnn-image-encoder-benchmark]
    jammy: [ros-humble-isaac-ros-dnn-image-encoder-benchmark]
isaac_ros_dnn_inference_test:
  ubuntu:
    focal: [ros-humble-isaac-ros-dnn-inference-test]
    jammy: [ros-humble-isaac-ros-dnn-inference-test]
isaac_ros_dope:
  ubuntu:
    focal: [ros-humble-isaac-ros-dope]
    jammy: [ros-humble-isaac-ros-dope]
isaac_ros_dope_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-dope-benchmark]
    jammy: [ros-humble-isaac-ros-dope-benchmark]
isaac_ros_esdf_visualizer:
  ubuntu:
    focal: [ros-humble-isaac-ros-esdf-visualizer]
    jammy: [ros-humble-isaac-ros-esdf-visualizer]
isaac_ros_ess:
  ubuntu:
    focal: [ros-humble-isaac-ros-ess]
    jammy: [ros-humble-isaac-ros-ess]
isaac_ros_ess_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-ess-benchmark]
    jammy: [ros-humble-isaac-ros-ess-benchmark]
isaac_ros_ess_models_install:
  ubuntu:
    focal: [ros-humble-isaac-ros-ess-models-install]
    jammy: [ros-humble-isaac-ros-ess-models-install]
isaac_ros_ess_nova_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-ess-nova-benchmark]
    jammy: [ros-humble-isaac-ros-ess-nova-benchmark]
isaac_ros_examples:
  ubuntu:
    focal: [ros-humble-isaac-ros-examples]
    jammy: [ros-humble-isaac-ros-examples]
isaac_ros_foundationpose:
  ubuntu:
    focal: [ros-humble-isaac-ros-foundationpose]
    jammy: [ros-humble-isaac-ros-foundationpose]
isaac_ros_foundationpose_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-foundationpose-benchmark]
    jammy: [ros-humble-isaac-ros-foundationpose-benchmark]
isaac_ros_franka_cumotion_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-franka-cumotion-benchmark]
    jammy: [ros-humble-isaac-ros-franka-cumotion-benchmark]
isaac_ros_franka_ompl_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-franka-ompl-benchmark]
    jammy: [ros-humble-isaac-ros-franka-ompl-benchmark]
isaac_ros_goal_setter_interfaces:
  ubuntu:
    focal: [ros-humble-isaac-ros-goal-setter-interfaces]
    jammy: [ros-humble-isaac-ros-goal-setter-interfaces]
isaac_ros_ground_calibration:
  ubuntu:
    focal: [ros-humble-isaac-ros-ground-calibration]
    jammy: [ros-humble-isaac-ros-ground-calibration]
isaac_ros_gxf:
  ubuntu:
    focal: [ros-humble-isaac-ros-gxf]
    jammy: [ros-humble-isaac-ros-gxf]
isaac_ros_h264_decoder:
  ubuntu:
    focal: [ros-humble-isaac-ros-h264-decoder]
    jammy: [ros-humble-isaac-ros-h264-decoder]
isaac_ros_h264_decoder_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-h264-decoder-benchmark]
    jammy: [ros-humble-isaac-ros-h264-decoder-benchmark]
isaac_ros_h264_encoder:
  ubuntu:
    focal: [ros-humble-isaac-ros-h264-encoder]
    jammy: [ros-humble-isaac-ros-h264-encoder]
isaac_ros_h264_encoder_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-h264-encoder-benchmark]
    jammy: [ros-humble-isaac-ros-h264-encoder-benchmark]
isaac_ros_hawk:
  ubuntu:
    focal: [ros-humble-isaac-ros-hawk]
    jammy: [ros-humble-isaac-ros-hawk]
isaac_ros_hawk_nova_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-hawk-nova-benchmark]
    jammy: [ros-humble-isaac-ros-hawk-nova-benchmark]
isaac_ros_hesai:
  ubuntu:
    focal: [ros-humble-isaac-ros-hesai]
    jammy: [ros-humble-isaac-ros-hesai]
isaac_ros_image_pipeline:
  ubuntu:
    focal: [ros-humble-isaac-ros-image-pipeline]
    jammy: [ros-humble-isaac-ros-image-pipeline]
isaac_ros_image_proc:
  ubuntu:
    focal: [ros-humble-isaac-ros-image-proc]
    jammy: [ros-humble-isaac-ros-image-proc]
isaac_ros_image_proc_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-image-proc-benchmark]
    jammy: [ros-humble-isaac-ros-image-proc-benchmark]
isaac_ros_imu_bmi088:
  ubuntu:
    focal: [ros-humble-isaac-ros-imu-bmi088]
    jammy: [ros-humble-isaac-ros-imu-bmi088]
isaac_ros_integration_test:
  ubuntu:
    focal: [ros-humble-isaac-ros-integration-test]
    jammy: [ros-humble-isaac-ros-integration-test]
isaac_ros_integration_test_control_node:
  ubuntu:
    focal: [ros-humble-isaac-ros-integration-test-control-node]
    jammy: [ros-humble-isaac-ros-integration-test-control-node]
isaac_ros_integration_test_interfaces:
  ubuntu:
    focal: [ros-humble-isaac-ros-integration-test-interfaces]
    jammy: [ros-humble-isaac-ros-integration-test-interfaces]
isaac_ros_integration_test_msgs:
  ubuntu:
    focal: [ros-noetic-isaac-ros-integration-test-msgs]
    jammy: [ros-noetic-isaac-ros-integration-test-msgs]
isaac_ros_jetson_stats:
  ubuntu:
    focal: [ros-humble-isaac-ros-jetson-stats]
    jammy: [ros-humble-isaac-ros-jetson-stats]
isaac_ros_jetson_stats_services:
  ubuntu:
    focal: [ros-humble-isaac-ros-jetson-stats-services]
    jammy: [ros-humble-isaac-ros-jetson-stats-services]
isaac_ros_json_info_generator:
  ubuntu:
    focal: [ros-humble-isaac-ros-json-info-generator]
    jammy: [ros-humble-isaac-ros-json-info-generator]
isaac_ros_launch_utils:
  ubuntu:
    focal: [ros-humble-isaac-ros-launch-utils]
    jammy: [ros-humble-isaac-ros-launch-utils]
isaac_ros_lidar_camera_projection:
  ubuntu:
    focal: [ros-humble-isaac-ros-lidar-camera-projection]
    jammy: [ros-humble-isaac-ros-lidar-camera-projection]
isaac_ros_managed_nitros:
  ubuntu:
    focal: [ros-humble-isaac-ros-managed-nitros]
    jammy: [ros-humble-isaac-ros-managed-nitros]
isaac_ros_mission_client:
  ubuntu:
    focal: [ros-humble-isaac-ros-mission-client]
    jammy: [ros-humble-isaac-ros-mission-client]
isaac_ros_moveit_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-moveit-benchmark]
    jammy: [ros-humble-isaac-ros-moveit-benchmark]
isaac_ros_moveit_goal_setter:
  ubuntu:
    focal: [ros-humble-isaac-ros-moveit-goal-setter]
    jammy: [ros-humble-isaac-ros-moveit-goal-setter]
isaac_ros_mqtt_bridge:
  ubuntu:
    focal: [ros-humble-isaac-ros-mqtt-bridge]
    jammy: [ros-humble-isaac-ros-mqtt-bridge]
isaac_ros_nitros:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros]
    jammy: [ros-humble-isaac-ros-nitros]
isaac_ros_nitros_battery_state_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-battery-state-type]
    jammy: [ros-humble-isaac-ros-nitros-battery-state-type]
isaac_ros_nitros_bi3d_inference_param_array_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-bi3d-inference-param-array-type]
    jammy: [ros-humble-isaac-ros-nitros-bi3d-inference-param-array-type]
isaac_ros_nitros_bridge_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-bridge-benchmark]
    jammy: [ros-humble-isaac-ros-nitros-bridge-benchmark]
isaac_ros_nitros_bridge_interfaces:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-bridge-interfaces]
    jammy: [ros-humble-isaac-ros-nitros-bridge-interfaces]
isaac_ros_nitros_bridge_msgs:
  ubuntu:
    focal: [ros-noetic-isaac-ros-nitros-bridge-msgs]
    jammy: [ros-noetic-isaac-ros-nitros-bridge-msgs]
isaac_ros_nitros_bridge_ros1:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-bridge-ros1]
    jammy: [ros-humble-isaac-ros-nitros-bridge-ros1]
isaac_ros_nitros_bridge_ros2:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-bridge-ros2]
    jammy: [ros-humble-isaac-ros-nitros-bridge-ros2]
isaac_ros_nitros_camera_info_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-camera-info-type]
    jammy: [ros-humble-isaac-ros-nitros-camera-info-type]
isaac_ros_nitros_compressed_image_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-compressed-image-type]
    jammy: [ros-humble-isaac-ros-nitros-compressed-image-type]
isaac_ros_nitros_compressed_video_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-compressed-video-type]
    jammy: [ros-humble-isaac-ros-nitros-compressed-video-type]
isaac_ros_nitros_correlated_timestamp_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-correlated-timestamp-type]
    jammy: [ros-humble-isaac-ros-nitros-correlated-timestamp-type]
isaac_ros_nitros_detection2_d_array_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-detection2-d-array-type]
    jammy: [ros-humble-isaac-ros-nitros-detection2-d-array-type]
isaac_ros_nitros_detection3_d_array_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-detection3-d-array-type]
    jammy: [ros-humble-isaac-ros-nitros-detection3-d-array-type]
isaac_ros_nitros_disparity_image_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-disparity-image-type]
    jammy: [ros-humble-isaac-ros-nitros-disparity-image-type]
isaac_ros_nitros_encoder_ticks_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-encoder-ticks-type]
    jammy: [ros-humble-isaac-ros-nitros-encoder-ticks-type]
isaac_ros_nitros_flat_scan_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-flat-scan-type]
    jammy: [ros-humble-isaac-ros-nitros-flat-scan-type]
isaac_ros_nitros_image_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-image-type]
    jammy: [ros-humble-isaac-ros-nitros-image-type]
isaac_ros_nitros_imu_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-imu-type]
    jammy: [ros-humble-isaac-ros-nitros-imu-type]
isaac_ros_nitros_msgs:
  ubuntu:
    focal: [ros-noetic-isaac-ros-nitros-msgs]
    jammy: [ros-noetic-isaac-ros-nitros-msgs]
isaac_ros_nitros_occupancy_grid_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-occupancy-grid-type]
    jammy: [ros-humble-isaac-ros-nitros-occupancy-grid-type]
isaac_ros_nitros_odometry_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-odometry-type]
    jammy: [ros-humble-isaac-ros-nitros-odometry-type]
isaac_ros_nitros_point_cloud_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-point-cloud-type]
    jammy: [ros-humble-isaac-ros-nitros-point-cloud-type]
isaac_ros_nitros_pose_array_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-pose-array-type]
    jammy: [ros-humble-isaac-ros-nitros-pose-array-type]
isaac_ros_nitros_pose_cov_stamped_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-pose-cov-stamped-type]
    jammy: [ros-humble-isaac-ros-nitros-pose-cov-stamped-type]
isaac_ros_nitros_std_msg_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-std-msg-type]
    jammy: [ros-humble-isaac-ros-nitros-std-msg-type]
isaac_ros_nitros_tensor_list_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-tensor-list-type]
    jammy: [ros-humble-isaac-ros-nitros-tensor-list-type]
isaac_ros_nitros_topic_tools:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-topic-tools]
    jammy: [ros-humble-isaac-ros-nitros-topic-tools]
isaac_ros_nitros_twist_type:
  ubuntu:
    focal: [ros-humble-isaac-ros-nitros-twist-type]
    jammy: [ros-humble-isaac-ros-nitros-twist-type]
isaac_ros_nova:
  ubuntu:
    focal: [ros-humble-isaac-ros-nova]
    jammy: [ros-humble-isaac-ros-nova]
isaac_ros_nova_interfaces:
  ubuntu:
    focal: [ros-humble-isaac-ros-nova-interfaces]
    jammy: [ros-humble-isaac-ros-nova-interfaces]
isaac_ros_nova_recorder:
  ubuntu:
    focal: [ros-humble-isaac-ros-nova-recorder]
    jammy: [ros-humble-isaac-ros-nova-recorder]
isaac_ros_nvblox:
  ubuntu:
    focal: [ros-humble-isaac-ros-nvblox]
    jammy: [ros-humble-isaac-ros-nvblox]
isaac_ros_nvblox_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-nvblox-benchmark]
    jammy: [ros-humble-isaac-ros-nvblox-benchmark]
isaac_ros_nvblox_nova_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-nvblox-nova-benchmark]
    jammy: [ros-humble-isaac-ros-nvblox-nova-benchmark]
isaac_ros_occupancy_grid_localizer:
  ubuntu:
    focal: [ros-humble-isaac-ros-occupancy-grid-localizer]
    jammy: [ros-humble-isaac-ros-occupancy-grid-localizer]
isaac_ros_occupancy_grid_localizer_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-occupancy-grid-localizer-benchmark]
    jammy: [ros-humble-isaac-ros-occupancy-grid-localizer-benchmark]
isaac_ros_occupancy_map_builder:
  ubuntu:
    focal: [ros-humble-isaac-ros-occupancy-map-builder]
    jammy: [ros-humble-isaac-ros-occupancy-map-builder]
isaac_ros_owl:
  ubuntu:
    focal: [ros-humble-isaac-ros-owl]
    jammy: [ros-humble-isaac-ros-owl]
isaac_ros_peoplenet_models_install:
  ubuntu:
    focal: [ros-humble-isaac-ros-peoplenet-models-install]
    jammy: [ros-humble-isaac-ros-peoplenet-models-install]
isaac_ros_peoplesemseg_models_install:
  ubuntu:
    focal: [ros-humble-isaac-ros-peoplesemseg-models-install]
    jammy: [ros-humble-isaac-ros-peoplesemseg-models-install]
isaac_ros_perceptor_bringup:
  ubuntu:
    focal: [ros-humble-isaac-ros-perceptor-bringup]
    jammy: [ros-humble-isaac-ros-perceptor-bringup]
isaac_ros_perceptor_python_utils:
  ubuntu:
    focal: [ros-humble-isaac-ros-perceptor-python-utils]
    jammy: [ros-humble-isaac-ros-perceptor-python-utils]
isaac_ros_perceptor_nova_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-perceptor-nova-benchmark]
    jammy: [ros-humble-isaac-ros-perceptor-nova-benchmark]
isaac_ros_pod_recording:
  ubuntu:
    focal: [ros-humble-isaac-ros-pod-recording]
    jammy: [ros-humble-isaac-ros-pod-recording]
isaac_ros_pointcloud_interfaces:
  ubuntu:
    focal: [ros-humble-isaac-ros-pointcloud-interfaces]
    jammy: [ros-humble-isaac-ros-pointcloud-interfaces]
isaac_ros_pointcloud_msgs:
  ubuntu:
    focal: [ros-noetic-isaac-ros-pointcloud-msgs]
    jammy: [ros-noetic-isaac-ros-pointcloud-msgs]
isaac_ros_pointcloud_utils:
  ubuntu:
    focal: [ros-humble-isaac-ros-pointcloud-utils]
    jammy: [ros-humble-isaac-ros-pointcloud-utils]
isaac_ros_pose_proc:
  ubuntu:
    focal: [ros-humble-isaac-ros-pose-proc]
    jammy: [ros-humble-isaac-ros-pose-proc]
isaac_ros_pynitros:
  ubuntu:
    focal: [ros-humble-isaac-ros-pynitros]
    jammy: [ros-humble-isaac-ros-pynitros]
isaac_ros_r2b_galileo:
  ubuntu:
    focal: [ros-humble-isaac-ros-r2b-galileo]
    jammy: [ros-humble-isaac-ros-r2b-galileo]
isaac_ros_realsense:
  ubuntu:
    focal: [ros-humble-isaac-ros-realsense]
    jammy: [ros-humble-isaac-ros-realsense]
isaac_ros_realsense_ess_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-realsense-ess-benchmark]
    jammy: [ros-humble-isaac-ros-realsense-ess-benchmark]
isaac_ros_ros1_converter:
  ubuntu:
    focal: [ros-humble-isaac-ros-ros1-converter]
    jammy: [ros-humble-isaac-ros-ros1-converter]
isaac_ros_ros1_forward:
  ubuntu:
    focal: [ros-humble-isaac-ros-ros1-forward]
    jammy: [ros-humble-isaac-ros-ros1-forward]
isaac_ros_ros2_converter:
  ubuntu:
    focal: [ros-humble-isaac-ros-ros2-converter]
    jammy: [ros-humble-isaac-ros-ros2-converter]
isaac_ros_rosbag_utils:
  ubuntu:
    focal: [ros-humble-isaac-ros-rosbag-utils]
    jammy: [ros-humble-isaac-ros-rosbag-utils]
isaac_ros_rtdetr:
  ubuntu:
    focal: [ros-humble-isaac-ros-rtdetr]
    jammy: [ros-humble-isaac-ros-rtdetr]
isaac_ros_rtdetr_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-rtdetr-benchmark]
    jammy: [ros-humble-isaac-ros-rtdetr-benchmark]
isaac_ros_scene_recorder:
  ubuntu:
    focal: [ros-humble-isaac-ros-scene-recorder]
    jammy: [ros-humble-isaac-ros-scene-recorder]
isaac_ros_segformer:
  ubuntu:
    focal: [ros-humble-isaac-ros-segformer]
    jammy: [ros-humble-isaac-ros-segformer]
isaac_ros_segformer_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-segformer-benchmark]
    jammy: [ros-humble-isaac-ros-segformer-benchmark]
isaac_ros_segment_anything:
  ubuntu:
    focal: [ros-humble-isaac-ros-segment-anything]
    jammy: [ros-humble-isaac-ros-segment-anything]
isaac_ros_segment_anything_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-segment-anything-benchmark]
    jammy: [ros-humble-isaac-ros-segment-anything-benchmark]
isaac_ros_segway_rmp:
  ubuntu:
    focal: [ros-humble-isaac-ros-segway-rmp]
    jammy: [ros-humble-isaac-ros-segway-rmp]
isaac_ros_stereo_image_proc:
  ubuntu:
    focal: [ros-humble-isaac-ros-stereo-image-proc]
    jammy: [ros-humble-isaac-ros-stereo-image-proc]
isaac_ros_stereo_image_proc_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-stereo-image-proc-benchmark]
    jammy: [ros-humble-isaac-ros-stereo-image-proc-benchmark]
isaac_ros_tensor_inspector:
  ubuntu:
    focal: [ros-humble-isaac-ros-tensor-inspector]
    jammy: [ros-humble-isaac-ros-tensor-inspector]
isaac_ros_tensor_list_interfaces:
  ubuntu:
    focal: [ros-humble-isaac-ros-tensor-list-interfaces]
    jammy: [ros-humble-isaac-ros-tensor-list-interfaces]
isaac_ros_tensor_list_msgs:
  ubuntu:
    focal: [ros-noetic-tensor-list-msgs]
    jammy: [ros-noetic-tensor-list-msgs]
isaac_ros_tensor_proc:
  ubuntu:
    focal: [ros-humble-isaac-ros-tensor-proc]
    jammy: [ros-humble-isaac-ros-tensor-proc]
isaac_ros_tensor_rt:
  ubuntu:
    focal: [ros-humble-isaac-ros-tensor-rt]
    jammy: [ros-humble-isaac-ros-tensor-rt]
isaac_ros_tensor_rt_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-tensor-rt-benchmark]
    jammy: [ros-humble-isaac-ros-tensor-rt-benchmark]
isaac_ros_test:
  ubuntu:
    focal: [ros-humble-isaac-ros-test]
    jammy: [ros-humble-isaac-ros-test]
isaac_ros_test_cmake:
  ubuntu:
    focal: [ros-humble-isaac-ros-test-cmake]
    jammy: [ros-humble-isaac-ros-test-cmake]
isaac_ros_triton:
  ubuntu:
    focal: [ros-humble-isaac-ros-triton]
    jammy: [ros-humble-isaac-ros-triton]
isaac_ros_triton_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-triton-benchmark]
    jammy: [ros-humble-isaac-ros-triton-benchmark]
isaac_ros_unet:
  ubuntu:
    focal: [ros-humble-isaac-ros-unet]
    jammy: [ros-humble-isaac-ros-unet]
isaac_ros_unet_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-unet-benchmark]
    jammy: [ros-humble-isaac-ros-unet-benchmark]
isaac_ros_ur5_cumotion_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-ur5-cumotion-benchmark]
    jammy: [ros-humble-isaac-ros-ur5-cumotion-benchmark]
isaac_ros_ur5_ompl_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-ur5-ompl-benchmark]
    jammy: [ros-humble-isaac-ros-ur5-ompl-benchmark]
isaac_ros_usb_cam:
  ubuntu:
    focal: [ros-humble-isaac-ros-usb-cam]
    jammy: [ros-humble-isaac-ros-usb-cam]
isaac_ros_vda5050_nav2_client:
  ubuntu:
    focal: [ros-humble-isaac-ros-vda5050-nav2-client]
    jammy: [ros-humble-isaac-ros-vda5050-nav2-client]
isaac_ros_vda5050_nav2_client_bringup:
  ubuntu:
    focal: [ros-humble-isaac-ros-vda5050-nav2-client-bringup]
    jammy: [ros-humble-isaac-ros-vda5050-nav2-client-bringup]
isaac_ros_visual_global_localization:
  ubuntu:
    focal: [ros-humble-isaac-ros-visual-global-localization]
    jammy: [ros-humble-isaac-ros-visual-global-localization]
isaac_ros_visual_slam:
  ubuntu:
    focal: [ros-humble-isaac-ros-visual-slam]
    jammy: [ros-humble-isaac-ros-visual-slam]
isaac_ros_visual_slam_benchmark:
  ubuntu:
    focal: [ros-humble-isaac-ros-visual-slam-benchmark]
    jammy: [ros-humble-isaac-ros-visual-slam-benchmark]
isaac_ros_visual_slam_interfaces:
  ubuntu:
    focal: [ros-humble-isaac-ros-visual-slam-interfaces]
    jammy: [ros-humble-isaac-ros-visual-slam-interfaces]
isaac_ros_visual_slam_msgs:
  ubuntu:
    focal: [ros-noetic-isaac-ros-visual-slam-msgs]
    jammy: [ros-noetic-isaac-ros-visual-slam-msgs]
isaac_ros_wifi_common:
  ubuntu:
    focal: [ros-humble-isaac-ros-wifi-common]
    jammy: [ros-humble-isaac-ros-wifi-common]
isaac_ros_wifi_localizer:
  ubuntu:
    focal: [ros-humble-isaac-ros-wifi-localizer]
    jammy: [ros-humble-isaac-ros-wifi-localizer]
isaac_ros_wifi_mapping:
  ubuntu:
    focal: [ros-humble-isaac-ros-wifi-mapping]
    jammy: [ros-humble-isaac-ros-wifi-mapping]
isaac_ros_wifi_scan:
  ubuntu:
    focal: [ros-humble-isaac-ros-wifi-scan]
    jammy: [ros-humble-isaac-ros-wifi-scan]
isaac_ros_wifi_scan_interfaces:
  ubuntu:
    focal: [ros-humble-isaac-ros-wifi-scan-interfaces]
    jammy: [ros-humble-isaac-ros-wifi-scan-interfaces]
isaac_ros_yolov8:
  ubuntu:
    focal: [ros-humble-isaac-ros-yolov8]
    jammy: [ros-humble-isaac-ros-yolov8]
isaac_ros_zed:
  ubuntu:
    focal: [ros-humble-isaac-ros-zed]
    jammy: [ros-humble-isaac-ros-zed]
isaac_ros_zed_test:
  ubuntu:
    focal: [ros-humble-isaac-ros-zed-test]
    jammy: [ros-humble-isaac-ros-zed-test]
joint_limits:
  ubuntu:
    focal: [ros-humble-joint-limits]
    jammy: [ros-humble-joint-limits]
joint_limits_dbgsym:
  ubuntu:
    focal: [ros-humble-joint-limits-dbgsym]
    jammy: [ros-humble-joint-limits-dbgsym]
joint_state_broadcaster:
  ubuntu:
    focal: [ros-humble-joint-state-broadcaster]
    jammy: [ros-humble-joint-state-broadcaster]
joint_state_broadcaster_dbgsym:
  ubuntu:
    focal: [ros-humble-joint-state-broadcaster-dbgsym]
    jammy: [ros-humble-joint-state-broadcaster-dbgsym]
joint_state_publisher:
  ubuntu:
    focal: [ros-humble-joint-state-publisher]
    jammy: [ros-humble-joint-state-publisher]
joint_state_publisher_gui:
  ubuntu:
    focal: [ros-humble-joint-state-publisher-gui]
    jammy: [ros-humble-joint-state-publisher-gui]
joint_trajectory_controller:
  ubuntu:
    focal: [ros-humble-joint-trajectory-controller]
    jammy: [ros-humble-joint-trajectory-controller]
joint_trajectory_controller_dbgsym:
  ubuntu:
    focal: [ros-humble-joint-trajectory-controller-dbgsym]
    jammy: [ros-humble-joint-trajectory-controller-dbgsym]
joy:
  ubuntu:
    focal: [ros-humble-joy]
    jammy: [ros-humble-joy]
joy_dbgsym:
  ubuntu:
    focal: [ros-humble-joy-dbgsym]
    jammy: [ros-humble-joy-dbgsym]
joy_linux:
  ubuntu:
    focal: [ros-humble-joy-linux]
    jammy: [ros-humble-joy-linux]
joy_linux_dbgsym:
  ubuntu:
    focal: [ros-humble-joy-linux-dbgsym]
    jammy: [ros-humble-joy-linux-dbgsym]
joy_teleop:
  ubuntu:
    focal: [ros-humble-joy-teleop]
    jammy: [ros-humble-joy-teleop]
joy_tester:
  ubuntu:
    focal: [ros-humble-joy-tester]
    jammy: [ros-humble-joy-tester]
kartech_linear_actuator_msgs:
  ubuntu:
    focal: [ros-humble-kartech-linear-actuator-msgs]
    jammy: [ros-humble-kartech-linear-actuator-msgs]
kartech_linear_actuator_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-kartech-linear-actuator-msgs-dbgsym]
    jammy: [ros-humble-kartech-linear-actuator-msgs-dbgsym]
kdl_parser:
  ubuntu:
    focal: [ros-humble-kdl-parser]
    jammy: [ros-humble-kdl-parser]
kdl_parser_dbgsym:
  ubuntu:
    focal: [ros-humble-kdl-parser-dbgsym]
    jammy: [ros-humble-kdl-parser-dbgsym]
key_teleop:
  ubuntu:
    focal: [ros-humble-key-teleop]
    jammy: [ros-humble-key-teleop]
keyboard_handler:
  ubuntu:
    focal: [ros-humble-keyboard-handler]
    jammy: [ros-humble-keyboard-handler]
keyboard_handler_dbgsym:
  ubuntu:
    focal: [ros-humble-keyboard-handler-dbgsym]
    jammy: [ros-humble-keyboard-handler-dbgsym]
kinematics_interface:
  ubuntu:
    focal: [ros-humble-kinematics-interface]
    jammy: [ros-humble-kinematics-interface]
kinematics_interface_kdl:
  ubuntu:
    focal: [ros-humble-kinematics-interface-kdl]
    jammy: [ros-humble-kinematics-interface-kdl]
kinematics_interface_kdl_dbgsym:
  ubuntu:
    focal: [ros-humble-kinematics-interface-kdl-dbgsym]
    jammy: [ros-humble-kinematics-interface-kdl-dbgsym]
kinova_gen3_6dof_robotiq_2f_85_moveit_config:
  ubuntu:
    focal: [ros-humble-kinova-gen3-6dof-robotiq-2f-85-moveit-config]
    jammy: [ros-humble-kinova-gen3-6dof-robotiq-2f-85-moveit-config]
kinova_gen3_7dof_robotiq_2f_85_moveit_config:
  ubuntu:
    focal: [ros-humble-kinova-gen3-7dof-robotiq-2f-85-moveit-config]
    jammy: [ros-humble-kinova-gen3-7dof-robotiq-2f-85-moveit-config]
kobuki_ros_interfaces:
  ubuntu:
    focal: [ros-humble-kobuki-ros-interfaces]
    jammy: [ros-humble-kobuki-ros-interfaces]
kobuki_ros_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-kobuki-ros-interfaces-dbgsym]
    jammy: [ros-humble-kobuki-ros-interfaces-dbgsym]
kobuki_velocity_smoother:
  ubuntu:
    focal: [ros-humble-kobuki-velocity-smoother]
    jammy: [ros-humble-kobuki-velocity-smoother]
kobuki_velocity_smoother_dbgsym:
  ubuntu:
    focal: [ros-humble-kobuki-velocity-smoother-dbgsym]
    jammy: [ros-humble-kobuki-velocity-smoother-dbgsym]
kortex_api:
  ubuntu:
    focal: [ros-humble-kortex-api]
    jammy: [ros-humble-kortex-api]
kortex_description:
  ubuntu:
    focal: [ros-humble-kortex-description]
    jammy: [ros-humble-kortex-description]
kortex_driver:
  ubuntu:
    focal: [ros-humble-kortex-driver]
    jammy: [ros-humble-kortex-driver]
kortex_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-kortex-driver-dbgsym]
    jammy: [ros-humble-kortex-driver-dbgsym]
lanelet2:
  ubuntu:
    focal: [ros-humble-lanelet2]
    jammy: [ros-humble-lanelet2]
lanelet2_core:
  ubuntu:
    focal: [ros-humble-lanelet2-core]
    jammy: [ros-humble-lanelet2-core]
lanelet2_core_dbgsym:
  ubuntu:
    focal: [ros-humble-lanelet2-core-dbgsym]
    jammy: [ros-humble-lanelet2-core-dbgsym]
lanelet2_examples:
  ubuntu:
    focal: [ros-humble-lanelet2-examples]
    jammy: [ros-humble-lanelet2-examples]
lanelet2_examples_dbgsym:
  ubuntu:
    focal: [ros-humble-lanelet2-examples-dbgsym]
    jammy: [ros-humble-lanelet2-examples-dbgsym]
lanelet2_io:
  ubuntu:
    focal: [ros-humble-lanelet2-io]
    jammy: [ros-humble-lanelet2-io]
lanelet2_io_dbgsym:
  ubuntu:
    focal: [ros-humble-lanelet2-io-dbgsym]
    jammy: [ros-humble-lanelet2-io-dbgsym]
lanelet2_maps:
  ubuntu:
    focal: [ros-humble-lanelet2-maps]
    jammy: [ros-humble-lanelet2-maps]
lanelet2_matching:
  ubuntu:
    focal: [ros-humble-lanelet2-matching]
    jammy: [ros-humble-lanelet2-matching]
lanelet2_matching_dbgsym:
  ubuntu:
    focal: [ros-humble-lanelet2-matching-dbgsym]
    jammy: [ros-humble-lanelet2-matching-dbgsym]
lanelet2_projection:
  ubuntu:
    focal: [ros-humble-lanelet2-projection]
    jammy: [ros-humble-lanelet2-projection]
lanelet2_projection_dbgsym:
  ubuntu:
    focal: [ros-humble-lanelet2-projection-dbgsym]
    jammy: [ros-humble-lanelet2-projection-dbgsym]
lanelet2_python:
  ubuntu:
    focal: [ros-humble-lanelet2-python]
    jammy: [ros-humble-lanelet2-python]
lanelet2_python_dbgsym:
  ubuntu:
    focal: [ros-humble-lanelet2-python-dbgsym]
    jammy: [ros-humble-lanelet2-python-dbgsym]
lanelet2_routing:
  ubuntu:
    focal: [ros-humble-lanelet2-routing]
    jammy: [ros-humble-lanelet2-routing]
lanelet2_routing_dbgsym:
  ubuntu:
    focal: [ros-humble-lanelet2-routing-dbgsym]
    jammy: [ros-humble-lanelet2-routing-dbgsym]
lanelet2_traffic_rules:
  ubuntu:
    focal: [ros-humble-lanelet2-traffic-rules]
    jammy: [ros-humble-lanelet2-traffic-rules]
lanelet2_traffic_rules_dbgsym:
  ubuntu:
    focal: [ros-humble-lanelet2-traffic-rules-dbgsym]
    jammy: [ros-humble-lanelet2-traffic-rules-dbgsym]
lanelet2_validation:
  ubuntu:
    focal: [ros-humble-lanelet2-validation]
    jammy: [ros-humble-lanelet2-validation]
lanelet2_validation_dbgsym:
  ubuntu:
    focal: [ros-humble-lanelet2-validation-dbgsym]
    jammy: [ros-humble-lanelet2-validation-dbgsym]
laser_filters:
  ubuntu:
    focal: [ros-humble-laser-filters]
    jammy: [ros-humble-laser-filters]
laser_filters_dbgsym:
  ubuntu:
    focal: [ros-humble-laser-filters-dbgsym]
    jammy: [ros-humble-laser-filters-dbgsym]
laser_geometry:
  ubuntu:
    focal: [ros-humble-laser-geometry]
    jammy: [ros-humble-laser-geometry]
laser_geometry_dbgsym:
  ubuntu:
    focal: [ros-humble-laser-geometry-dbgsym]
    jammy: [ros-humble-laser-geometry-dbgsym]
laser_proc:
  ubuntu:
    focal: [ros-humble-laser-proc]
    jammy: [ros-humble-laser-proc]
laser_proc_dbgsym:
  ubuntu:
    focal: [ros-humble-laser-proc-dbgsym]
    jammy: [ros-humble-laser-proc-dbgsym]
launch:
  ubuntu:
    focal: [ros-humble-launch]
    jammy: [ros-humble-launch]
launch_pal:
  ubuntu:
    focal: [ros-humble-launch-pal]
    jammy: [ros-humble-launch-pal]
launch_param_builder:
  ubuntu:
    focal: [ros-humble-launch-param-builder]
    jammy: [ros-humble-launch-param-builder]
launch_pytest:
  ubuntu:
    focal: [ros-humble-launch-pytest]
    jammy: [ros-humble-launch-pytest]
launch_ros:
  ubuntu:
    focal: [ros-humble-launch-ros]
    jammy: [ros-humble-launch-ros]
launch_system_modes:
  ubuntu:
    focal: [ros-humble-launch-system-modes]
    jammy: [ros-humble-launch-system-modes]
launch_testing:
  ubuntu:
    focal: [ros-humble-launch-testing]
    jammy: [ros-humble-launch-testing]
launch_testing_ament_cmake:
  ubuntu:
    focal: [ros-humble-launch-testing-ament-cmake]
    jammy: [ros-humble-launch-testing-ament-cmake]
launch_testing_examples:
  ubuntu:
    focal: [ros-humble-launch-testing-examples]
    jammy: [ros-humble-launch-testing-examples]
launch_testing_ros:
  ubuntu:
    focal: [ros-humble-launch-testing-ros]
    jammy: [ros-humble-launch-testing-ros]
launch_xml:
  ubuntu:
    focal: [ros-humble-launch-xml]
    jammy: [ros-humble-launch-xml]
launch_yaml:
  ubuntu:
    focal: [ros-humble-launch-yaml]
    jammy: [ros-humble-launch-yaml]
leo:
  ubuntu:
    focal: [ros-humble-leo]
    jammy: [ros-humble-leo]
leo_bringup:
  ubuntu:
    focal: [ros-humble-leo-bringup]
    jammy: [ros-humble-leo-bringup]
leo_description:
  ubuntu:
    focal: [ros-humble-leo-description]
    jammy: [ros-humble-leo-description]
leo_desktop:
  ubuntu:
    focal: [ros-humble-leo-desktop]
    jammy: [ros-humble-leo-desktop]
leo_fw:
  ubuntu:
    focal: [ros-humble-leo-fw]
    jammy: [ros-humble-leo-fw]
leo_fw_dbgsym:
  ubuntu:
    focal: [ros-humble-leo-fw-dbgsym]
    jammy: [ros-humble-leo-fw-dbgsym]
leo_msgs:
  ubuntu:
    focal: [ros-humble-leo-msgs]
    jammy: [ros-humble-leo-msgs]
leo_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-leo-msgs-dbgsym]
    jammy: [ros-humble-leo-msgs-dbgsym]
leo_robot:
  ubuntu:
    focal: [ros-humble-leo-robot]
    jammy: [ros-humble-leo-robot]
leo_teleop:
  ubuntu:
    focal: [ros-humble-leo-teleop]
    jammy: [ros-humble-leo-teleop]
leo_viz:
  ubuntu:
    focal: [ros-humble-leo-viz]
    jammy: [ros-humble-leo-viz]
lgsvl_msgs:
  ubuntu:
    focal: [ros-humble-lgsvl-msgs]
    jammy: [ros-humble-lgsvl-msgs]
lgsvl_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-lgsvl-msgs-dbgsym]
    jammy: [ros-humble-lgsvl-msgs-dbgsym]
libcamera:
  ubuntu:
    focal: [ros-humble-libcamera]
    jammy: [ros-humble-libcamera]
libcreate:
  ubuntu:
    focal: [ros-humble-libcreate]
    jammy: [ros-humble-libcreate]
libcreate_dbgsym:
  ubuntu:
    focal: [ros-humble-libcreate-dbgsym]
    jammy: [ros-humble-libcreate-dbgsym]
libcurl_vendor:
  ubuntu:
    focal: [ros-humble-libcurl-vendor]
    jammy: [ros-humble-libcurl-vendor]
libg2o:
  ubuntu:
    focal: [ros-humble-libg2o]
    jammy: [ros-humble-libg2o]
libg2o_dbgsym:
  ubuntu:
    focal: [ros-humble-libg2o-dbgsym]
    jammy: [ros-humble-libg2o-dbgsym]
libmavconn:
  ubuntu:
    focal: [ros-humble-libmavconn]
    jammy: [ros-humble-libmavconn]
libmavconn_dbgsym:
  ubuntu:
    focal: [ros-humble-libmavconn-dbgsym]
    jammy: [ros-humble-libmavconn-dbgsym]
libnabo:
  ubuntu:
    focal: [ros-humble-libnabo]
    jammy: [ros-humble-libnabo]
libopenvdb:
  ubuntu:
    jammy: [openvdb]
libopenvdb-dev:
  ubuntu:
    jammy: [libilmbase-dev]
libphidget22:
  ubuntu:
    focal: [ros-humble-libphidget22]
    jammy: [ros-humble-libphidget22]
libphidget22_dbgsym:
  ubuntu:
    focal: [ros-humble-libphidget22-dbgsym]
    jammy: [ros-humble-libphidget22-dbgsym]
libpointmatcher:
  ubuntu:
    focal: [ros-humble-libpointmatcher]
    jammy: [ros-humble-libpointmatcher]
libpointmatcher_dbgsym:
  ubuntu:
    focal: [ros-humble-libpointmatcher-dbgsym]
    jammy: [ros-humble-libpointmatcher-dbgsym]
librealsense2:
  ubuntu:
    focal: [ros-humble-librealsense2]
    jammy: [ros-humble-librealsense2]
librealsense2_dbgsym:
  ubuntu:
    focal: [ros-humble-librealsense2-dbgsym]
    jammy: [ros-humble-librealsense2-dbgsym]
libstatistics_collector:
  ubuntu:
    focal: [ros-humble-libstatistics-collector]
    jammy: [ros-humble-libstatistics-collector]
libstatistics_collector_dbgsym:
  ubuntu:
    focal: [ros-humble-libstatistics-collector-dbgsym]
    jammy: [ros-humble-libstatistics-collector-dbgsym]
libyaml_vendor:
  ubuntu:
    focal: [ros-humble-libyaml-vendor]
    jammy: [ros-humble-libyaml-vendor]
libyaml_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-libyaml-vendor-dbgsym]
    jammy: [ros-humble-libyaml-vendor-dbgsym]
lifecycle:
  ubuntu:
    focal: [ros-humble-lifecycle]
    jammy: [ros-humble-lifecycle]
lifecycle_dbgsym:
  ubuntu:
    focal: [ros-humble-lifecycle-dbgsym]
    jammy: [ros-humble-lifecycle-dbgsym]
lifecycle_msgs:
  ubuntu:
    focal: [ros-humble-lifecycle-msgs]
    jammy: [ros-humble-lifecycle-msgs]
lifecycle_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-lifecycle-msgs-dbgsym]
    jammy: [ros-humble-lifecycle-msgs-dbgsym]
lifecycle_py:
  ubuntu:
    focal: [ros-humble-lifecycle-py]
    jammy: [ros-humble-lifecycle-py]
lms1xx:
  ubuntu:
    focal: [ros-humble-lms1xx]
    jammy: [ros-humble-lms1xx]
lms1xx_dbgsym:
  ubuntu:
    focal: [ros-humble-lms1xx-dbgsym]
    jammy: [ros-humble-lms1xx-dbgsym]
logging_demo:
  ubuntu:
    focal: [ros-humble-logging-demo]
    jammy: [ros-humble-logging-demo]
logging_demo_dbgsym:
  ubuntu:
    focal: [ros-humble-logging-demo-dbgsym]
    jammy: [ros-humble-logging-demo-dbgsym]
lsc_ros2_driver:
  ubuntu:
    focal: [ros-humble-lsc-ros2-driver]
    jammy: [ros-humble-lsc-ros2-driver]
lsc_ros2_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-lsc-ros2-driver-dbgsym]
    jammy: [ros-humble-lsc-ros2-driver-dbgsym]
lusb:
  ubuntu:
    focal: [ros-humble-lusb]
    jammy: [ros-humble-lusb]
lusb_dbgsym:
  ubuntu:
    focal: [ros-humble-lusb-dbgsym]
    jammy: [ros-humble-lusb-dbgsym]
magic_enum:
  ubuntu:
    focal: [ros-humble-magic-enum]
    jammy: [ros-humble-magic-enum]
magic_enum:
  ubuntu:
    focal: [ros-humble-magic-enum]
    jammy: [ros-humble-magic-enum]
map_msgs:
  ubuntu:
    focal: [ros-humble-map-msgs]
    jammy: [ros-humble-map-msgs]
map_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-map-msgs-dbgsym]
    jammy: [ros-humble-map-msgs-dbgsym]
mapviz_interfaces:
  ubuntu:
    focal: [ros-humble-mapviz-interfaces]
    jammy: [ros-humble-mapviz-interfaces]
mapviz_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-mapviz-interfaces-dbgsym]
    jammy: [ros-humble-mapviz-interfaces-dbgsym]
marker_msgs:
  ubuntu:
    focal: [ros-humble-marker-msgs]
    jammy: [ros-humble-marker-msgs]
marker_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-marker-msgs-dbgsym]
    jammy: [ros-humble-marker-msgs-dbgsym]
marti_can_msgs:
  ubuntu:
    focal: [ros-humble-marti-can-msgs]
    jammy: [ros-humble-marti-can-msgs]
marti_can_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-marti-can-msgs-dbgsym]
    jammy: [ros-humble-marti-can-msgs-dbgsym]
marti_common_msgs:
  ubuntu:
    focal: [ros-humble-marti-common-msgs]
    jammy: [ros-humble-marti-common-msgs]
marti_common_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-marti-common-msgs-dbgsym]
    jammy: [ros-humble-marti-common-msgs-dbgsym]
marti_dbw_msgs:
  ubuntu:
    focal: [ros-humble-marti-dbw-msgs]
    jammy: [ros-humble-marti-dbw-msgs]
marti_dbw_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-marti-dbw-msgs-dbgsym]
    jammy: [ros-humble-marti-dbw-msgs-dbgsym]
marti_introspection_msgs:
  ubuntu:
    focal: [ros-humble-marti-introspection-msgs]
    jammy: [ros-humble-marti-introspection-msgs]
marti_introspection_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-marti-introspection-msgs-dbgsym]
    jammy: [ros-humble-marti-introspection-msgs-dbgsym]
marti_nav_msgs:
  ubuntu:
    focal: [ros-humble-marti-nav-msgs]
    jammy: [ros-humble-marti-nav-msgs]
marti_nav_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-marti-nav-msgs-dbgsym]
    jammy: [ros-humble-marti-nav-msgs-dbgsym]
marti_perception_msgs:
  ubuntu:
    focal: [ros-humble-marti-perception-msgs]
    jammy: [ros-humble-marti-perception-msgs]
marti_perception_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-marti-perception-msgs-dbgsym]
    jammy: [ros-humble-marti-perception-msgs-dbgsym]
marti_sensor_msgs:
  ubuntu:
    focal: [ros-humble-marti-sensor-msgs]
    jammy: [ros-humble-marti-sensor-msgs]
marti_sensor_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-marti-sensor-msgs-dbgsym]
    jammy: [ros-humble-marti-sensor-msgs-dbgsym]
marti_status_msgs:
  ubuntu:
    focal: [ros-humble-marti-status-msgs]
    jammy: [ros-humble-marti-status-msgs]
marti_status_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-marti-status-msgs-dbgsym]
    jammy: [ros-humble-marti-status-msgs-dbgsym]
marti_visualization_msgs:
  ubuntu:
    focal: [ros-humble-marti-visualization-msgs]
    jammy: [ros-humble-marti-visualization-msgs]
marti_visualization_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-marti-visualization-msgs-dbgsym]
    jammy: [ros-humble-marti-visualization-msgs-dbgsym]
marvelmind_ros2:
  ubuntu:
    focal: [ros-humble-marvelmind-ros2]
    jammy: [ros-humble-marvelmind-ros2]
marvelmind_ros2_dbgsym:
  ubuntu:
    focal: [ros-humble-marvelmind-ros2-dbgsym]
    jammy: [ros-humble-marvelmind-ros2-dbgsym]
marvelmind_ros2_msgs:
  ubuntu:
    focal: [ros-humble-marvelmind-ros2-msgs]
    jammy: [ros-humble-marvelmind-ros2-msgs]
marvelmind_ros2_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-marvelmind-ros2-msgs-dbgsym]
    jammy: [ros-humble-marvelmind-ros2-msgs-dbgsym]
mavlink:
  ubuntu:
    focal: [ros-humble-mavlink]
    jammy: [ros-humble-mavlink]
mavros:
  ubuntu:
    focal: [ros-humble-mavros]
    jammy: [ros-humble-mavros]
mavros_dbgsym:
  ubuntu:
    focal: [ros-humble-mavros-dbgsym]
    jammy: [ros-humble-mavros-dbgsym]
mavros_extras:
  ubuntu:
    focal: [ros-humble-mavros-extras]
    jammy: [ros-humble-mavros-extras]
mavros_extras_dbgsym:
  ubuntu:
    focal: [ros-humble-mavros-extras-dbgsym]
    jammy: [ros-humble-mavros-extras-dbgsym]
mavros_msgs:
  ubuntu:
    focal: [ros-humble-mavros-msgs]
    jammy: [ros-humble-mavros-msgs]
mavros_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-mavros-msgs-dbgsym]
    jammy: [ros-humble-mavros-msgs-dbgsym]
mcap_vendor:
  ubuntu:
    focal: [ros-humble-mcap-vendor]
    jammy: [ros-humble-mcap-vendor]
mcap_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-mcap-vendor-dbgsym]
    jammy: [ros-humble-mcap-vendor-dbgsym]
menge_vendor:
  ubuntu:
    focal: [ros-humble-menge-vendor]
    jammy: [ros-humble-menge-vendor]
menge_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-menge-vendor-dbgsym]
    jammy: [ros-humble-menge-vendor-dbgsym]
message_filters:
  ubuntu:
    focal: [ros-humble-message-filters]
    jammy: [ros-humble-message-filters]
message_filters_dbgsym:
  ubuntu:
    focal: [ros-humble-message-filters-dbgsym]
    jammy: [ros-humble-message-filters-dbgsym]
message_generation:
  ubuntu:
    focal: [ros-noetic-message-generation]
    jammy: [ros-noetic-message-generation]
message_runtime:
  ubuntu:
    focal: [ros-noetic-message-runtime]
    jammy: [ros-noetic-message-runtime]
message_tf_frame_transformer:
  ubuntu:
    focal: [ros-humble-message-tf-frame-transformer]
    jammy: [ros-humble-message-tf-frame-transformer]
message_tf_frame_transformer_dbgsym:
  ubuntu:
    focal: [ros-humble-message-tf-frame-transformer-dbgsym]
    jammy: [ros-humble-message-tf-frame-transformer-dbgsym]
metavision_driver:
  ubuntu:
    focal: [ros-humble-metavision-driver]
    jammy: [ros-humble-metavision-driver]
metavision_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-metavision-driver-dbgsym]
    jammy: [ros-humble-metavision-driver-dbgsym]
metrics_plugins:
  ubuntu:
    focal: [ros-humble-metrics-plugins]
    jammy: [ros-humble-metrics-plugins]
metrics_plugins:
  ubuntu:
    focal: [ros-humble-metrics-plugins]
    jammy: [ros-humble-metrics-plugins]
micro_ros_diagnostic_bridge:
  ubuntu:
    focal: [ros-humble-micro-ros-diagnostic-bridge]
    jammy: [ros-humble-micro-ros-diagnostic-bridge]
micro_ros_diagnostic_bridge_dbgsym:
  ubuntu:
    focal: [ros-humble-micro-ros-diagnostic-bridge-dbgsym]
    jammy: [ros-humble-micro-ros-diagnostic-bridge-dbgsym]
micro_ros_diagnostic_msgs:
  ubuntu:
    focal: [ros-humble-micro-ros-diagnostic-msgs]
    jammy: [ros-humble-micro-ros-diagnostic-msgs]
micro_ros_diagnostic_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-micro-ros-diagnostic-msgs-dbgsym]
    jammy: [ros-humble-micro-ros-diagnostic-msgs-dbgsym]
micro_ros_msgs:
  ubuntu:
    focal: [ros-humble-micro-ros-msgs]
    jammy: [ros-humble-micro-ros-msgs]
micro_ros_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-micro-ros-msgs-dbgsym]
    jammy: [ros-humble-micro-ros-msgs-dbgsym]
microstrain_inertial_driver:
  ubuntu:
    focal: [ros-humble-microstrain-inertial-driver]
    jammy: [ros-humble-microstrain-inertial-driver]
microstrain_inertial_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-microstrain-inertial-driver-dbgsym]
    jammy: [ros-humble-microstrain-inertial-driver-dbgsym]
microstrain_inertial_examples:
  ubuntu:
    focal: [ros-humble-microstrain-inertial-examples]
    jammy: [ros-humble-microstrain-inertial-examples]
microstrain_inertial_examples_dbgsym:
  ubuntu:
    focal: [ros-humble-microstrain-inertial-examples-dbgsym]
    jammy: [ros-humble-microstrain-inertial-examples-dbgsym]
microstrain_inertial_msgs:
  ubuntu:
    focal: [ros-humble-microstrain-inertial-msgs]
    jammy: [ros-humble-microstrain-inertial-msgs]
microstrain_inertial_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-microstrain-inertial-msgs-dbgsym]
    jammy: [ros-humble-microstrain-inertial-msgs-dbgsym]
microstrain_inertial_rqt:
  ubuntu:
    focal: [ros-humble-microstrain-inertial-rqt]
    jammy: [ros-humble-microstrain-inertial-rqt]
mimick_vendor:
  ubuntu:
    focal: [ros-humble-mimick-vendor]
    jammy: [ros-humble-mimick-vendor]
mission_monitor:
  ubuntu:
    focal: [ros-humble-mission-monitor]
    jammy: [ros-humble-mission-monitor]
mobileye_560_660_msgs:
  ubuntu:
    focal: [ros-humble-mobileye-560-660-msgs]
    jammy: [ros-humble-mobileye-560-660-msgs]
mobileye_560_660_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-mobileye-560-660-msgs-dbgsym]
    jammy: [ros-humble-mobileye-560-660-msgs-dbgsym]
mod:
  ubuntu:
    focal: [ros-humble-mod]
    jammy: [ros-humble-mod]
mod_dbgsym:
  ubuntu:
    focal: [ros-humble-mod-dbgsym]
    jammy: [ros-humble-mod-dbgsym]
mola_common:
  ubuntu:
    focal: [ros-humble-mola-common]
    jammy: [ros-humble-mola-common]
mola_demos:
  ubuntu:
    focal: [ros-humble-mola-demos]
    jammy: [ros-humble-mola-demos]
mola_test_datasets:
  ubuntu:
    focal: [ros-humble-mola-test-datasets]
    jammy: [ros-humble-mola-test-datasets]
mouse_teleop:
  ubuntu:
    focal: [ros-humble-mouse-teleop]
    jammy: [ros-humble-mouse-teleop]
moveit:
  ubuntu:
    focal: [ros-humble-moveit]
    jammy: [ros-humble-moveit]
moveit2_tutorials:
  ubuntu:
    focal: [ros-humble-moveit2-tutorials]
    jammy: [ros-humble-moveit2-tutorials]
moveit_chomp_optimizer_adapter:
  ubuntu:
    focal: [ros-humble-moveit-chomp-optimizer-adapter]
    jammy: [ros-humble-moveit-chomp-optimizer-adapter]
moveit_chomp_optimizer_adapter_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-chomp-optimizer-adapter-dbgsym]
    jammy: [ros-humble-moveit-chomp-optimizer-adapter-dbgsym]
moveit_common:
  ubuntu:
    focal: [ros-humble-moveit-common]
    jammy: [ros-humble-moveit-common]
moveit_configs_utils:
  ubuntu:
    focal: [ros-humble-moveit-configs-utils]
    jammy: [ros-humble-moveit-configs-utils]
moveit_core:
  ubuntu:
    focal: [ros-humble-moveit-core]
    jammy: [ros-humble-moveit-core]
moveit_core_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-core-dbgsym]
    jammy: [ros-humble-moveit-core-dbgsym]
moveit_hybrid_planning:
  ubuntu:
    focal: [ros-humble-moveit-hybrid-planning]
    jammy: [ros-humble-moveit-hybrid-planning]
moveit_hybrid_planning_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-hybrid-planning-dbgsym]
    jammy: [ros-humble-moveit-hybrid-planning-dbgsym]
moveit_kinematics:
  ubuntu:
    focal: [ros-humble-moveit-kinematics]
    jammy: [ros-humble-moveit-kinematics]
moveit_kinematics_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-kinematics-dbgsym]
    jammy: [ros-humble-moveit-kinematics-dbgsym]
moveit_msgs:
  ubuntu:
    focal: [ros-humble-moveit-msgs]
    jammy: [ros-humble-moveit-msgs]
moveit_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-msgs-dbgsym]
    jammy: [ros-humble-moveit-msgs-dbgsym]
moveit_planners:
  ubuntu:
    focal: [ros-humble-moveit-planners]
    jammy: [ros-humble-moveit-planners]
moveit_planners_chomp:
  ubuntu:
    focal: [ros-humble-moveit-planners-chomp]
    jammy: [ros-humble-moveit-planners-chomp]
moveit_planners_chomp_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-planners-chomp-dbgsym]
    jammy: [ros-humble-moveit-planners-chomp-dbgsym]
moveit_planners_ompl:
  ubuntu:
    focal: [ros-humble-moveit-planners-ompl]
    jammy: [ros-humble-moveit-planners-ompl]
moveit_planners_ompl_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-planners-ompl-dbgsym]
    jammy: [ros-humble-moveit-planners-ompl-dbgsym]
moveit_plugins:
  ubuntu:
    focal: [ros-humble-moveit-plugins]
    jammy: [ros-humble-moveit-plugins]
moveit_resources:
  ubuntu:
    focal: [ros-humble-moveit-resources]
    jammy: [ros-humble-moveit-resources]
moveit_resources_fanuc_description:
  ubuntu:
    focal: [ros-humble-moveit-resources-fanuc-description]
    jammy: [ros-humble-moveit-resources-fanuc-description]
moveit_resources_fanuc_moveit_config:
  ubuntu:
    focal: [ros-humble-moveit-resources-fanuc-moveit-config]
    jammy: [ros-humble-moveit-resources-fanuc-moveit-config]
moveit_resources_panda_description:
  ubuntu:
    focal: [ros-humble-moveit-resources-panda-description]
    jammy: [ros-humble-moveit-resources-panda-description]
moveit_resources_panda_moveit_config:
  ubuntu:
    focal: [ros-humble-moveit-resources-panda-moveit-config]
    jammy: [ros-humble-moveit-resources-panda-moveit-config]
moveit_resources_pr2_description:
  ubuntu:
    focal: [ros-humble-moveit-resources-pr2-description]
    jammy: [ros-humble-moveit-resources-pr2-description]
moveit_resources_prbt_ikfast_manipulator_plugin:
  ubuntu:
    focal: [ros-humble-moveit-resources-prbt-ikfast-manipulator-plugin]
    jammy: [ros-humble-moveit-resources-prbt-ikfast-manipulator-plugin]
moveit_resources_prbt_ikfast_manipulator_plugin_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-resources-prbt-ikfast-manipulator-plugin-dbgsym]
    jammy: [ros-humble-moveit-resources-prbt-ikfast-manipulator-plugin-dbgsym]
moveit_resources_prbt_moveit_config:
  ubuntu:
    focal: [ros-humble-moveit-resources-prbt-moveit-config]
    jammy: [ros-humble-moveit-resources-prbt-moveit-config]
moveit_resources_prbt_pg70_support:
  ubuntu:
    focal: [ros-humble-moveit-resources-prbt-pg70-support]
    jammy: [ros-humble-moveit-resources-prbt-pg70-support]
moveit_resources_prbt_support:
  ubuntu:
    focal: [ros-humble-moveit-resources-prbt-support]
    jammy: [ros-humble-moveit-resources-prbt-support]
moveit_ros:
  ubuntu:
    focal: [ros-humble-moveit-ros]
    jammy: [ros-humble-moveit-ros]
moveit_ros_benchmarks:
  ubuntu:
    focal: [ros-humble-moveit-ros-benchmarks]
    jammy: [ros-humble-moveit-ros-benchmarks]
moveit_ros_benchmarks_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-ros-benchmarks-dbgsym]
    jammy: [ros-humble-moveit-ros-benchmarks-dbgsym]
moveit_ros_control_interface:
  ubuntu:
    focal: [ros-humble-moveit-ros-control-interface]
    jammy: [ros-humble-moveit-ros-control-interface]
moveit_ros_control_interface_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-ros-control-interface-dbgsym]
    jammy: [ros-humble-moveit-ros-control-interface-dbgsym]
moveit_ros_move_group:
  ubuntu:
    focal: [ros-humble-moveit-ros-move-group]
    jammy: [ros-humble-moveit-ros-move-group]
moveit_ros_move_group_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-ros-move-group-dbgsym]
    jammy: [ros-humble-moveit-ros-move-group-dbgsym]
moveit_ros_occupancy_map_monitor:
  ubuntu:
    focal: [ros-humble-moveit-ros-occupancy-map-monitor]
    jammy: [ros-humble-moveit-ros-occupancy-map-monitor]
moveit_ros_occupancy_map_monitor_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-ros-occupancy-map-monitor-dbgsym]
    jammy: [ros-humble-moveit-ros-occupancy-map-monitor-dbgsym]
moveit_ros_perception:
  ubuntu:
    focal: [ros-humble-moveit-ros-perception]
    jammy: [ros-humble-moveit-ros-perception]
moveit_ros_perception_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-ros-perception-dbgsym]
    jammy: [ros-humble-moveit-ros-perception-dbgsym]
moveit_ros_planning:
  ubuntu:
    focal: [ros-humble-moveit-ros-planning]
    jammy: [ros-humble-moveit-ros-planning]
moveit_ros_planning_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-ros-planning-dbgsym]
    jammy: [ros-humble-moveit-ros-planning-dbgsym]
moveit_ros_planning_interface:
  ubuntu:
    focal: [ros-humble-moveit-ros-planning-interface]
    jammy: [ros-humble-moveit-ros-planning-interface]
moveit_ros_planning_interface_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-ros-planning-interface-dbgsym]
    jammy: [ros-humble-moveit-ros-planning-interface-dbgsym]
moveit_ros_robot_interaction:
  ubuntu:
    focal: [ros-humble-moveit-ros-robot-interaction]
    jammy: [ros-humble-moveit-ros-robot-interaction]
moveit_ros_robot_interaction_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-ros-robot-interaction-dbgsym]
    jammy: [ros-humble-moveit-ros-robot-interaction-dbgsym]
moveit_ros_visualization:
  ubuntu:
    focal: [ros-humble-moveit-ros-visualization]
    jammy: [ros-humble-moveit-ros-visualization]
moveit_ros_visualization_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-ros-visualization-dbgsym]
    jammy: [ros-humble-moveit-ros-visualization-dbgsym]
moveit_ros_warehouse:
  ubuntu:
    focal: [ros-humble-moveit-ros-warehouse]
    jammy: [ros-humble-moveit-ros-warehouse]
moveit_ros_warehouse_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-ros-warehouse-dbgsym]
    jammy: [ros-humble-moveit-ros-warehouse-dbgsym]
moveit_runtime:
  ubuntu:
    focal: [ros-humble-moveit-runtime]
    jammy: [ros-humble-moveit-runtime]
moveit_servo:
  ubuntu:
    focal: [ros-humble-moveit-servo]
    jammy: [ros-humble-moveit-servo]
moveit_servo_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-servo-dbgsym]
    jammy: [ros-humble-moveit-servo-dbgsym]
moveit_setup_app_plugins:
  ubuntu:
    focal: [ros-humble-moveit-setup-app-plugins]
    jammy: [ros-humble-moveit-setup-app-plugins]
moveit_setup_app_plugins_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-setup-app-plugins-dbgsym]
    jammy: [ros-humble-moveit-setup-app-plugins-dbgsym]
moveit_setup_assistant:
  ubuntu:
    focal: [ros-humble-moveit-setup-assistant]
    jammy: [ros-humble-moveit-setup-assistant]
moveit_setup_assistant_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-setup-assistant-dbgsym]
    jammy: [ros-humble-moveit-setup-assistant-dbgsym]
moveit_setup_controllers:
  ubuntu:
    focal: [ros-humble-moveit-setup-controllers]
    jammy: [ros-humble-moveit-setup-controllers]
moveit_setup_controllers_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-setup-controllers-dbgsym]
    jammy: [ros-humble-moveit-setup-controllers-dbgsym]
moveit_setup_core_plugins:
  ubuntu:
    focal: [ros-humble-moveit-setup-core-plugins]
    jammy: [ros-humble-moveit-setup-core-plugins]
moveit_setup_core_plugins_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-setup-core-plugins-dbgsym]
    jammy: [ros-humble-moveit-setup-core-plugins-dbgsym]
moveit_setup_framework:
  ubuntu:
    focal: [ros-humble-moveit-setup-framework]
    jammy: [ros-humble-moveit-setup-framework]
moveit_setup_framework_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-setup-framework-dbgsym]
    jammy: [ros-humble-moveit-setup-framework-dbgsym]
moveit_setup_srdf_plugins:
  ubuntu:
    focal: [ros-humble-moveit-setup-srdf-plugins]
    jammy: [ros-humble-moveit-setup-srdf-plugins]
moveit_setup_srdf_plugins_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-setup-srdf-plugins-dbgsym]
    jammy: [ros-humble-moveit-setup-srdf-plugins-dbgsym]
moveit_simple_controller_manager:
  ubuntu:
    focal: [ros-humble-moveit-simple-controller-manager]
    jammy: [ros-humble-moveit-simple-controller-manager]
moveit_simple_controller_manager_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-simple-controller-manager-dbgsym]
    jammy: [ros-humble-moveit-simple-controller-manager-dbgsym]
moveit_task_constructor_capabilities:
  ubuntu:
    focal: [ros-humble-moveit-task-constructor-capabilities]
    jammy: [ros-humble-moveit-task-constructor-capabilities]
moveit_task_constructor_core:
  ubuntu:
    focal: [ros-humble-moveit-task-constructor-core]
    jammy: [ros-humble-moveit-task-constructor-core]
moveit_task_constructor_demo:
  ubuntu:
    focal: [ros-humble-moveit-task-constructor-demo]
    jammy: [ros-humble-moveit-task-constructor-demo]
moveit_task_constructor_msgs:
  ubuntu:
    focal: [ros-humble-moveit-task-constructor-msgs]
    jammy: [ros-humble-moveit-task-constructor-msgs]
moveit_task_constructor_visualization:
  ubuntu:
    focal: [ros-humble-moveit-task-constructor-visualization]
    jammy: [ros-humble-moveit-task-constructor-visualization]
moveit_visual_tools:
  ubuntu:
    focal: [ros-humble-moveit-visual-tools]
    jammy: [ros-humble-moveit-visual-tools]
moveit_visual_tools_dbgsym:
  ubuntu:
    focal: [ros-humble-moveit-visual-tools-dbgsym]
    jammy: [ros-humble-moveit-visual-tools-dbgsym]
mqtt_client:
  ubuntu:
    focal: [ros-humble-mqtt-client]
    jammy: [ros-humble-mqtt-client]
mqtt_client_dbgsym:
  ubuntu:
    focal: [ros-humble-mqtt-client-dbgsym]
    jammy: [ros-humble-mqtt-client-dbgsym]
mqtt_client_interfaces:
  ubuntu:
    focal: [ros-humble-mqtt-client-interfaces]
    jammy: [ros-humble-mqtt-client-interfaces]
mqtt_client_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-mqtt-client-interfaces-dbgsym]
    jammy: [ros-humble-mqtt-client-interfaces-dbgsym]
mrpt_msgs:
  ubuntu:
    focal: [ros-humble-mrpt-msgs]
    jammy: [ros-humble-mrpt-msgs]
mrpt_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-mrpt-msgs-dbgsym]
    jammy: [ros-humble-mrpt-msgs-dbgsym]
mrt_cmake_modules:
  ubuntu:
    focal: [ros-humble-mrt-cmake-modules]
    jammy: [ros-humble-mrt-cmake-modules]
nao_button_sim:
  ubuntu:
    focal: [ros-humble-nao-button-sim]
    jammy: [ros-humble-nao-button-sim]
nao_command_msgs:
  ubuntu:
    focal: [ros-humble-nao-command-msgs]
    jammy: [ros-humble-nao-command-msgs]
nao_command_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-nao-command-msgs-dbgsym]
    jammy: [ros-humble-nao-command-msgs-dbgsym]
nao_lola:
  ubuntu:
    focal: [ros-humble-nao-lola]
    jammy: [ros-humble-nao-lola]
nao_lola_dbgsym:
  ubuntu:
    focal: [ros-humble-nao-lola-dbgsym]
    jammy: [ros-humble-nao-lola-dbgsym]
nao_sensor_msgs:
  ubuntu:
    focal: [ros-humble-nao-sensor-msgs]
    jammy: [ros-humble-nao-sensor-msgs]
nao_sensor_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-nao-sensor-msgs-dbgsym]
    jammy: [ros-humble-nao-sensor-msgs-dbgsym]
nav2_amcl:
  ubuntu:
    focal: [ros-humble-nav2-amcl]
    jammy: [ros-humble-nav2-amcl]
nav2_amcl_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-amcl-dbgsym]
    jammy: [ros-humble-nav2-amcl-dbgsym]
nav2_behavior_tree:
  ubuntu:
    focal: [ros-humble-nav2-behavior-tree]
    jammy: [ros-humble-nav2-behavior-tree]
nav2_behavior_tree_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-behavior-tree-dbgsym]
    jammy: [ros-humble-nav2-behavior-tree-dbgsym]
nav2_behaviors:
  ubuntu:
    focal: [ros-humble-nav2-behaviors]
    jammy: [ros-humble-nav2-behaviors]
nav2_behaviors_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-behaviors-dbgsym]
    jammy: [ros-humble-nav2-behaviors-dbgsym]
nav2_bringup:
  ubuntu:
    focal: [ros-humble-nav2-bringup]
    jammy: [ros-humble-nav2-bringup]
nav2_bt_navigator:
  ubuntu:
    focal: [ros-humble-nav2-bt-navigator]
    jammy: [ros-humble-nav2-bt-navigator]
nav2_bt_navigator_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-bt-navigator-dbgsym]
    jammy: [ros-humble-nav2-bt-navigator-dbgsym]
nav2_collision_monitor:
  ubuntu:
    focal: [ros-humble-nav2-collision-monitor]
    jammy: [ros-humble-nav2-collision-monitor]
nav2_collision_monitor_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-collision-monitor-dbgsym]
    jammy: [ros-humble-nav2-collision-monitor-dbgsym]
nav2_common:
  ubuntu:
    focal: [ros-humble-nav2-common]
    jammy: [ros-humble-nav2-common]
nav2_constrained_smoother:
  ubuntu:
    focal: [ros-humble-nav2-constrained-smoother]
    jammy: [ros-humble-nav2-constrained-smoother]
nav2_constrained_smoother_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-constrained-smoother-dbgsym]
    jammy: [ros-humble-nav2-constrained-smoother-dbgsym]
nav2_controller:
  ubuntu:
    focal: [ros-humble-nav2-controller]
    jammy: [ros-humble-nav2-controller]
nav2_controller_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-controller-dbgsym]
    jammy: [ros-humble-nav2-controller-dbgsym]
nav2_core:
  ubuntu:
    focal: [ros-humble-nav2-core]
    jammy: [ros-humble-nav2-core]
nav2_costmap_2d:
  ubuntu:
    focal: [ros-humble-nav2-costmap-2d]
    jammy: [ros-humble-nav2-costmap-2d]
nav2_costmap_2d_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-costmap-2d-dbgsym]
    jammy: [ros-humble-nav2-costmap-2d-dbgsym]
nav2_dwb_controller:
  ubuntu:
    focal: [ros-humble-nav2-dwb-controller]
    jammy: [ros-humble-nav2-dwb-controller]
nav2_lifecycle_manager:
  ubuntu:
    focal: [ros-humble-nav2-lifecycle-manager]
    jammy: [ros-humble-nav2-lifecycle-manager]
nav2_lifecycle_manager_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-lifecycle-manager-dbgsym]
    jammy: [ros-humble-nav2-lifecycle-manager-dbgsym]
nav2_map_server:
  ubuntu:
    focal: [ros-humble-nav2-map-server]
    jammy: [ros-humble-nav2-map-server]
nav2_map_server_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-map-server-dbgsym]
    jammy: [ros-humble-nav2-map-server-dbgsym]
nav2_mppi_controller:
  ubuntu:
    focal: [ros-humble-nav2-mppi-controller]
    jammy: [ros-humble-nav2-mppi-controller]
nav2_mppi_controller_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-mppi-controller-dbgsym]
    jammy: [ros-humble-nav2-mppi-controller-dbgsym]
nav2_msgs:
  ubuntu:
    focal: [ros-humble-nav2-msgs]
    jammy: [ros-humble-nav2-msgs]
nav2_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-msgs-dbgsym]
    jammy: [ros-humble-nav2-msgs-dbgsym]
nav2_navfn_planner:
  ubuntu:
    focal: [ros-humble-nav2-navfn-planner]
    jammy: [ros-humble-nav2-navfn-planner]
nav2_navfn_planner_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-navfn-planner-dbgsym]
    jammy: [ros-humble-nav2-navfn-planner-dbgsym]
nav2_planner:
  ubuntu:
    focal: [ros-humble-nav2-planner]
    jammy: [ros-humble-nav2-planner]
nav2_planner_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-planner-dbgsym]
    jammy: [ros-humble-nav2-planner-dbgsym]
nav2_regulated_pure_pursuit_controller:
  ubuntu:
    focal: [ros-humble-nav2-regulated-pure-pursuit-controller]
    jammy: [ros-humble-nav2-regulated-pure-pursuit-controller]
nav2_regulated_pure_pursuit_controller_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-regulated-pure-pursuit-controller-dbgsym]
    jammy: [ros-humble-nav2-regulated-pure-pursuit-controller-dbgsym]
nav2_rotation_shim_controller:
  ubuntu:
    focal: [ros-humble-nav2-rotation-shim-controller]
    jammy: [ros-humble-nav2-rotation-shim-controller]
nav2_rotation_shim_controller_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-rotation-shim-controller-dbgsym]
    jammy: [ros-humble-nav2-rotation-shim-controller-dbgsym]
nav2_rviz_plugins:
  ubuntu:
    focal: [ros-humble-nav2-rviz-plugins]
    jammy: [ros-humble-nav2-rviz-plugins]
nav2_rviz_plugins_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-rviz-plugins-dbgsym]
    jammy: [ros-humble-nav2-rviz-plugins-dbgsym]
nav2_simple_commander:
  ubuntu:
    focal: [ros-humble-nav2-simple-commander]
    jammy: [ros-humble-nav2-simple-commander]
nav2_smac_planner:
  ubuntu:
    focal: [ros-humble-nav2-smac-planner]
    jammy: [ros-humble-nav2-smac-planner]
nav2_smac_planner_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-smac-planner-dbgsym]
    jammy: [ros-humble-nav2-smac-planner-dbgsym]
nav2_smoother:
  ubuntu:
    focal: [ros-humble-nav2-smoother]
    jammy: [ros-humble-nav2-smoother]
nav2_smoother_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-smoother-dbgsym]
    jammy: [ros-humble-nav2-smoother-dbgsym]
nav2_theta_star_planner:
  ubuntu:
    focal: [ros-humble-nav2-theta-star-planner]
    jammy: [ros-humble-nav2-theta-star-planner]
nav2_theta_star_planner_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-theta-star-planner-dbgsym]
    jammy: [ros-humble-nav2-theta-star-planner-dbgsym]
nav2_util:
  ubuntu:
    focal: [ros-humble-nav2-util]
    jammy: [ros-humble-nav2-util]
nav2_util_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-util-dbgsym]
    jammy: [ros-humble-nav2-util-dbgsym]
nav2_velocity_smoother:
  ubuntu:
    focal: [ros-humble-nav2-velocity-smoother]
    jammy: [ros-humble-nav2-velocity-smoother]
nav2_velocity_smoother_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-velocity-smoother-dbgsym]
    jammy: [ros-humble-nav2-velocity-smoother-dbgsym]
nav2_voxel_grid:
  ubuntu:
    focal: [ros-humble-nav2-voxel-grid]
    jammy: [ros-humble-nav2-voxel-grid]
nav2_voxel_grid_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-voxel-grid-dbgsym]
    jammy: [ros-humble-nav2-voxel-grid-dbgsym]
nav2_waypoint_follower:
  ubuntu:
    focal: [ros-humble-nav2-waypoint-follower]
    jammy: [ros-humble-nav2-waypoint-follower]
nav2_waypoint_follower_dbgsym:
  ubuntu:
    focal: [ros-humble-nav2-waypoint-follower-dbgsym]
    jammy: [ros-humble-nav2-waypoint-follower-dbgsym]
nav_2d_msgs:
  ubuntu:
    focal: [ros-humble-nav-2d-msgs]
    jammy: [ros-humble-nav-2d-msgs]
nav_2d_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-nav-2d-msgs-dbgsym]
    jammy: [ros-humble-nav-2d-msgs-dbgsym]
nav_2d_utils:
  ubuntu:
    focal: [ros-humble-nav-2d-utils]
    jammy: [ros-humble-nav-2d-utils]
nav_2d_utils_dbgsym:
  ubuntu:
    focal: [ros-humble-nav-2d-utils-dbgsym]
    jammy: [ros-humble-nav-2d-utils-dbgsym]
nav_msgs:
  ubuntu:
    focal: [ros-humble-nav-msgs]
    jammy: [ros-humble-nav-msgs]
nav_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-nav-msgs-dbgsym]
    jammy: [ros-humble-nav-msgs-dbgsym]
navigation2:
  ubuntu:
    focal: [ros-humble-navigation2]
    jammy: [ros-humble-navigation2]
negotiated:
  ubuntu:
    focal: [ros-humble-negotiated]
    jammy: [ros-humble-negotiated]
negotiated_interfaces:
  ubuntu:
    focal: [ros-humble-negotiated-interfaces]
    jammy: [ros-humble-negotiated-interfaces]
neo_simulation2:
  ubuntu:
    focal: [ros-humble-neo-simulation2]
    jammy: [ros-humble-neo-simulation2]
neobotix_usboard_msgs:
  ubuntu:
    focal: [ros-humble-neobotix-usboard-msgs]
    jammy: [ros-humble-neobotix-usboard-msgs]
neobotix_usboard_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-neobotix-usboard-msgs-dbgsym]
    jammy: [ros-humble-neobotix-usboard-msgs-dbgsym]
nerian_stereo:
  ubuntu:
    focal: [ros-humble-nerian-stereo]
    jammy: [ros-humble-nerian-stereo]
nerian_stereo_dbgsym:
  ubuntu:
    focal: [ros-humble-nerian-stereo-dbgsym]
    jammy: [ros-humble-nerian-stereo-dbgsym]
network_performance_measurement:
  ubuntu:
    focal: [ros-humble-network-performance-measurement]
    jammy: [ros-humble-network-performance-measurement]
nlohmann_json:
  ubuntu:
    focal: [nlohmann-json3-dev]
    jammy: [nlohmann-json3-dev]
nlohmann_json_schema_validator_vendor:
  ubuntu:
    focal: [ros-humble-nlohmann-json-schema-validator-vendor]
    jammy: [ros-humble-nlohmann-json-schema-validator-vendor]
nlohmann_json_schema_validator_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-nlohmann-json-schema-validator-vendor-dbgsym]
    jammy: [ros-humble-nlohmann-json-schema-validator-vendor-dbgsym]
nmea_msgs:
  ubuntu:
    focal: [ros-humble-nmea-msgs]
    jammy: [ros-humble-nmea-msgs]
nmea_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-nmea-msgs-dbgsym]
    jammy: [ros-humble-nmea-msgs-dbgsym]
nmea_navsat_driver:
  ubuntu:
    focal: [ros-humble-nmea-navsat-driver]
    jammy: [ros-humble-nmea-navsat-driver]
nodelet:
  ubuntu:
    focal: [ros-noetic-nodelet]
    jammy: [ros-noetic-nodelet]
nodl_python:
  ubuntu:
    focal: [ros-humble-nodl-python]
    jammy: [ros-humble-nodl-python]
nodl_to_policy:
  ubuntu:
    focal: [ros-humble-nodl-to-policy]
    jammy: [ros-humble-nodl-to-policy]
nova_carter_bringup:
  ubuntu:
    focal: [ros-humble-nova-carter-bringup]
    jammy: [ros-humble-nova-carter-bringup]
nova_carter_description:
  ubuntu:
    focal: [ros-humble-nova-carter-description]
    jammy: [ros-humble-nova-carter-description]
nova_carter_docking:
  ubuntu:
    focal: [ros-humble-nova-carter-docking]
    jammy: [ros-humble-nova-carter-docking]
nova_carter_example_data:
  ubuntu:
    focal: [ros-humble-nova-carter-example-data]
    jammy: [ros-humble-nova-carter-example-data]
nova_carter_navigation:
  ubuntu:
    focal: [ros-humble-nova-carter-navigation]
    jammy: [ros-humble-nova-carter-navigation]
nova_developer_kit_bringup:
  ubuntu:
    focal: [ros-humble-nova-developer-kit-bringup]
    jammy: [ros-humble-nova-developer-kit-bringup]
nova_developer_kit_description:
  ubuntu:
    focal: [ros-humble-nova-developer-kit-description]
    jammy: [ros-humble-nova-developer-kit-description]
novatel_gps_msgs:
  ubuntu:
    focal: [ros-humble-novatel-gps-msgs]
    jammy: [ros-humble-novatel-gps-msgs]
novatel_gps_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-novatel-gps-msgs-dbgsym]
    jammy: [ros-humble-novatel-gps-msgs-dbgsym]
novatel_oem7_driver:
  ubuntu:
    focal: [ros-humble-novatel-oem7-driver]
    jammy: [ros-humble-novatel-oem7-driver]
novatel_oem7_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-novatel-oem7-driver-dbgsym]
    jammy: [ros-humble-novatel-oem7-driver-dbgsym]
novatel_oem7_msgs:
  ubuntu:
    focal: [ros-humble-novatel-oem7-msgs]
    jammy: [ros-humble-novatel-oem7-msgs]
novatel_oem7_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-novatel-oem7-msgs-dbgsym]
    jammy: [ros-humble-novatel-oem7-msgs-dbgsym]
ntpd_driver:
  ubuntu:
    focal: [ros-humble-ntpd-driver]
    jammy: [ros-humble-ntpd-driver]
ntpd_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-ntpd-driver-dbgsym]
    jammy: [ros-humble-ntpd-driver-dbgsym]
ntrip_client:
  ubuntu:
    focal: [ros-humble-ntrip-client]
    jammy: [ros-humble-ntrip-client]
ntrip_client_node:
  ubuntu:
    focal: [ros-humble-ntrip-client-node]
    jammy: [ros-humble-ntrip-client-node]
ntrip_client_node_dbgsym:
  ubuntu:
    focal: [ros-humble-ntrip-client-node-dbgsym]
    jammy: [ros-humble-ntrip-client-node-dbgsym]
nvblox:
  ubuntu:
    focal: [nvblox]
    jammy: [nvblox]
nvblox_cpu_gpu_tools:
  ubuntu:
    focal: [ros-humble-nvblox-cpu-gpu-tools]
    jammy: [ros-humble-nvblox-cpu-gpu-tools]
nvblox_examples_bringup:
  ubuntu:
    focal: [ros-humble-nvblox-examples-bringup]
    jammy: [ros-humble-nvblox-examples-bringup]
nvblox_image_padding:
  ubuntu:
    focal: [ros-humble-nvblox-image-padding]
    jammy: [ros-humble-nvblox-image-padding]
nvblox_isaac_sim:
  ubuntu:
    focal: [ros-humble-nvblox-isaac-sim]
    jammy: [ros-humble-nvblox-isaac-sim]
nvblox_msgs:
  ubuntu:
    focal: [ros-humble-nvblox-msgs]
    jammy: [ros-humble-nvblox-msgs]
nvblox_nav2:
  ubuntu:
    focal: [ros-humble-nvblox-nav2]
    jammy: [ros-humble-nvblox-nav2]
nvblox_performance_measurement:
  ubuntu:
    focal: [ros-humble-nvblox-performance-measurement]
    jammy: [ros-humble-nvblox-performance-measurement]
nvblox_performance_measurement_msgs:
  ubuntu:
    focal: [ros-humble-nvblox-performance-measurement-msgs]
    jammy: [ros-humble-nvblox-performance-measurement-msgs]
nvblox_ros:
  ubuntu:
    focal: [ros-humble-nvblox-ros]
    jammy: [ros-humble-nvblox-ros]
nvblox_ros_common:
  ubuntu:
    focal: [ros-humble-nvblox-ros-common]
    jammy: [ros-humble-nvblox-ros-common]
nvblox_ros_python_utils:
  ubuntu:
    focal: [ros-humble-nvblox-ros-python-utils]
    jammy: [ros-humble-nvblox-ros-python-utils]
nvblox_rviz_plugin:
  ubuntu:
    focal: [ros-humble-nvblox-rviz-plugin]
    jammy: [ros-humble-nvblox-rviz-plugin]
nvblox_test:
  ubuntu:
    focal: [ros-humble-nvblox-test]
    jammy: [ros-humble-nvblox-test]
nvblox_test_data:
  ubuntu:
    focal: [ros-humble-nvblox-test-data]
    jammy: [ros-humble-nvblox-test-data]
object_recognition_msgs:
  ubuntu:
    focal: [ros-humble-object-recognition-msgs]
    jammy: [ros-humble-object-recognition-msgs]
object_recognition_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-object-recognition-msgs-dbgsym]
    jammy: [ros-humble-object-recognition-msgs-dbgsym]
octomap:
  ubuntu:
    focal: [ros-humble-octomap]
    jammy: [ros-humble-octomap]
octomap_dbgsym:
  ubuntu:
    focal: [ros-humble-octomap-dbgsym]
    jammy: [ros-humble-octomap-dbgsym]
octomap_mapping:
  ubuntu:
    focal: [ros-humble-octomap-mapping]
    jammy: [ros-humble-octomap-mapping]
octomap_msgs:
  ubuntu:
    focal: [ros-humble-octomap-msgs]
    jammy: [ros-humble-octomap-msgs]
octomap_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-octomap-msgs-dbgsym]
    jammy: [ros-humble-octomap-msgs-dbgsym]
octomap_ros:
  ubuntu:
    focal: [ros-humble-octomap-ros]
    jammy: [ros-humble-octomap-ros]
octomap_ros_dbgsym:
  ubuntu:
    focal: [ros-humble-octomap-ros-dbgsym]
    jammy: [ros-humble-octomap-ros-dbgsym]
octomap_rviz_plugins:
  ubuntu:
    focal: [ros-humble-octomap-rviz-plugins]
    jammy: [ros-humble-octomap-rviz-plugins]
octomap_rviz_plugins_dbgsym:
  ubuntu:
    focal: [ros-humble-octomap-rviz-plugins-dbgsym]
    jammy: [ros-humble-octomap-rviz-plugins-dbgsym]
octomap_server:
  ubuntu:
    focal: [ros-humble-octomap-server]
    jammy: [ros-humble-octomap-server]
octomap_server_dbgsym:
  ubuntu:
    focal: [ros-humble-octomap-server-dbgsym]
    jammy: [ros-humble-octomap-server-dbgsym]
octovis:
  ubuntu:
    focal: [ros-humble-octovis]
    jammy: [ros-humble-octovis]
octovis_dbgsym:
  ubuntu:
    focal: [ros-humble-octovis-dbgsym]
    jammy: [ros-humble-octovis-dbgsym]
odom_to_tf_ros2:
  ubuntu:
    focal: [ros-humble-odom-to-tf-ros2]
    jammy: [ros-humble-odom-to-tf-ros2]
odom_to_tf_ros2_dbgsym:
  ubuntu:
    focal: [ros-humble-odom-to-tf-ros2-dbgsym]
    jammy: [ros-humble-odom-to-tf-ros2-dbgsym]
odometry_flattener:
  ubuntu:
    focal: [ros-humble-odometry-flattener]
    jammy: [ros-humble-odometry-flattener]
ompl:
  ubuntu:
    focal: [ros-humble-ompl]
    jammy: [ros-humble-ompl]
ompl_dbgsym:
  ubuntu:
    focal: [ros-humble-ompl-dbgsym]
    jammy: [ros-humble-ompl-dbgsym]
opennav_docking:
  ubuntu:
    focal: [ros-humble-opennav-docking]
    jammy: [ros-humble-opennav-docking]
opennav_docking_bt:
  ubuntu:
    focal: [ros-humble-opennav-docking-bt]
    jammy: [ros-humble-opennav-docking-bt]
opennav_docking_core:
  ubuntu:
    focal: [ros-humble-opennav-docking-core]
    jammy: [ros-humble-opennav-docking-core]
opennav_docking_msgs:
  ubuntu:
    focal: [ros-humble-opennav-docking-msgs]
    jammy: [ros-humble-opennav-docking-msgs]
openni2_camera:
  ubuntu:
    focal: [ros-humble-openni2-camera]
    jammy: [ros-humble-openni2-camera]
openni2_camera_dbgsym:
  ubuntu:
    focal: [ros-humble-openni2-camera-dbgsym]
    jammy: [ros-humble-openni2-camera-dbgsym]
orocos_kdl_vendor:
  ubuntu:
    focal: [ros-humble-orocos-kdl-vendor]
    jammy: [ros-humble-orocos-kdl-vendor]
orocos_kdl_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-orocos-kdl-vendor-dbgsym]
    jammy: [ros-humble-orocos-kdl-vendor-dbgsym]
osqp_vendor:
  ubuntu:
    focal: [ros-humble-osqp-vendor]
    jammy: [ros-humble-osqp-vendor]
osqp_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-osqp-vendor-dbgsym]
    jammy: [ros-humble-osqp-vendor-dbgsym]
osrf_pycommon:
  ubuntu:
    focal: [ros-humble-osrf-pycommon]
    jammy: [ros-humble-osrf-pycommon]
osrf_testing_tools_cpp:
  ubuntu:
    focal: [ros-humble-osrf-testing-tools-cpp]
    jammy: [ros-humble-osrf-testing-tools-cpp]
osrf_testing_tools_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-osrf-testing-tools-cpp-dbgsym]
    jammy: [ros-humble-osrf-testing-tools-cpp-dbgsym]
ouster_msgs:
  ubuntu:
    focal: [ros-humble-ouster-msgs]
    jammy: [ros-humble-ouster-msgs]
ouster_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-ouster-msgs-dbgsym]
    jammy: [ros-humble-ouster-msgs-dbgsym]
ouxt_common:
  ubuntu:
    focal: [ros-humble-ouxt-common]
    jammy: [ros-humble-ouxt-common]
ouxt_lint_common:
  ubuntu:
    focal: [ros-humble-ouxt-lint-common]
    jammy: [ros-humble-ouxt-lint-common]
owl_description:
  ubuntu:
    focal: [ros-humble-owl-description]
    jammy: [ros-humble-owl-description]
pal_gazebo_worlds:
  ubuntu:
    focal: [ros-humble-pal-gazebo-worlds]
    jammy: [ros-humble-pal-gazebo-worlds]
pal_gripper:
  ubuntu:
    focal: [ros-humble-pal-gripper]
    jammy: [ros-humble-pal-gripper]
pal_gripper_controller_configuration:
  ubuntu:
    focal: [ros-humble-pal-gripper-controller-configuration]
    jammy: [ros-humble-pal-gripper-controller-configuration]
pal_gripper_description:
  ubuntu:
    focal: [ros-humble-pal-gripper-description]
    jammy: [ros-humble-pal-gripper-description]
pal_navigation_cfg:
  ubuntu:
    focal: [ros-humble-pal-navigation-cfg]
    jammy: [ros-humble-pal-navigation-cfg]
pal_navigation_cfg_bringup:
  ubuntu:
    focal: [ros-humble-pal-navigation-cfg-bringup]
    jammy: [ros-humble-pal-navigation-cfg-bringup]
pal_navigation_cfg_params:
  ubuntu:
    focal: [ros-humble-pal-navigation-cfg-params]
    jammy: [ros-humble-pal-navigation-cfg-params]
pal_statistics:
  ubuntu:
    focal: [ros-humble-pal-statistics]
    jammy: [ros-humble-pal-statistics]
pal_statistics_dbgsym:
  ubuntu:
    focal: [ros-humble-pal-statistics-dbgsym]
    jammy: [ros-humble-pal-statistics-dbgsym]
pal_statistics_msgs:
  ubuntu:
    focal: [ros-humble-pal-statistics-msgs]
    jammy: [ros-humble-pal-statistics-msgs]
pal_statistics_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-pal-statistics-msgs-dbgsym]
    jammy: [ros-humble-pal-statistics-msgs-dbgsym]
pandar_xt32_description:
  ubuntu:
    focal: [ros-humble-pandar-xt32-description]
    jammy: [ros-humble-pandar-xt32-description]
parameter_traits:
  ubuntu:
    focal: [ros-humble-parameter-traits]
    jammy: [ros-humble-parameter-traits]
pcl_conversions:
  ubuntu:
    focal: [ros-humble-pcl-conversions]
    jammy: [ros-humble-pcl-conversions]
pcl_msgs:
  ubuntu:
    focal: [ros-humble-pcl-msgs]
    jammy: [ros-humble-pcl-msgs]
pcl_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-pcl-msgs-dbgsym]
    jammy: [ros-humble-pcl-msgs-dbgsym]
pcl_ros:
  ubuntu:
    focal: [ros-humble-pcl-ros]
    jammy: [ros-humble-pcl-ros]
pendulum_control:
  ubuntu:
    focal: [ros-humble-pendulum-control]
    jammy: [ros-humble-pendulum-control]
pendulum_control_dbgsym:
  ubuntu:
    focal: [ros-humble-pendulum-control-dbgsym]
    jammy: [ros-humble-pendulum-control-dbgsym]
pendulum_msgs:
  ubuntu:
    focal: [ros-humble-pendulum-msgs]
    jammy: [ros-humble-pendulum-msgs]
pendulum_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-pendulum-msgs-dbgsym]
    jammy: [ros-humble-pendulum-msgs-dbgsym]
perception:
  ubuntu:
    focal: [ros-humble-perception]
    jammy: [ros-humble-perception]
perception_pcl:
  ubuntu:
    focal: [ros-humble-perception-pcl]
    jammy: [ros-humble-perception-pcl]
performance_test_fixture:
  ubuntu:
    focal: [ros-humble-performance-test-fixture]
    jammy: [ros-humble-performance-test-fixture]
performance_test_fixture_dbgsym:
  ubuntu:
    focal: [ros-humble-performance-test-fixture-dbgsym]
    jammy: [ros-humble-performance-test-fixture-dbgsym]
phidgets_accelerometer:
  ubuntu:
    focal: [ros-humble-phidgets-accelerometer]
    jammy: [ros-humble-phidgets-accelerometer]
phidgets_accelerometer_dbgsym:
  ubuntu:
    focal: [ros-humble-phidgets-accelerometer-dbgsym]
    jammy: [ros-humble-phidgets-accelerometer-dbgsym]
phidgets_analog_inputs:
  ubuntu:
    focal: [ros-humble-phidgets-analog-inputs]
    jammy: [ros-humble-phidgets-analog-inputs]
phidgets_analog_inputs_dbgsym:
  ubuntu:
    focal: [ros-humble-phidgets-analog-inputs-dbgsym]
    jammy: [ros-humble-phidgets-analog-inputs-dbgsym]
phidgets_analog_outputs:
  ubuntu:
    focal: [ros-humble-phidgets-analog-outputs]
    jammy: [ros-humble-phidgets-analog-outputs]
phidgets_analog_outputs_dbgsym:
  ubuntu:
    focal: [ros-humble-phidgets-analog-outputs-dbgsym]
    jammy: [ros-humble-phidgets-analog-outputs-dbgsym]
phidgets_api:
  ubuntu:
    focal: [ros-humble-phidgets-api]
    jammy: [ros-humble-phidgets-api]
phidgets_api_dbgsym:
  ubuntu:
    focal: [ros-humble-phidgets-api-dbgsym]
    jammy: [ros-humble-phidgets-api-dbgsym]
phidgets_digital_inputs:
  ubuntu:
    focal: [ros-humble-phidgets-digital-inputs]
    jammy: [ros-humble-phidgets-digital-inputs]
phidgets_digital_inputs_dbgsym:
  ubuntu:
    focal: [ros-humble-phidgets-digital-inputs-dbgsym]
    jammy: [ros-humble-phidgets-digital-inputs-dbgsym]
phidgets_digital_outputs:
  ubuntu:
    focal: [ros-humble-phidgets-digital-outputs]
    jammy: [ros-humble-phidgets-digital-outputs]
phidgets_digital_outputs_dbgsym:
  ubuntu:
    focal: [ros-humble-phidgets-digital-outputs-dbgsym]
    jammy: [ros-humble-phidgets-digital-outputs-dbgsym]
phidgets_drivers:
  ubuntu:
    focal: [ros-humble-phidgets-drivers]
    jammy: [ros-humble-phidgets-drivers]
phidgets_gyroscope:
  ubuntu:
    focal: [ros-humble-phidgets-gyroscope]
    jammy: [ros-humble-phidgets-gyroscope]
phidgets_gyroscope_dbgsym:
  ubuntu:
    focal: [ros-humble-phidgets-gyroscope-dbgsym]
    jammy: [ros-humble-phidgets-gyroscope-dbgsym]
phidgets_high_speed_encoder:
  ubuntu:
    focal: [ros-humble-phidgets-high-speed-encoder]
    jammy: [ros-humble-phidgets-high-speed-encoder]
phidgets_high_speed_encoder_dbgsym:
  ubuntu:
    focal: [ros-humble-phidgets-high-speed-encoder-dbgsym]
    jammy: [ros-humble-phidgets-high-speed-encoder-dbgsym]
phidgets_ik:
  ubuntu:
    focal: [ros-humble-phidgets-ik]
    jammy: [ros-humble-phidgets-ik]
phidgets_magnetometer:
  ubuntu:
    focal: [ros-humble-phidgets-magnetometer]
    jammy: [ros-humble-phidgets-magnetometer]
phidgets_magnetometer_dbgsym:
  ubuntu:
    focal: [ros-humble-phidgets-magnetometer-dbgsym]
    jammy: [ros-humble-phidgets-magnetometer-dbgsym]
phidgets_motors:
  ubuntu:
    focal: [ros-humble-phidgets-motors]
    jammy: [ros-humble-phidgets-motors]
phidgets_motors_dbgsym:
  ubuntu:
    focal: [ros-humble-phidgets-motors-dbgsym]
    jammy: [ros-humble-phidgets-motors-dbgsym]
phidgets_msgs:
  ubuntu:
    focal: [ros-humble-phidgets-msgs]
    jammy: [ros-humble-phidgets-msgs]
phidgets_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-phidgets-msgs-dbgsym]
    jammy: [ros-humble-phidgets-msgs-dbgsym]
phidgets_spatial:
  ubuntu:
    focal: [ros-humble-phidgets-spatial]
    jammy: [ros-humble-phidgets-spatial]
phidgets_spatial_dbgsym:
  ubuntu:
    focal: [ros-humble-phidgets-spatial-dbgsym]
    jammy: [ros-humble-phidgets-spatial-dbgsym]
phidgets_temperature:
  ubuntu:
    focal: [ros-humble-phidgets-temperature]
    jammy: [ros-humble-phidgets-temperature]
phidgets_temperature_dbgsym:
  ubuntu:
    focal: [ros-humble-phidgets-temperature-dbgsym]
    jammy: [ros-humble-phidgets-temperature-dbgsym]
pick_ik:
  ubuntu:
    focal: [ros-humble-pick-ik]
    jammy: [ros-humble-pick-ik]
pick_ik_dbgsym:
  ubuntu:
    focal: [ros-humble-pick-ik-dbgsym]
    jammy: [ros-humble-pick-ik-dbgsym]
picknik_ament_copyright:
  ubuntu:
    focal: [ros-humble-picknik-ament-copyright]
    jammy: [ros-humble-picknik-ament-copyright]
picknik_reset_fault_controller:
  ubuntu:
    focal: [ros-humble-picknik-reset-fault-controller]
    jammy: [ros-humble-picknik-reset-fault-controller]
picknik_reset_fault_controller_dbgsym:
  ubuntu:
    focal: [ros-humble-picknik-reset-fault-controller-dbgsym]
    jammy: [ros-humble-picknik-reset-fault-controller-dbgsym]
picknik_twist_controller:
  ubuntu:
    focal: [ros-humble-picknik-twist-controller]
    jammy: [ros-humble-picknik-twist-controller]
picknik_twist_controller_dbgsym:
  ubuntu:
    focal: [ros-humble-picknik-twist-controller-dbgsym]
    jammy: [ros-humble-picknik-twist-controller-dbgsym]
pilz_industrial_motion_planner:
  ubuntu:
    focal: [ros-humble-pilz-industrial-motion-planner]
    jammy: [ros-humble-pilz-industrial-motion-planner]
pilz_industrial_motion_planner_dbgsym:
  ubuntu:
    focal: [ros-humble-pilz-industrial-motion-planner-dbgsym]
    jammy: [ros-humble-pilz-industrial-motion-planner-dbgsym]
pilz_industrial_motion_planner_testutils:
  ubuntu:
    focal: [ros-humble-pilz-industrial-motion-planner-testutils]
    jammy: [ros-humble-pilz-industrial-motion-planner-testutils]
pilz_industrial_motion_planner_testutils_dbgsym:
  ubuntu:
    focal: [ros-humble-pilz-industrial-motion-planner-testutils-dbgsym]
    jammy: [ros-humble-pilz-industrial-motion-planner-testutils-dbgsym]
pinocchio:
  ubuntu:
    focal: [ros-humble-pinocchio]
    jammy: [ros-humble-pinocchio]
pinocchio_dbgsym:
  ubuntu:
    focal: [ros-humble-pinocchio-dbgsym]
    jammy: [ros-humble-pinocchio-dbgsym]
plansys2_bringup:
  ubuntu:
    focal: [ros-humble-plansys2-bringup]
    jammy: [ros-humble-plansys2-bringup]
plansys2_bringup_dbgsym:
  ubuntu:
    focal: [ros-humble-plansys2-bringup-dbgsym]
    jammy: [ros-humble-plansys2-bringup-dbgsym]
plansys2_bt_actions:
  ubuntu:
    focal: [ros-humble-plansys2-bt-actions]
    jammy: [ros-humble-plansys2-bt-actions]
plansys2_bt_actions_dbgsym:
  ubuntu:
    focal: [ros-humble-plansys2-bt-actions-dbgsym]
    jammy: [ros-humble-plansys2-bt-actions-dbgsym]
plansys2_core:
  ubuntu:
    focal: [ros-humble-plansys2-core]
    jammy: [ros-humble-plansys2-core]
plansys2_core_dbgsym:
  ubuntu:
    focal: [ros-humble-plansys2-core-dbgsym]
    jammy: [ros-humble-plansys2-core-dbgsym]
plansys2_domain_expert:
  ubuntu:
    focal: [ros-humble-plansys2-domain-expert]
    jammy: [ros-humble-plansys2-domain-expert]
plansys2_domain_expert_dbgsym:
  ubuntu:
    focal: [ros-humble-plansys2-domain-expert-dbgsym]
    jammy: [ros-humble-plansys2-domain-expert-dbgsym]
plansys2_executor:
  ubuntu:
    focal: [ros-humble-plansys2-executor]
    jammy: [ros-humble-plansys2-executor]
plansys2_executor_dbgsym:
  ubuntu:
    focal: [ros-humble-plansys2-executor-dbgsym]
    jammy: [ros-humble-plansys2-executor-dbgsym]
plansys2_lifecycle_manager:
  ubuntu:
    focal: [ros-humble-plansys2-lifecycle-manager]
    jammy: [ros-humble-plansys2-lifecycle-manager]
plansys2_lifecycle_manager_dbgsym:
  ubuntu:
    focal: [ros-humble-plansys2-lifecycle-manager-dbgsym]
    jammy: [ros-humble-plansys2-lifecycle-manager-dbgsym]
plansys2_msgs:
  ubuntu:
    focal: [ros-humble-plansys2-msgs]
    jammy: [ros-humble-plansys2-msgs]
plansys2_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-plansys2-msgs-dbgsym]
    jammy: [ros-humble-plansys2-msgs-dbgsym]
plansys2_pddl_parser:
  ubuntu:
    focal: [ros-humble-plansys2-pddl-parser]
    jammy: [ros-humble-plansys2-pddl-parser]
plansys2_pddl_parser_dbgsym:
  ubuntu:
    focal: [ros-humble-plansys2-pddl-parser-dbgsym]
    jammy: [ros-humble-plansys2-pddl-parser-dbgsym]
plansys2_planner:
  ubuntu:
    focal: [ros-humble-plansys2-planner]
    jammy: [ros-humble-plansys2-planner]
plansys2_planner_dbgsym:
  ubuntu:
    focal: [ros-humble-plansys2-planner-dbgsym]
    jammy: [ros-humble-plansys2-planner-dbgsym]
plansys2_popf_plan_solver:
  ubuntu:
    focal: [ros-humble-plansys2-popf-plan-solver]
    jammy: [ros-humble-plansys2-popf-plan-solver]
plansys2_popf_plan_solver_dbgsym:
  ubuntu:
    focal: [ros-humble-plansys2-popf-plan-solver-dbgsym]
    jammy: [ros-humble-plansys2-popf-plan-solver-dbgsym]
plansys2_problem_expert:
  ubuntu:
    focal: [ros-humble-plansys2-problem-expert]
    jammy: [ros-humble-plansys2-problem-expert]
plansys2_problem_expert_dbgsym:
  ubuntu:
    focal: [ros-humble-plansys2-problem-expert-dbgsym]
    jammy: [ros-humble-plansys2-problem-expert-dbgsym]
plansys2_terminal:
  ubuntu:
    focal: [ros-humble-plansys2-terminal]
    jammy: [ros-humble-plansys2-terminal]
plansys2_terminal_dbgsym:
  ubuntu:
    focal: [ros-humble-plansys2-terminal-dbgsym]
    jammy: [ros-humble-plansys2-terminal-dbgsym]
plansys2_tools:
  ubuntu:
    focal: [ros-humble-plansys2-tools]
    jammy: [ros-humble-plansys2-tools]
plansys2_tools_dbgsym:
  ubuntu:
    focal: [ros-humble-plansys2-tools-dbgsym]
    jammy: [ros-humble-plansys2-tools-dbgsym]
play_motion2:
  ubuntu:
    focal: [ros-humble-play-motion2]
    jammy: [ros-humble-play-motion2]
play_motion2_dbgsym:
  ubuntu:
    focal: [ros-humble-play-motion2-dbgsym]
    jammy: [ros-humble-play-motion2-dbgsym]
play_motion2_msgs:
  ubuntu:
    focal: [ros-humble-play-motion2-msgs]
    jammy: [ros-humble-play-motion2-msgs]
play_motion2_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-play-motion2-msgs-dbgsym]
    jammy: [ros-humble-play-motion2-msgs-dbgsym]
plotjuggler:
  ubuntu:
    focal: [ros-humble-plotjuggler]
    jammy: [ros-humble-plotjuggler]
plotjuggler_dbgsym:
  ubuntu:
    focal: [ros-humble-plotjuggler-dbgsym]
    jammy: [ros-humble-plotjuggler-dbgsym]
plotjuggler_msgs:
  ubuntu:
    focal: [ros-humble-plotjuggler-msgs]
    jammy: [ros-humble-plotjuggler-msgs]
plotjuggler_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-plotjuggler-msgs-dbgsym]
    jammy: [ros-humble-plotjuggler-msgs-dbgsym]
plotjuggler_ros:
  ubuntu:
    focal: [ros-humble-plotjuggler-ros]
    jammy: [ros-humble-plotjuggler-ros]
plotjuggler_ros_dbgsym:
  ubuntu:
    focal: [ros-humble-plotjuggler-ros-dbgsym]
    jammy: [ros-humble-plotjuggler-ros-dbgsym]
pluginlib:
  ubuntu:
    focal: [ros-humble-pluginlib]
    jammy: [ros-humble-pluginlib]
pmb2_2dnav:
  ubuntu:
    focal: [ros-humble-pmb2-2dnav]
    jammy: [ros-humble-pmb2-2dnav]
pmb2_bringup:
  ubuntu:
    focal: [ros-humble-pmb2-bringup]
    jammy: [ros-humble-pmb2-bringup]
pmb2_controller_configuration:
  ubuntu:
    focal: [ros-humble-pmb2-controller-configuration]
    jammy: [ros-humble-pmb2-controller-configuration]
pmb2_description:
  ubuntu:
    focal: [ros-humble-pmb2-description]
    jammy: [ros-humble-pmb2-description]
pmb2_laser_sensors:
  ubuntu:
    focal: [ros-humble-pmb2-laser-sensors]
    jammy: [ros-humble-pmb2-laser-sensors]
pmb2_maps:
  ubuntu:
    focal: [ros-humble-pmb2-maps]
    jammy: [ros-humble-pmb2-maps]
pmb2_navigation:
  ubuntu:
    focal: [ros-humble-pmb2-navigation]
    jammy: [ros-humble-pmb2-navigation]
pmb2_robot:
  ubuntu:
    focal: [ros-humble-pmb2-robot]
    jammy: [ros-humble-pmb2-robot]
point_cloud_interfaces:
  ubuntu:
    focal: [ros-humble-point-cloud-interfaces]
    jammy: [ros-humble-point-cloud-interfaces]
point_cloud_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-point-cloud-interfaces-dbgsym]
    jammy: [ros-humble-point-cloud-interfaces-dbgsym]
point_cloud_msg_wrapper:
  ubuntu:
    focal: [ros-humble-point-cloud-msg-wrapper]
    jammy: [ros-humble-point-cloud-msg-wrapper]
point_cloud_transport:
  ubuntu:
    focal: [ros-humble-point-cloud-transport]
    jammy: [ros-humble-point-cloud-transport]
point_cloud_transport_dbgsym:
  ubuntu:
    focal: [ros-humble-point-cloud-transport-dbgsym]
    jammy: [ros-humble-point-cloud-transport-dbgsym]
point_cloud_transport_py:
  ubuntu:
    focal: [ros-humble-point-cloud-transport-py]
    jammy: [ros-humble-point-cloud-transport-py]
pointcloud_to_laserscan:
  ubuntu:
    focal: [ros-humble-pointcloud-to-laserscan]
    jammy: [ros-humble-pointcloud-to-laserscan]
pointcloud_to_laserscan_dbgsym:
  ubuntu:
    focal: [ros-humble-pointcloud-to-laserscan-dbgsym]
    jammy: [ros-humble-pointcloud-to-laserscan-dbgsym]
polygon_demos:
  ubuntu:
    focal: [ros-humble-polygon-demos]
    jammy: [ros-humble-polygon-demos]
polygon_demos_dbgsym:
  ubuntu:
    focal: [ros-humble-polygon-demos-dbgsym]
    jammy: [ros-humble-polygon-demos-dbgsym]
polygon_msgs:
  ubuntu:
    focal: [ros-humble-polygon-msgs]
    jammy: [ros-humble-polygon-msgs]
polygon_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-polygon-msgs-dbgsym]
    jammy: [ros-humble-polygon-msgs-dbgsym]
polygon_rviz_plugins:
  ubuntu:
    focal: [ros-humble-polygon-rviz-plugins]
    jammy: [ros-humble-polygon-rviz-plugins]
polygon_rviz_plugins_dbgsym:
  ubuntu:
    focal: [ros-humble-polygon-rviz-plugins-dbgsym]
    jammy: [ros-humble-polygon-rviz-plugins-dbgsym]
polygon_utils:
  ubuntu:
    focal: [ros-humble-polygon-utils]
    jammy: [ros-humble-polygon-utils]
popf:
  ubuntu:
    focal: [ros-humble-popf]
    jammy: [ros-humble-popf]
popf_dbgsym:
  ubuntu:
    focal: [ros-humble-popf-dbgsym]
    jammy: [ros-humble-popf-dbgsym]
position_controllers:
  ubuntu:
    focal: [ros-humble-position-controllers]
    jammy: [ros-humble-position-controllers]
position_controllers_dbgsym:
  ubuntu:
    focal: [ros-humble-position-controllers-dbgsym]
    jammy: [ros-humble-position-controllers-dbgsym]
posix_ipc:
  ubuntu:
    focal: [python3-posix-ipc]
    jammy: [python3-posix-ipc]
proxsuite:
  ubuntu:
    focal: [ros-humble-proxsuite]
    jammy: [ros-humble-proxsuite]
py_binding_tools:
  ubuntu:
    focal: [ros-humble-py-binding-tools]
    jammy: [ros-humble-py-binding-tools]
py_trees:
  ubuntu:
    focal: [ros-humble-py-trees]
    jammy: [ros-humble-py-trees]
py_trees_js:
  ubuntu:
    focal: [ros-humble-py-trees-js]
    jammy: [ros-humble-py-trees-js]
py_trees_ros:
  ubuntu:
    focal: [ros-humble-py-trees-ros]
    jammy: [ros-humble-py-trees-ros]
py_trees_ros_interfaces:
  ubuntu:
    focal: [ros-humble-py-trees-ros-interfaces]
    jammy: [ros-humble-py-trees-ros-interfaces]
py_trees_ros_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-py-trees-ros-interfaces-dbgsym]
    jammy: [ros-humble-py-trees-ros-interfaces-dbgsym]
pybind11_json_vendor:
  ubuntu:
    focal: [ros-humble-pybind11-json-vendor]
    jammy: [ros-humble-pybind11-json-vendor]
pybind11_vendor:
  ubuntu:
    focal: [ros-humble-pybind11-vendor]
    jammy: [ros-humble-pybind11-vendor]
python3-av-pip:
  ubuntu:
    focal:
      pip:
        packages: [av]
    jammy:
      pip:
        packages: [av]
python3-pydantic-pip:
  ubuntu:
    focal:
      pip:
        packages: [pydantic]
    jammy:
      pip:
        packages: [pydantic]
python3-pymupdf:
  ubuntu:
    focal: [python3-fitz]
    jammy: [python3-fitz]
python3-pytransform3d-pip:
  ubuntu:
    focal:
      pip:
        packages: [pytransform3d]
    jammy:
      pip:
        packages: [pytransform3d]
python_cmake_module:
  ubuntu:
    focal: [ros-humble-python-cmake-module]
    jammy: [ros-humble-python-cmake-module]
python_orocos_kdl_vendor:
  ubuntu:
    focal: [ros-humble-python-orocos-kdl-vendor]
    jammy: [ros-humble-python-orocos-kdl-vendor]
python_qt_binding:
  ubuntu:
    focal: [ros-humble-python-qt-binding]
    jammy: [ros-humble-python-qt-binding]
qt_dotgraph:
  ubuntu:
    focal: [ros-humble-qt-dotgraph]
    jammy: [ros-humble-qt-dotgraph]
qt_gui:
  ubuntu:
    focal: [ros-humble-qt-gui]
    jammy: [ros-humble-qt-gui]
qt_gui_app:
  ubuntu:
    focal: [ros-humble-qt-gui-app]
    jammy: [ros-humble-qt-gui-app]
qt_gui_core:
  ubuntu:
    focal: [ros-humble-qt-gui-core]
    jammy: [ros-humble-qt-gui-core]
qt_gui_cpp:
  ubuntu:
    focal: [ros-humble-qt-gui-cpp]
    jammy: [ros-humble-qt-gui-cpp]
qt_gui_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-qt-gui-cpp-dbgsym]
    jammy: [ros-humble-qt-gui-cpp-dbgsym]
qt_gui_py_common:
  ubuntu:
    focal: [ros-humble-qt-gui-py-common]
    jammy: [ros-humble-qt-gui-py-common]
quality_of_service_demo_cpp:
  ubuntu:
    focal: [ros-humble-quality-of-service-demo-cpp]
    jammy: [ros-humble-quality-of-service-demo-cpp]
quality_of_service_demo_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-quality-of-service-demo-cpp-dbgsym]
    jammy: [ros-humble-quality-of-service-demo-cpp-dbgsym]
quality_of_service_demo_py:
  ubuntu:
    focal: [ros-humble-quality-of-service-demo-py]
    jammy: [ros-humble-quality-of-service-demo-py]
quaternion_operation:
  ubuntu:
    focal: [ros-humble-quaternion-operation]
    jammy: [ros-humble-quaternion-operation]
quaternion_operation_dbgsym:
  ubuntu:
    focal: [ros-humble-quaternion-operation-dbgsym]
    jammy: [ros-humble-quaternion-operation-dbgsym]
r2r_spl_7:
  ubuntu:
    focal: [ros-humble-r2r-spl-7]
    jammy: [ros-humble-r2r-spl-7]
radar_msgs:
  ubuntu:
    focal: [ros-humble-radar-msgs]
    jammy: [ros-humble-radar-msgs]
radar_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-radar-msgs-dbgsym]
    jammy: [ros-humble-radar-msgs-dbgsym]
random_numbers:
  ubuntu:
    focal: [ros-humble-random-numbers]
    jammy: [ros-humble-random-numbers]
random_numbers_dbgsym:
  ubuntu:
    focal: [ros-humble-random-numbers-dbgsym]
    jammy: [ros-humble-random-numbers-dbgsym]
raspimouse:
  ubuntu:
    focal: [ros-humble-raspimouse]
    jammy: [ros-humble-raspimouse]
raspimouse_dbgsym:
  ubuntu:
    focal: [ros-humble-raspimouse-dbgsym]
    jammy: [ros-humble-raspimouse-dbgsym]
raspimouse_description:
  ubuntu:
    focal: [ros-humble-raspimouse-description]
    jammy: [ros-humble-raspimouse-description]
raspimouse_msgs:
  ubuntu:
    focal: [ros-humble-raspimouse-msgs]
    jammy: [ros-humble-raspimouse-msgs]
raspimouse_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-raspimouse-msgs-dbgsym]
    jammy: [ros-humble-raspimouse-msgs-dbgsym]
raspimouse_navigation:
  ubuntu:
    focal: [ros-humble-raspimouse-navigation]
    jammy: [ros-humble-raspimouse-navigation]
raspimouse_ros2_examples:
  ubuntu:
    focal: [ros-humble-raspimouse-ros2-examples]
    jammy: [ros-humble-raspimouse-ros2-examples]
raspimouse_ros2_examples_dbgsym:
  ubuntu:
    focal: [ros-humble-raspimouse-ros2-examples-dbgsym]
    jammy: [ros-humble-raspimouse-ros2-examples-dbgsym]
raspimouse_slam:
  ubuntu:
    focal: [ros-humble-raspimouse-slam]
    jammy: [ros-humble-raspimouse-slam]
raspimouse_slam_navigation:
  ubuntu:
    focal: [ros-humble-raspimouse-slam-navigation]
    jammy: [ros-humble-raspimouse-slam-navigation]
rc_common_msgs:
  ubuntu:
    focal: [ros-humble-rc-common-msgs]
    jammy: [ros-humble-rc-common-msgs]
rc_common_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rc-common-msgs-dbgsym]
    jammy: [ros-humble-rc-common-msgs-dbgsym]
rc_dynamics_api:
  ubuntu:
    focal: [ros-humble-rc-dynamics-api]
    jammy: [ros-humble-rc-dynamics-api]
rc_dynamics_api_dbgsym:
  ubuntu:
    focal: [ros-humble-rc-dynamics-api-dbgsym]
    jammy: [ros-humble-rc-dynamics-api-dbgsym]
rc_genicam_api:
  ubuntu:
    focal: [ros-humble-rc-genicam-api]
    jammy: [ros-humble-rc-genicam-api]
rc_genicam_api_dbgsym:
  ubuntu:
    focal: [ros-humble-rc-genicam-api-dbgsym]
    jammy: [ros-humble-rc-genicam-api-dbgsym]
rc_genicam_driver:
  ubuntu:
    focal: [ros-humble-rc-genicam-driver]
    jammy: [ros-humble-rc-genicam-driver]
rc_genicam_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-rc-genicam-driver-dbgsym]
    jammy: [ros-humble-rc-genicam-driver-dbgsym]
rc_reason_clients:
  ubuntu:
    focal: [ros-humble-rc-reason-clients]
    jammy: [ros-humble-rc-reason-clients]
rc_reason_msgs:
  ubuntu:
    focal: [ros-humble-rc-reason-msgs]
    jammy: [ros-humble-rc-reason-msgs]
rc_reason_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rc-reason-msgs-dbgsym]
    jammy: [ros-humble-rc-reason-msgs-dbgsym]
rcdiscover:
  ubuntu:
    focal: [ros-humble-rcdiscover]
    jammy: [ros-humble-rcdiscover]
rcdiscover_dbgsym:
  ubuntu:
    focal: [ros-humble-rcdiscover-dbgsym]
    jammy: [ros-humble-rcdiscover-dbgsym]
rcgcd_spl_14:
  ubuntu:
    focal: [ros-humble-rcgcd-spl-14]
    jammy: [ros-humble-rcgcd-spl-14]
rcgcd_spl_14_conversion:
  ubuntu:
    focal: [ros-humble-rcgcd-spl-14-conversion]
    jammy: [ros-humble-rcgcd-spl-14-conversion]
rcgcd_spl_14_dbgsym:
  ubuntu:
    focal: [ros-humble-rcgcd-spl-14-dbgsym]
    jammy: [ros-humble-rcgcd-spl-14-dbgsym]
rcgcrd_spl_4:
  ubuntu:
    focal: [ros-humble-rcgcrd-spl-4]
    jammy: [ros-humble-rcgcrd-spl-4]
rcgcrd_spl_4_conversion:
  ubuntu:
    focal: [ros-humble-rcgcrd-spl-4-conversion]
    jammy: [ros-humble-rcgcrd-spl-4-conversion]
rcgcrd_spl_4_dbgsym:
  ubuntu:
    focal: [ros-humble-rcgcrd-spl-4-dbgsym]
    jammy: [ros-humble-rcgcrd-spl-4-dbgsym]
rcl:
  ubuntu:
    focal: [ros-humble-rcl]
    jammy: [ros-humble-rcl]
rcl_action:
  ubuntu:
    focal: [ros-humble-rcl-action]
    jammy: [ros-humble-rcl-action]
rcl_action_dbgsym:
  ubuntu:
    focal: [ros-humble-rcl-action-dbgsym]
    jammy: [ros-humble-rcl-action-dbgsym]
rcl_dbgsym:
  ubuntu:
    focal: [ros-humble-rcl-dbgsym]
    jammy: [ros-humble-rcl-dbgsym]
rcl_interfaces:
  ubuntu:
    focal: [ros-humble-rcl-interfaces]
    jammy: [ros-humble-rcl-interfaces]
rcl_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-rcl-interfaces-dbgsym]
    jammy: [ros-humble-rcl-interfaces-dbgsym]
rcl_lifecycle:
  ubuntu:
    focal: [ros-humble-rcl-lifecycle]
    jammy: [ros-humble-rcl-lifecycle]
rcl_lifecycle_dbgsym:
  ubuntu:
    focal: [ros-humble-rcl-lifecycle-dbgsym]
    jammy: [ros-humble-rcl-lifecycle-dbgsym]
rcl_logging_interface:
  ubuntu:
    focal: [ros-humble-rcl-logging-interface]
    jammy: [ros-humble-rcl-logging-interface]
rcl_logging_interface_dbgsym:
  ubuntu:
    focal: [ros-humble-rcl-logging-interface-dbgsym]
    jammy: [ros-humble-rcl-logging-interface-dbgsym]
rcl_logging_noop:
  ubuntu:
    focal: [ros-humble-rcl-logging-noop]
    jammy: [ros-humble-rcl-logging-noop]
rcl_logging_noop_dbgsym:
  ubuntu:
    focal: [ros-humble-rcl-logging-noop-dbgsym]
    jammy: [ros-humble-rcl-logging-noop-dbgsym]
rcl_logging_spdlog:
  ubuntu:
    focal: [ros-humble-rcl-logging-spdlog]
    jammy: [ros-humble-rcl-logging-spdlog]
rcl_logging_spdlog_dbgsym:
  ubuntu:
    focal: [ros-humble-rcl-logging-spdlog-dbgsym]
    jammy: [ros-humble-rcl-logging-spdlog-dbgsym]
rcl_yaml_param_parser:
  ubuntu:
    focal: [ros-humble-rcl-yaml-param-parser]
    jammy: [ros-humble-rcl-yaml-param-parser]
rcl_yaml_param_parser_dbgsym:
  ubuntu:
    focal: [ros-humble-rcl-yaml-param-parser-dbgsym]
    jammy: [ros-humble-rcl-yaml-param-parser-dbgsym]
rclc:
  ubuntu:
    focal: [ros-humble-rclc]
    jammy: [ros-humble-rclc]
rclc_dbgsym:
  ubuntu:
    focal: [ros-humble-rclc-dbgsym]
    jammy: [ros-humble-rclc-dbgsym]
rclc_examples:
  ubuntu:
    focal: [ros-humble-rclc-examples]
    jammy: [ros-humble-rclc-examples]
rclc_examples_dbgsym:
  ubuntu:
    focal: [ros-humble-rclc-examples-dbgsym]
    jammy: [ros-humble-rclc-examples-dbgsym]
rclc_lifecycle:
  ubuntu:
    focal: [ros-humble-rclc-lifecycle]
    jammy: [ros-humble-rclc-lifecycle]
rclc_lifecycle_dbgsym:
  ubuntu:
    focal: [ros-humble-rclc-lifecycle-dbgsym]
    jammy: [ros-humble-rclc-lifecycle-dbgsym]
rclc_parameter:
  ubuntu:
    focal: [ros-humble-rclc-parameter]
    jammy: [ros-humble-rclc-parameter]
rclcpp:
  ubuntu:
    focal: [ros-humble-rclcpp]
    jammy: [ros-humble-rclcpp]
rclcpp_action:
  ubuntu:
    focal: [ros-humble-rclcpp-action]
    jammy: [ros-humble-rclcpp-action]
rclcpp_action_dbgsym:
  ubuntu:
    focal: [ros-humble-rclcpp-action-dbgsym]
    jammy: [ros-humble-rclcpp-action-dbgsym]
rclcpp_cascade_lifecycle:
  ubuntu:
    focal: [ros-humble-rclcpp-cascade-lifecycle]
    jammy: [ros-humble-rclcpp-cascade-lifecycle]
rclcpp_cascade_lifecycle_dbgsym:
  ubuntu:
    focal: [ros-humble-rclcpp-cascade-lifecycle-dbgsym]
    jammy: [ros-humble-rclcpp-cascade-lifecycle-dbgsym]
rclcpp_components:
  ubuntu:
    focal: [ros-humble-rclcpp-components]
    jammy: [ros-humble-rclcpp-components]
rclcpp_components_dbgsym:
  ubuntu:
    focal: [ros-humble-rclcpp-components-dbgsym]
    jammy: [ros-humble-rclcpp-components-dbgsym]
rclcpp_dbgsym:
  ubuntu:
    focal: [ros-humble-rclcpp-dbgsym]
    jammy: [ros-humble-rclcpp-dbgsym]
rclcpp_lifecycle:
  ubuntu:
    focal: [ros-humble-rclcpp-lifecycle]
    jammy: [ros-humble-rclcpp-lifecycle]
rclcpp_lifecycle_dbgsym:
  ubuntu:
    focal: [ros-humble-rclcpp-lifecycle-dbgsym]
    jammy: [ros-humble-rclcpp-lifecycle-dbgsym]
rclpy:
  ubuntu:
    focal: [ros-humble-rclpy]
    jammy: [ros-humble-rclpy]
rclpy_message_converter:
  ubuntu:
    focal: [ros-humble-rclpy-message-converter]
    jammy: [ros-humble-rclpy-message-converter]
rclpy_message_converter_msgs:
  ubuntu:
    focal: [ros-humble-rclpy-message-converter-msgs]
    jammy: [ros-humble-rclpy-message-converter-msgs]
rclpy_message_converter_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rclpy-message-converter-msgs-dbgsym]
    jammy: [ros-humble-rclpy-message-converter-msgs-dbgsym]
rcpputils:
  ubuntu:
    focal: [ros-humble-rcpputils]
    jammy: [ros-humble-rcpputils]
rcpputils_dbgsym:
  ubuntu:
    focal: [ros-humble-rcpputils-dbgsym]
    jammy: [ros-humble-rcpputils-dbgsym]
rcss3d_agent:
  ubuntu:
    focal: [ros-humble-rcss3d-agent]
    jammy: [ros-humble-rcss3d-agent]
rcss3d_agent_basic:
  ubuntu:
    focal: [ros-humble-rcss3d-agent-basic]
    jammy: [ros-humble-rcss3d-agent-basic]
rcss3d_agent_basic_dbgsym:
  ubuntu:
    focal: [ros-humble-rcss3d-agent-basic-dbgsym]
    jammy: [ros-humble-rcss3d-agent-basic-dbgsym]
rcss3d_agent_dbgsym:
  ubuntu:
    focal: [ros-humble-rcss3d-agent-dbgsym]
    jammy: [ros-humble-rcss3d-agent-dbgsym]
rcss3d_agent_msgs:
  ubuntu:
    focal: [ros-humble-rcss3d-agent-msgs]
    jammy: [ros-humble-rcss3d-agent-msgs]
rcss3d_agent_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rcss3d-agent-msgs-dbgsym]
    jammy: [ros-humble-rcss3d-agent-msgs-dbgsym]
rcutils:
  ubuntu:
    focal: [ros-humble-rcutils]
    jammy: [ros-humble-rcutils]
rcutils_dbgsym:
  ubuntu:
    focal: [ros-humble-rcutils-dbgsym]
    jammy: [ros-humble-rcutils-dbgsym]
reach:
  ubuntu:
    focal: [ros-humble-reach]
    jammy: [ros-humble-reach]
reach_dbgsym:
  ubuntu:
    focal: [ros-humble-reach-dbgsym]
    jammy: [ros-humble-reach-dbgsym]
reach_ros:
  ubuntu:
    focal: [ros-humble-reach-ros]
    jammy: [ros-humble-reach-ros]
reach_ros_dbgsym:
  ubuntu:
    focal: [ros-humble-reach-ros-dbgsym]
    jammy: [ros-humble-reach-ros-dbgsym]
realsense2_camera:
  ubuntu:
    focal: [ros-humble-realsense2-camera]
    jammy: [ros-humble-realsense2-camera]
realsense2_camera_dbgsym:
  ubuntu:
    focal: [ros-humble-realsense2-camera-dbgsym]
    jammy: [ros-humble-realsense2-camera-dbgsym]
realsense2_camera_msgs:
  ubuntu:
    focal: [ros-humble-realsense2-camera-msgs]
    jammy: [ros-humble-realsense2-camera-msgs]
realsense2_camera_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-realsense2-camera-msgs-dbgsym]
    jammy: [ros-humble-realsense2-camera-msgs-dbgsym]
realsense2_description:
  ubuntu:
    focal: [ros-humble-realsense2-description]
    jammy: [ros-humble-realsense2-description]
realsense_splitter:
  ubuntu:
    focal: [ros-humble-realsense-splitter]
    jammy: [ros-humble-realsense-splitter]
realtime_tools:
  ubuntu:
    focal: [ros-humble-realtime-tools]
    jammy: [ros-humble-realtime-tools]
realtime_tools_dbgsym:
  ubuntu:
    focal: [ros-humble-realtime-tools-dbgsym]
    jammy: [ros-humble-realtime-tools-dbgsym]
resource_retriever:
  ubuntu:
    focal: [ros-humble-resource-retriever]
    jammy: [ros-humble-resource-retriever]
resource_retriever_dbgsym:
  ubuntu:
    focal: [ros-humble-resource-retriever-dbgsym]
    jammy: [ros-humble-resource-retriever-dbgsym]
rig_reconfigure:
  ubuntu:
    focal: [ros-humble-rig-reconfigure]
    jammy: [ros-humble-rig-reconfigure]
rig_reconfigure_dbgsym:
  ubuntu:
    focal: [ros-humble-rig-reconfigure-dbgsym]
    jammy: [ros-humble-rig-reconfigure-dbgsym]
rmf_api_msgs:
  ubuntu:
    focal: [ros-humble-rmf-api-msgs]
    jammy: [ros-humble-rmf-api-msgs]
rmf_battery:
  ubuntu:
    focal: [ros-humble-rmf-battery]
    jammy: [ros-humble-rmf-battery]
rmf_battery_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-battery-dbgsym]
    jammy: [ros-humble-rmf-battery-dbgsym]
rmf_building_map_msgs:
  ubuntu:
    focal: [ros-humble-rmf-building-map-msgs]
    jammy: [ros-humble-rmf-building-map-msgs]
rmf_building_map_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-building-map-msgs-dbgsym]
    jammy: [ros-humble-rmf-building-map-msgs-dbgsym]
rmf_building_map_tools:
  ubuntu:
    focal: [ros-humble-rmf-building-map-tools]
    jammy: [ros-humble-rmf-building-map-tools]
rmf_building_sim_common:
  ubuntu:
    focal: [ros-humble-rmf-building-sim-common]
    jammy: [ros-humble-rmf-building-sim-common]
rmf_building_sim_common_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-building-sim-common-dbgsym]
    jammy: [ros-humble-rmf-building-sim-common-dbgsym]
rmf_building_sim_gz_plugins:
  ubuntu:
    focal: [ros-humble-rmf-building-sim-gz-plugins]
    jammy: [ros-humble-rmf-building-sim-gz-plugins]
rmf_building_sim_gz_plugins_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-building-sim-gz-plugins-dbgsym]
    jammy: [ros-humble-rmf-building-sim-gz-plugins-dbgsym]
rmf_charger_msgs:
  ubuntu:
    focal: [ros-humble-rmf-charger-msgs]
    jammy: [ros-humble-rmf-charger-msgs]
rmf_charger_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-charger-msgs-dbgsym]
    jammy: [ros-humble-rmf-charger-msgs-dbgsym]
rmf_cmake_uncrustify:
  ubuntu:
    focal: [ros-humble-rmf-cmake-uncrustify]
    jammy: [ros-humble-rmf-cmake-uncrustify]
rmf_dispenser_msgs:
  ubuntu:
    focal: [ros-humble-rmf-dispenser-msgs]
    jammy: [ros-humble-rmf-dispenser-msgs]
rmf_dispenser_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-dispenser-msgs-dbgsym]
    jammy: [ros-humble-rmf-dispenser-msgs-dbgsym]
rmf_door_msgs:
  ubuntu:
    focal: [ros-humble-rmf-door-msgs]
    jammy: [ros-humble-rmf-door-msgs]
rmf_door_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-door-msgs-dbgsym]
    jammy: [ros-humble-rmf-door-msgs-dbgsym]
rmf_fleet_adapter:
  ubuntu:
    focal: [ros-humble-rmf-fleet-adapter]
    jammy: [ros-humble-rmf-fleet-adapter]
rmf_fleet_adapter_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-fleet-adapter-dbgsym]
    jammy: [ros-humble-rmf-fleet-adapter-dbgsym]
rmf_fleet_adapter_python:
  ubuntu:
    focal: [ros-humble-rmf-fleet-adapter-python]
    jammy: [ros-humble-rmf-fleet-adapter-python]
rmf_fleet_msgs:
  ubuntu:
    focal: [ros-humble-rmf-fleet-msgs]
    jammy: [ros-humble-rmf-fleet-msgs]
rmf_fleet_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-fleet-msgs-dbgsym]
    jammy: [ros-humble-rmf-fleet-msgs-dbgsym]
rmf_ingestor_msgs:
  ubuntu:
    focal: [ros-humble-rmf-ingestor-msgs]
    jammy: [ros-humble-rmf-ingestor-msgs]
rmf_ingestor_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-ingestor-msgs-dbgsym]
    jammy: [ros-humble-rmf-ingestor-msgs-dbgsym]
rmf_lift_msgs:
  ubuntu:
    focal: [ros-humble-rmf-lift-msgs]
    jammy: [ros-humble-rmf-lift-msgs]
rmf_lift_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-lift-msgs-dbgsym]
    jammy: [ros-humble-rmf-lift-msgs-dbgsym]
rmf_obstacle_msgs:
  ubuntu:
    focal: [ros-humble-rmf-obstacle-msgs]
    jammy: [ros-humble-rmf-obstacle-msgs]
rmf_obstacle_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-obstacle-msgs-dbgsym]
    jammy: [ros-humble-rmf-obstacle-msgs-dbgsym]
rmf_robot_sim_common:
  ubuntu:
    focal: [ros-humble-rmf-robot-sim-common]
    jammy: [ros-humble-rmf-robot-sim-common]
rmf_robot_sim_common_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-robot-sim-common-dbgsym]
    jammy: [ros-humble-rmf-robot-sim-common-dbgsym]
rmf_robot_sim_gz_plugins:
  ubuntu:
    focal: [ros-humble-rmf-robot-sim-gz-plugins]
    jammy: [ros-humble-rmf-robot-sim-gz-plugins]
rmf_robot_sim_gz_plugins_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-robot-sim-gz-plugins-dbgsym]
    jammy: [ros-humble-rmf-robot-sim-gz-plugins-dbgsym]
rmf_scheduler_msgs:
  ubuntu:
    focal: [ros-humble-rmf-scheduler-msgs]
    jammy: [ros-humble-rmf-scheduler-msgs]
rmf_scheduler_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-scheduler-msgs-dbgsym]
    jammy: [ros-humble-rmf-scheduler-msgs-dbgsym]
rmf_site_map_msgs:
  ubuntu:
    focal: [ros-humble-rmf-site-map-msgs]
    jammy: [ros-humble-rmf-site-map-msgs]
rmf_site_map_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-site-map-msgs-dbgsym]
    jammy: [ros-humble-rmf-site-map-msgs-dbgsym]
rmf_task:
  ubuntu:
    focal: [ros-humble-rmf-task]
    jammy: [ros-humble-rmf-task]
rmf_task_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-task-dbgsym]
    jammy: [ros-humble-rmf-task-dbgsym]
rmf_task_msgs:
  ubuntu:
    focal: [ros-humble-rmf-task-msgs]
    jammy: [ros-humble-rmf-task-msgs]
rmf_task_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-task-msgs-dbgsym]
    jammy: [ros-humble-rmf-task-msgs-dbgsym]
rmf_task_ros2:
  ubuntu:
    focal: [ros-humble-rmf-task-ros2]
    jammy: [ros-humble-rmf-task-ros2]
rmf_task_ros2_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-task-ros2-dbgsym]
    jammy: [ros-humble-rmf-task-ros2-dbgsym]
rmf_task_sequence:
  ubuntu:
    focal: [ros-humble-rmf-task-sequence]
    jammy: [ros-humble-rmf-task-sequence]
rmf_task_sequence_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-task-sequence-dbgsym]
    jammy: [ros-humble-rmf-task-sequence-dbgsym]
rmf_traffic:
  ubuntu:
    focal: [ros-humble-rmf-traffic]
    jammy: [ros-humble-rmf-traffic]
rmf_traffic_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-traffic-dbgsym]
    jammy: [ros-humble-rmf-traffic-dbgsym]
rmf_traffic_editor:
  ubuntu:
    focal: [ros-humble-rmf-traffic-editor]
    jammy: [ros-humble-rmf-traffic-editor]
rmf_traffic_editor_assets:
  ubuntu:
    focal: [ros-humble-rmf-traffic-editor-assets]
    jammy: [ros-humble-rmf-traffic-editor-assets]
rmf_traffic_editor_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-traffic-editor-dbgsym]
    jammy: [ros-humble-rmf-traffic-editor-dbgsym]
rmf_traffic_editor_test_maps:
  ubuntu:
    focal: [ros-humble-rmf-traffic-editor-test-maps]
    jammy: [ros-humble-rmf-traffic-editor-test-maps]
rmf_traffic_examples:
  ubuntu:
    focal: [ros-humble-rmf-traffic-examples]
    jammy: [ros-humble-rmf-traffic-examples]
rmf_traffic_examples_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-traffic-examples-dbgsym]
    jammy: [ros-humble-rmf-traffic-examples-dbgsym]
rmf_traffic_msgs:
  ubuntu:
    focal: [ros-humble-rmf-traffic-msgs]
    jammy: [ros-humble-rmf-traffic-msgs]
rmf_traffic_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-traffic-msgs-dbgsym]
    jammy: [ros-humble-rmf-traffic-msgs-dbgsym]
rmf_traffic_ros2:
  ubuntu:
    focal: [ros-humble-rmf-traffic-ros2]
    jammy: [ros-humble-rmf-traffic-ros2]
rmf_traffic_ros2_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-traffic-ros2-dbgsym]
    jammy: [ros-humble-rmf-traffic-ros2-dbgsym]
rmf_utils:
  ubuntu:
    focal: [ros-humble-rmf-utils]
    jammy: [ros-humble-rmf-utils]
rmf_utils_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-utils-dbgsym]
    jammy: [ros-humble-rmf-utils-dbgsym]
rmf_visualization:
  ubuntu:
    focal: [ros-humble-rmf-visualization]
    jammy: [ros-humble-rmf-visualization]
rmf_visualization_building_systems:
  ubuntu:
    focal: [ros-humble-rmf-visualization-building-systems]
    jammy: [ros-humble-rmf-visualization-building-systems]
rmf_visualization_fleet_states:
  ubuntu:
    focal: [ros-humble-rmf-visualization-fleet-states]
    jammy: [ros-humble-rmf-visualization-fleet-states]
rmf_visualization_fleet_states_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-visualization-fleet-states-dbgsym]
    jammy: [ros-humble-rmf-visualization-fleet-states-dbgsym]
rmf_visualization_floorplans:
  ubuntu:
    focal: [ros-humble-rmf-visualization-floorplans]
    jammy: [ros-humble-rmf-visualization-floorplans]
rmf_visualization_floorplans_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-visualization-floorplans-dbgsym]
    jammy: [ros-humble-rmf-visualization-floorplans-dbgsym]
rmf_visualization_msgs:
  ubuntu:
    focal: [ros-humble-rmf-visualization-msgs]
    jammy: [ros-humble-rmf-visualization-msgs]
rmf_visualization_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-visualization-msgs-dbgsym]
    jammy: [ros-humble-rmf-visualization-msgs-dbgsym]
rmf_visualization_navgraphs:
  ubuntu:
    focal: [ros-humble-rmf-visualization-navgraphs]
    jammy: [ros-humble-rmf-visualization-navgraphs]
rmf_visualization_navgraphs_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-visualization-navgraphs-dbgsym]
    jammy: [ros-humble-rmf-visualization-navgraphs-dbgsym]
rmf_visualization_obstacles:
  ubuntu:
    focal: [ros-humble-rmf-visualization-obstacles]
    jammy: [ros-humble-rmf-visualization-obstacles]
rmf_visualization_obstacles_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-visualization-obstacles-dbgsym]
    jammy: [ros-humble-rmf-visualization-obstacles-dbgsym]
rmf_visualization_rviz2_plugins:
  ubuntu:
    focal: [ros-humble-rmf-visualization-rviz2-plugins]
    jammy: [ros-humble-rmf-visualization-rviz2-plugins]
rmf_visualization_rviz2_plugins_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-visualization-rviz2-plugins-dbgsym]
    jammy: [ros-humble-rmf-visualization-rviz2-plugins-dbgsym]
rmf_visualization_schedule:
  ubuntu:
    focal: [ros-humble-rmf-visualization-schedule]
    jammy: [ros-humble-rmf-visualization-schedule]
rmf_visualization_schedule_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-visualization-schedule-dbgsym]
    jammy: [ros-humble-rmf-visualization-schedule-dbgsym]
rmf_websocket:
  ubuntu:
    focal: [ros-humble-rmf-websocket]
    jammy: [ros-humble-rmf-websocket]
rmf_websocket_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-websocket-dbgsym]
    jammy: [ros-humble-rmf-websocket-dbgsym]
rmf_workcell_msgs:
  ubuntu:
    focal: [ros-humble-rmf-workcell-msgs]
    jammy: [ros-humble-rmf-workcell-msgs]
rmf_workcell_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rmf-workcell-msgs-dbgsym]
    jammy: [ros-humble-rmf-workcell-msgs-dbgsym]
rmw:
  ubuntu:
    focal: [ros-humble-rmw]
    jammy: [ros-humble-rmw]
rmw_connextdds:
  ubuntu:
    focal: [ros-humble-rmw-connextdds]
    jammy: [ros-humble-rmw-connextdds]
rmw_connextdds_common:
  ubuntu:
    focal: [ros-humble-rmw-connextdds-common]
    jammy: [ros-humble-rmw-connextdds-common]
rmw_connextdds_common_dbgsym:
  ubuntu:
    focal: [ros-humble-rmw-connextdds-common-dbgsym]
    jammy: [ros-humble-rmw-connextdds-common-dbgsym]
rmw_connextdds_dbgsym:
  ubuntu:
    focal: [ros-humble-rmw-connextdds-dbgsym]
    jammy: [ros-humble-rmw-connextdds-dbgsym]
rmw_cyclonedds_cpp:
  ubuntu:
    focal: [ros-humble-rmw-cyclonedds-cpp]
    jammy: [ros-humble-rmw-cyclonedds-cpp]
rmw_cyclonedds_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-rmw-cyclonedds-cpp-dbgsym]
    jammy: [ros-humble-rmw-cyclonedds-cpp-dbgsym]
rmw_dbgsym:
  ubuntu:
    focal: [ros-humble-rmw-dbgsym]
    jammy: [ros-humble-rmw-dbgsym]
rmw_dds_common:
  ubuntu:
    focal: [ros-humble-rmw-dds-common]
    jammy: [ros-humble-rmw-dds-common]
rmw_dds_common_dbgsym:
  ubuntu:
    focal: [ros-humble-rmw-dds-common-dbgsym]
    jammy: [ros-humble-rmw-dds-common-dbgsym]
rmw_fastrtps_cpp:
  ubuntu:
    focal: [ros-humble-rmw-fastrtps-cpp]
    jammy: [ros-humble-rmw-fastrtps-cpp]
rmw_fastrtps_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-rmw-fastrtps-cpp-dbgsym]
    jammy: [ros-humble-rmw-fastrtps-cpp-dbgsym]
rmw_fastrtps_dynamic_cpp:
  ubuntu:
    focal: [ros-humble-rmw-fastrtps-dynamic-cpp]
    jammy: [ros-humble-rmw-fastrtps-dynamic-cpp]
rmw_fastrtps_dynamic_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-rmw-fastrtps-dynamic-cpp-dbgsym]
    jammy: [ros-humble-rmw-fastrtps-dynamic-cpp-dbgsym]
rmw_fastrtps_shared_cpp:
  ubuntu:
    focal: [ros-humble-rmw-fastrtps-shared-cpp]
    jammy: [ros-humble-rmw-fastrtps-shared-cpp]
rmw_fastrtps_shared_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-rmw-fastrtps-shared-cpp-dbgsym]
    jammy: [ros-humble-rmw-fastrtps-shared-cpp-dbgsym]
rmw_implementation:
  ubuntu:
    focal: [ros-humble-rmw-implementation]
    jammy: [ros-humble-rmw-implementation]
rmw_implementation_cmake:
  ubuntu:
    focal: [ros-humble-rmw-implementation-cmake]
    jammy: [ros-humble-rmw-implementation-cmake]
rmw_implementation_dbgsym:
  ubuntu:
    focal: [ros-humble-rmw-implementation-dbgsym]
    jammy: [ros-humble-rmw-implementation-dbgsym]
roboeval:
  ubuntu:
    focal: [ros-humble-roboeval]
    jammy: [ros-humble-roboeval]
roboeval_bringup:
  ubuntu:
    focal: [ros-humble-roboeval-bringup]
    jammy: [ros-humble-roboeval-bringup]
roboeval_commander:
  ubuntu:
    focal: [ros-humble-roboeval-commander]
    jammy: [ros-humble-roboeval-commander]
roboeval_compute_monitor:
  ubuntu:
    focal: [ros-humble-roboeval-compute-monitor]
    jammy: [ros-humble-roboeval-compute-monitor]
roboeval_evaluator:
  ubuntu:
    focal: [ros-humble-roboeval-evaluator]
    jammy: [ros-humble-roboeval-evaluator]
roboeval_interfaces:
  ubuntu:
    focal: [ros-humble-roboeval-interfaces]
    jammy: [ros-humble-roboeval-interfaces]
roboeval_runner:
  ubuntu:
    focal: [ros-humble-roboeval-runner]
    jammy: [ros-humble-roboeval-runner]
roboeval_utils:
  ubuntu:
    focal: [ros-humble-roboeval-utils]
    jammy: [ros-humble-roboeval-utils]
robot_calibration:
  ubuntu:
    focal: [ros-humble-robot-calibration]
    jammy: [ros-humble-robot-calibration]
robot_calibration_dbgsym:
  ubuntu:
    focal: [ros-humble-robot-calibration-dbgsym]
    jammy: [ros-humble-robot-calibration-dbgsym]
robot_calibration_msgs:
  ubuntu:
    focal: [ros-humble-robot-calibration-msgs]
    jammy: [ros-humble-robot-calibration-msgs]
robot_calibration_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-robot-calibration-msgs-dbgsym]
    jammy: [ros-humble-robot-calibration-msgs-dbgsym]
robot_controllers:
  ubuntu:
    focal: [ros-humble-robot-controllers]
    jammy: [ros-humble-robot-controllers]
robot_controllers_dbgsym:
  ubuntu:
    focal: [ros-humble-robot-controllers-dbgsym]
    jammy: [ros-humble-robot-controllers-dbgsym]
robot_controllers_interface:
  ubuntu:
    focal: [ros-humble-robot-controllers-interface]
    jammy: [ros-humble-robot-controllers-interface]
robot_controllers_interface_dbgsym:
  ubuntu:
    focal: [ros-humble-robot-controllers-interface-dbgsym]
    jammy: [ros-humble-robot-controllers-interface-dbgsym]
robot_controllers_msgs:
  ubuntu:
    focal: [ros-humble-robot-controllers-msgs]
    jammy: [ros-humble-robot-controllers-msgs]
robot_controllers_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-robot-controllers-msgs-dbgsym]
    jammy: [ros-humble-robot-controllers-msgs-dbgsym]
robot_localization:
  ubuntu:
    focal: [ros-humble-robot-localization]
    jammy: [ros-humble-robot-localization]
robot_localization_dbgsym:
  ubuntu:
    focal: [ros-humble-robot-localization-dbgsym]
    jammy: [ros-humble-robot-localization-dbgsym]
robot_state_publisher:
  ubuntu:
    focal: [ros-humble-robot-state-publisher]
    jammy: [ros-humble-robot-state-publisher]
robot_state_publisher_dbgsym:
  ubuntu:
    focal: [ros-humble-robot-state-publisher-dbgsym]
    jammy: [ros-humble-robot-state-publisher-dbgsym]
robot_upstart:
  ubuntu:
    focal: [ros-humble-robot-upstart]
    jammy: [ros-humble-robot-upstart]
robotiq_controllers:
  ubuntu:
    focal: [ros-humble-robotiq-controllers]
    jammy: [ros-humble-robotiq-controllers]
robotiq_controllers_dbgsym:
  ubuntu:
    focal: [ros-humble-robotiq-controllers-dbgsym]
    jammy: [ros-humble-robotiq-controllers-dbgsym]
robotiq_description:
  ubuntu:
    focal: [ros-humble-robotiq-description]
    jammy: [ros-humble-robotiq-description]
robotraconteur:
  ubuntu:
    focal: [ros-humble-robotraconteur]
    jammy: [ros-humble-robotraconteur]
robotraconteur_dbgsym:
  ubuntu:
    focal: [ros-humble-robotraconteur-dbgsym]
    jammy: [ros-humble-robotraconteur-dbgsym]
ros2_benchmark:
  ubuntu:
    focal: [ros-humble-ros2-benchmark]
    jammy: [ros-humble-ros2-benchmark]
ros2_benchmark_interfaces:
  ubuntu:
    focal: [ros-humble-ros2-benchmark-interfaces]
    jammy: [ros-humble-ros2-benchmark-interfaces]
ros2_benchmark_msgs:
  ubuntu:
    focal: [ros-noetic-ros2-benchmark-msgs]
    jammy: [ros-noetic-ros2-benchmark-msgs]
ros2_control:
  ubuntu:
    focal: [ros-humble-ros2-control]
    jammy: [ros-humble-ros2-control]
ros2_control_test_assets:
  ubuntu:
    focal: [ros-humble-ros2-control-test-assets]
    jammy: [ros-humble-ros2-control-test-assets]
ros2_controllers:
  ubuntu:
    focal: [ros-humble-ros2-controllers]
    jammy: [ros-humble-ros2-controllers]
ros2_controllers_test_nodes:
  ubuntu:
    focal: [ros-humble-ros2-controllers-test-nodes]
    jammy: [ros-humble-ros2-controllers-test-nodes]
ros2_socketcan:
  ubuntu:
    focal: [ros-humble-ros2-socketcan]
    jammy: [ros-humble-ros2-socketcan]
ros2_socketcan_dbgsym:
  ubuntu:
    focal: [ros-humble-ros2-socketcan-dbgsym]
    jammy: [ros-humble-ros2-socketcan-dbgsym]
ros2acceleration:
  ubuntu:
    focal: [ros-humble-ros2acceleration]
    jammy: [ros-humble-ros2acceleration]
ros2action:
  ubuntu:
    focal: [ros-humble-ros2action]
    jammy: [ros-humble-ros2action]
ros2bag:
  ubuntu:
    focal: [ros-humble-ros2bag]
    jammy: [ros-humble-ros2bag]
ros2bag_tools:
  ubuntu:
    focal: [ros-humble-ros2bag-tools]
    jammy: [ros-humble-ros2bag-tools]
ros2cli:
  ubuntu:
    focal: [ros-humble-ros2cli]
    jammy: [ros-humble-ros2cli]
ros2cli_common_extensions:
  ubuntu:
    focal: [ros-humble-ros2cli-common-extensions]
    jammy: [ros-humble-ros2cli-common-extensions]
ros2cli_test_interfaces:
  ubuntu:
    focal: [ros-humble-ros2cli-test-interfaces]
    jammy: [ros-humble-ros2cli-test-interfaces]
ros2cli_test_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-ros2cli-test-interfaces-dbgsym]
    jammy: [ros-humble-ros2cli-test-interfaces-dbgsym]
ros2component:
  ubuntu:
    focal: [ros-humble-ros2component]
    jammy: [ros-humble-ros2component]
ros2controlcli:
  ubuntu:
    focal: [ros-humble-ros2controlcli]
    jammy: [ros-humble-ros2controlcli]
ros2doctor:
  ubuntu:
    focal: [ros-humble-ros2doctor]
    jammy: [ros-humble-ros2doctor]
ros2interface:
  ubuntu:
    focal: [ros-humble-ros2interface]
    jammy: [ros-humble-ros2interface]
ros2launch:
  ubuntu:
    focal: [ros-humble-ros2launch]
    jammy: [ros-humble-ros2launch]
ros2launch_security:
  ubuntu:
    focal: [ros-humble-ros2launch-security]
    jammy: [ros-humble-ros2launch-security]
ros2launch_security_examples:
  ubuntu:
    focal: [ros-humble-ros2launch-security-examples]
    jammy: [ros-humble-ros2launch-security-examples]
ros2launch_security_examples_dbgsym:
  ubuntu:
    focal: [ros-humble-ros2launch-security-examples-dbgsym]
    jammy: [ros-humble-ros2launch-security-examples-dbgsym]
ros2lifecycle:
  ubuntu:
    focal: [ros-humble-ros2lifecycle]
    jammy: [ros-humble-ros2lifecycle]
ros2lifecycle_test_fixtures:
  ubuntu:
    focal: [ros-humble-ros2lifecycle-test-fixtures]
    jammy: [ros-humble-ros2lifecycle-test-fixtures]
ros2lifecycle_test_fixtures_dbgsym:
  ubuntu:
    focal: [ros-humble-ros2lifecycle-test-fixtures-dbgsym]
    jammy: [ros-humble-ros2lifecycle-test-fixtures-dbgsym]
ros2multicast:
  ubuntu:
    focal: [ros-humble-ros2multicast]
    jammy: [ros-humble-ros2multicast]
ros2node:
  ubuntu:
    focal: [ros-humble-ros2node]
    jammy: [ros-humble-ros2node]
ros2nodl:
  ubuntu:
    focal: [ros-humble-ros2nodl]
    jammy: [ros-humble-ros2nodl]
ros2param:
  ubuntu:
    focal: [ros-humble-ros2param]
    jammy: [ros-humble-ros2param]
ros2pkg:
  ubuntu:
    focal: [ros-humble-ros2pkg]
    jammy: [ros-humble-ros2pkg]
ros2run:
  ubuntu:
    focal: [ros-humble-ros2run]
    jammy: [ros-humble-ros2run]
ros2service:
  ubuntu:
    focal: [ros-humble-ros2service]
    jammy: [ros-humble-ros2service]
ros2test:
  ubuntu:
    focal: [ros-humble-ros2test]
    jammy: [ros-humble-ros2test]
ros2topic:
  ubuntu:
    focal: [ros-humble-ros2topic]
    jammy: [ros-humble-ros2topic]
ros2trace:
  ubuntu:
    focal: [ros-humble-ros2trace]
    jammy: [ros-humble-ros2trace]
ros2trace_analysis:
  ubuntu:
    focal: [ros-humble-ros2trace-analysis]
    jammy: [ros-humble-ros2trace-analysis]
ros_base:
  ubuntu:
    focal: [ros-humble-ros-base]
    jammy: [ros-humble-ros-base]
ros_core:
  ubuntu:
    focal: [ros-humble-ros-core]
    jammy: [ros-humble-ros-core]
ros_environment:
  ubuntu:
    focal: [ros-humble-ros-environment]
    jammy: [ros-humble-ros-environment]
ros_gz:
  ubuntu:
    focal: [ros-humble-ros-gz]
    jammy: [ros-humble-ros-gz]
ros_gz_bridge:
  ubuntu:
    focal: [ros-humble-ros-gz-bridge]
    jammy: [ros-humble-ros-gz-bridge]
ros_gz_bridge_dbgsym:
  ubuntu:
    focal: [ros-humble-ros-gz-bridge-dbgsym]
    jammy: [ros-humble-ros-gz-bridge-dbgsym]
ros_gz_image:
  ubuntu:
    focal: [ros-humble-ros-gz-image]
    jammy: [ros-humble-ros-gz-image]
ros_gz_image_dbgsym:
  ubuntu:
    focal: [ros-humble-ros-gz-image-dbgsym]
    jammy: [ros-humble-ros-gz-image-dbgsym]
ros_gz_interfaces:
  ubuntu:
    focal: [ros-humble-ros-gz-interfaces]
    jammy: [ros-humble-ros-gz-interfaces]
ros_gz_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-ros-gz-interfaces-dbgsym]
    jammy: [ros-humble-ros-gz-interfaces-dbgsym]
ros_gz_sim:
  ubuntu:
    focal: [ros-humble-ros-gz-sim]
    jammy: [ros-humble-ros-gz-sim]
ros_gz_sim_dbgsym:
  ubuntu:
    focal: [ros-humble-ros-gz-sim-dbgsym]
    jammy: [ros-humble-ros-gz-sim-dbgsym]
ros_gz_sim_demos:
  ubuntu:
    focal: [ros-humble-ros-gz-sim-demos]
    jammy: [ros-humble-ros-gz-sim-demos]
ros_ign:
  ubuntu:
    focal: [ros-humble-ros-ign]
    jammy: [ros-humble-ros-ign]
ros_ign_bridge:
  ubuntu:
    focal: [ros-humble-ros-ign-bridge]
    jammy: [ros-humble-ros-ign-bridge]
ros_ign_bridge_dbgsym:
  ubuntu:
    focal: [ros-humble-ros-ign-bridge-dbgsym]
    jammy: [ros-humble-ros-ign-bridge-dbgsym]
ros_ign_gazebo:
  ubuntu:
    focal: [ros-humble-ros-ign-gazebo]
    jammy: [ros-humble-ros-ign-gazebo]
ros_ign_gazebo_dbgsym:
  ubuntu:
    focal: [ros-humble-ros-ign-gazebo-dbgsym]
    jammy: [ros-humble-ros-ign-gazebo-dbgsym]
ros_ign_gazebo_demos:
  ubuntu:
    focal: [ros-humble-ros-ign-gazebo-demos]
    jammy: [ros-humble-ros-ign-gazebo-demos]
ros_ign_image:
  ubuntu:
    focal: [ros-humble-ros-ign-image]
    jammy: [ros-humble-ros-ign-image]
ros_ign_image_dbgsym:
  ubuntu:
    focal: [ros-humble-ros-ign-image-dbgsym]
    jammy: [ros-humble-ros-ign-image-dbgsym]
ros_ign_interfaces:
  ubuntu:
    focal: [ros-humble-ros-ign-interfaces]
    jammy: [ros-humble-ros-ign-interfaces]
ros_ign_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-ros-ign-interfaces-dbgsym]
    jammy: [ros-humble-ros-ign-interfaces-dbgsym]
ros_image_to_qimage:
  ubuntu:
    focal: [ros-humble-ros-image-to-qimage]
    jammy: [ros-humble-ros-image-to-qimage]
ros_industrial_cmake_boilerplate:
  ubuntu:
    focal: [ros-humble-ros-industrial-cmake-boilerplate]
    jammy: [ros-humble-ros-industrial-cmake-boilerplate]
ros_testing:
  ubuntu:
    focal: [ros-humble-ros-testing]
    jammy: [ros-humble-ros-testing]
ros_workspace:
  ubuntu:
    focal: [ros-humble-ros-workspace]
    jammy: [ros-humble-ros-workspace]
rosapi:
  ubuntu:
    focal: [ros-humble-rosapi]
    jammy: [ros-humble-rosapi]
rosapi_msgs:
  ubuntu:
    focal: [ros-humble-rosapi-msgs]
    jammy: [ros-humble-rosapi-msgs]
rosapi_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rosapi-msgs-dbgsym]
    jammy: [ros-humble-rosapi-msgs-dbgsym]
rosbag2:
  ubuntu:
    focal: [ros-humble-rosbag2]
    jammy: [ros-humble-rosbag2]
rosbag2_compression:
  ubuntu:
    focal: [ros-humble-rosbag2-compression]
    jammy: [ros-humble-rosbag2-compression]
rosbag2_compression_dbgsym:
  ubuntu:
    focal: [ros-humble-rosbag2-compression-dbgsym]
    jammy: [ros-humble-rosbag2-compression-dbgsym]
rosbag2_compression_zstd:
  ubuntu:
    focal: [ros-humble-rosbag2-compression-zstd]
    jammy: [ros-humble-rosbag2-compression-zstd]
rosbag2_compression_zstd_dbgsym:
  ubuntu:
    focal: [ros-humble-rosbag2-compression-zstd-dbgsym]
    jammy: [ros-humble-rosbag2-compression-zstd-dbgsym]
rosbag2_cpp:
  ubuntu:
    focal: [ros-humble-rosbag2-cpp]
    jammy: [ros-humble-rosbag2-cpp]
rosbag2_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-rosbag2-cpp-dbgsym]
    jammy: [ros-humble-rosbag2-cpp-dbgsym]
rosbag2_interfaces:
  ubuntu:
    focal: [ros-humble-rosbag2-interfaces]
    jammy: [ros-humble-rosbag2-interfaces]
rosbag2_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-rosbag2-interfaces-dbgsym]
    jammy: [ros-humble-rosbag2-interfaces-dbgsym]
rosbag2_performance_benchmarking:
  ubuntu:
    focal: [ros-humble-rosbag2-performance-benchmarking]
    jammy: [ros-humble-rosbag2-performance-benchmarking]
rosbag2_py:
  ubuntu:
    focal: [ros-humble-rosbag2-py]
    jammy: [ros-humble-rosbag2-py]
rosbag2_storage:
  ubuntu:
    focal: [ros-humble-rosbag2-storage]
    jammy: [ros-humble-rosbag2-storage]
rosbag2_storage_dbgsym:
  ubuntu:
    focal: [ros-humble-rosbag2-storage-dbgsym]
    jammy: [ros-humble-rosbag2-storage-dbgsym]
rosbag2_storage_default_plugins:
  ubuntu:
    focal: [ros-humble-rosbag2-storage-default-plugins]
    jammy: [ros-humble-rosbag2-storage-default-plugins]
rosbag2_storage_default_plugins_dbgsym:
  ubuntu:
    focal: [ros-humble-rosbag2-storage-default-plugins-dbgsym]
    jammy: [ros-humble-rosbag2-storage-default-plugins-dbgsym]
rosbag2_storage_mcap:
  ubuntu:
    focal: [ros-humble-rosbag2-storage-mcap]
    jammy: [ros-humble-rosbag2-storage-mcap]
rosbag2_storage_mcap_dbgsym:
  ubuntu:
    focal: [ros-humble-rosbag2-storage-mcap-dbgsym]
    jammy: [ros-humble-rosbag2-storage-mcap-dbgsym]
rosbag2_storage_mcap_testdata:
  ubuntu:
    focal: [ros-humble-rosbag2-storage-mcap-testdata]
    jammy: [ros-humble-rosbag2-storage-mcap-testdata]
rosbag2_storage_mcap_testdata_dbgsym:
  ubuntu:
    focal: [ros-humble-rosbag2-storage-mcap-testdata-dbgsym]
    jammy: [ros-humble-rosbag2-storage-mcap-testdata-dbgsym]
rosbag2_test_common:
  ubuntu:
    focal: [ros-humble-rosbag2-test-common]
    jammy: [ros-humble-rosbag2-test-common]
rosbag2_tests:
  ubuntu:
    focal: [ros-humble-rosbag2-tests]
    jammy: [ros-humble-rosbag2-tests]
rosbag2_tools:
  ubuntu:
    focal: [ros-humble-rosbag2-tools]
    jammy: [ros-humble-rosbag2-tools]
rosbag2_transport:
  ubuntu:
    focal: [ros-humble-rosbag2-transport]
    jammy: [ros-humble-rosbag2-transport]
rosbag2_transport_dbgsym:
  ubuntu:
    focal: [ros-humble-rosbag2-transport-dbgsym]
    jammy: [ros-humble-rosbag2-transport-dbgsym]
rosbridge_library:
  ubuntu:
    focal: [ros-humble-rosbridge-library]
    jammy: [ros-humble-rosbridge-library]
rosbridge_msgs:
  ubuntu:
    focal: [ros-humble-rosbridge-msgs]
    jammy: [ros-humble-rosbridge-msgs]
rosbridge_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rosbridge-msgs-dbgsym]
    jammy: [ros-humble-rosbridge-msgs-dbgsym]
rosbridge_server:
  ubuntu:
    focal: [ros-humble-rosbridge-server]
    jammy: [ros-humble-rosbridge-server]
rosbridge_suite:
  ubuntu:
    focal: [ros-humble-rosbridge-suite]
    jammy: [ros-humble-rosbridge-suite]
rosbridge_test_msgs:
  ubuntu:
    focal: [ros-humble-rosbridge-test-msgs]
    jammy: [ros-humble-rosbridge-test-msgs]
rosbridge_test_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rosbridge-test-msgs-dbgsym]
    jammy: [ros-humble-rosbridge-test-msgs-dbgsym]
roscpp:
  ubuntu:
    focal: [ros-noetic-roscpp]
    jammy: [ros-noetic-roscpp]
rosgraph_msgs:
  ubuntu:
    focal: [ros-humble-rosgraph-msgs]
    jammy: [ros-humble-rosgraph-msgs]
rosgraph_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rosgraph-msgs-dbgsym]
    jammy: [ros-humble-rosgraph-msgs-dbgsym]
rosidl_adapter:
  ubuntu:
    focal: [ros-humble-rosidl-adapter]
    jammy: [ros-humble-rosidl-adapter]
rosidl_cli:
  ubuntu:
    focal: [ros-humble-rosidl-cli]
    jammy: [ros-humble-rosidl-cli]
rosidl_cmake:
  ubuntu:
    focal: [ros-humble-rosidl-cmake]
    jammy: [ros-humble-rosidl-cmake]
rosidl_default_generators:
  ubuntu:
    focal: [ros-humble-rosidl-default-generators]
    jammy: [ros-humble-rosidl-default-generators]
rosidl_default_runtime:
  ubuntu:
    focal: [ros-humble-rosidl-default-runtime]
    jammy: [ros-humble-rosidl-default-runtime]
rosidl_generator_c:
  ubuntu:
    focal: [ros-humble-rosidl-generator-c]
    jammy: [ros-humble-rosidl-generator-c]
rosidl_generator_cpp:
  ubuntu:
    focal: [ros-humble-rosidl-generator-cpp]
    jammy: [ros-humble-rosidl-generator-cpp]
rosidl_generator_dds_idl:
  ubuntu:
    focal: [ros-humble-rosidl-generator-dds-idl]
    jammy: [ros-humble-rosidl-generator-dds-idl]
rosidl_generator_py:
  ubuntu:
    focal: [ros-humble-rosidl-generator-py]
    jammy: [ros-humble-rosidl-generator-py]
rosidl_parser:
  ubuntu:
    focal: [ros-humble-rosidl-parser]
    jammy: [ros-humble-rosidl-parser]
rosidl_runtime_c:
  ubuntu:
    focal: [ros-humble-rosidl-runtime-c]
    jammy: [ros-humble-rosidl-runtime-c]
rosidl_runtime_c_dbgsym:
  ubuntu:
    focal: [ros-humble-rosidl-runtime-c-dbgsym]
    jammy: [ros-humble-rosidl-runtime-c-dbgsym]
rosidl_runtime_cpp:
  ubuntu:
    focal: [ros-humble-rosidl-runtime-cpp]
    jammy: [ros-humble-rosidl-runtime-cpp]
rosidl_runtime_py:
  ubuntu:
    focal: [ros-humble-rosidl-runtime-py]
    jammy: [ros-humble-rosidl-runtime-py]
rosidl_typesupport_c:
  ubuntu:
    focal: [ros-humble-rosidl-typesupport-c]
    jammy: [ros-humble-rosidl-typesupport-c]
rosidl_typesupport_c_dbgsym:
  ubuntu:
    focal: [ros-humble-rosidl-typesupport-c-dbgsym]
    jammy: [ros-humble-rosidl-typesupport-c-dbgsym]
rosidl_typesupport_cpp:
  ubuntu:
    focal: [ros-humble-rosidl-typesupport-cpp]
    jammy: [ros-humble-rosidl-typesupport-cpp]
rosidl_typesupport_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-rosidl-typesupport-cpp-dbgsym]
    jammy: [ros-humble-rosidl-typesupport-cpp-dbgsym]
rosidl_typesupport_fastrtps_c:
  ubuntu:
    focal: [ros-humble-rosidl-typesupport-fastrtps-c]
    jammy: [ros-humble-rosidl-typesupport-fastrtps-c]
rosidl_typesupport_fastrtps_c_dbgsym:
  ubuntu:
    focal: [ros-humble-rosidl-typesupport-fastrtps-c-dbgsym]
    jammy: [ros-humble-rosidl-typesupport-fastrtps-c-dbgsym]
rosidl_typesupport_fastrtps_cpp:
  ubuntu:
    focal: [ros-humble-rosidl-typesupport-fastrtps-cpp]
    jammy: [ros-humble-rosidl-typesupport-fastrtps-cpp]
rosidl_typesupport_fastrtps_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-rosidl-typesupport-fastrtps-cpp-dbgsym]
    jammy: [ros-humble-rosidl-typesupport-fastrtps-cpp-dbgsym]
rosidl_typesupport_interface:
  ubuntu:
    focal: [ros-humble-rosidl-typesupport-interface]
    jammy: [ros-humble-rosidl-typesupport-interface]
rosidl_typesupport_introspection_c:
  ubuntu:
    focal: [ros-humble-rosidl-typesupport-introspection-c]
    jammy: [ros-humble-rosidl-typesupport-introspection-c]
rosidl_typesupport_introspection_c_dbgsym:
  ubuntu:
    focal: [ros-humble-rosidl-typesupport-introspection-c-dbgsym]
    jammy: [ros-humble-rosidl-typesupport-introspection-c-dbgsym]
rosidl_typesupport_introspection_cpp:
  ubuntu:
    focal: [ros-humble-rosidl-typesupport-introspection-cpp]
    jammy: [ros-humble-rosidl-typesupport-introspection-cpp]
rosidl_typesupport_introspection_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-rosidl-typesupport-introspection-cpp-dbgsym]
    jammy: [ros-humble-rosidl-typesupport-introspection-cpp-dbgsym]
roslib:
  ubuntu:
    focal: [python3-roslib]
    jammy: [python3-roslib]
rosparam_shortcuts:
  ubuntu:
    focal: [ros-humble-rosparam-shortcuts]
    jammy: [ros-humble-rosparam-shortcuts]
rospy:
  ubuntu:
    focal: [ros-noetic-rospy]
    jammy: [ros-noetic-rospy]
rosx_introspection:
  ubuntu:
    focal: [ros-humble-rosx-introspection]
    jammy: [ros-humble-rosx-introspection]
rot_conv:
  ubuntu:
    focal: [ros-humble-rot-conv]
    jammy: [ros-humble-rot-conv]
rot_conv_dbgsym:
  ubuntu:
    focal: [ros-humble-rot-conv-dbgsym]
    jammy: [ros-humble-rot-conv-dbgsym]
rplidar_ros:
  ubuntu:
    focal: [ros-humble-rplidar-ros]
    jammy: [ros-humble-rplidar-ros]
rplidar_ros_dbgsym:
  ubuntu:
    focal: [ros-humble-rplidar-ros-dbgsym]
    jammy: [ros-humble-rplidar-ros-dbgsym]
rplidar_s2e_description:
  ubuntu:
    focal: [ros-humble-rplidar-s2e-description]
    jammy: [ros-humble-rplidar-s2e-description]
rpyutils:
  ubuntu:
    focal: [ros-humble-rpyutils]
    jammy: [ros-humble-rpyutils]
rqt:
  ubuntu:
    focal: [ros-humble-rqt]
    jammy: [ros-humble-rqt]
rqt_action:
  ubuntu:
    focal: [ros-humble-rqt-action]
    jammy: [ros-humble-rqt-action]
rqt_bag:
  ubuntu:
    focal: [ros-humble-rqt-bag]
    jammy: [ros-humble-rqt-bag]
rqt_bag_plugins:
  ubuntu:
    focal: [ros-humble-rqt-bag-plugins]
    jammy: [ros-humble-rqt-bag-plugins]
rqt_common_plugins:
  ubuntu:
    focal: [ros-humble-rqt-common-plugins]
    jammy: [ros-humble-rqt-common-plugins]
rqt_console:
  ubuntu:
    focal: [ros-humble-rqt-console]
    jammy: [ros-humble-rqt-console]
rqt_controller_manager:
  ubuntu:
    focal: [ros-humble-rqt-controller-manager]
    jammy: [ros-humble-rqt-controller-manager]
rqt_graph:
  ubuntu:
    focal: [ros-humble-rqt-graph]
    jammy: [ros-humble-rqt-graph]
rqt_gui:
  ubuntu:
    focal: [ros-humble-rqt-gui]
    jammy: [ros-humble-rqt-gui]
rqt_gui_cpp:
  ubuntu:
    focal: [ros-humble-rqt-gui-cpp]
    jammy: [ros-humble-rqt-gui-cpp]
rqt_gui_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-rqt-gui-cpp-dbgsym]
    jammy: [ros-humble-rqt-gui-cpp-dbgsym]
rqt_gui_py:
  ubuntu:
    focal: [ros-humble-rqt-gui-py]
    jammy: [ros-humble-rqt-gui-py]
rqt_image_overlay:
  ubuntu:
    focal: [ros-humble-rqt-image-overlay]
    jammy: [ros-humble-rqt-image-overlay]
rqt_image_overlay_dbgsym:
  ubuntu:
    focal: [ros-humble-rqt-image-overlay-dbgsym]
    jammy: [ros-humble-rqt-image-overlay-dbgsym]
rqt_image_overlay_layer:
  ubuntu:
    focal: [ros-humble-rqt-image-overlay-layer]
    jammy: [ros-humble-rqt-image-overlay-layer]
rqt_image_view:
  ubuntu:
    focal: [ros-humble-rqt-image-view]
    jammy: [ros-humble-rqt-image-view]
rqt_image_view_dbgsym:
  ubuntu:
    focal: [ros-humble-rqt-image-view-dbgsym]
    jammy: [ros-humble-rqt-image-view-dbgsym]
rqt_joint_trajectory_controller:
  ubuntu:
    focal: [ros-humble-rqt-joint-trajectory-controller]
    jammy: [ros-humble-rqt-joint-trajectory-controller]
rqt_moveit:
  ubuntu:
    focal: [ros-humble-rqt-moveit]
    jammy: [ros-humble-rqt-moveit]
rqt_msg:
  ubuntu:
    focal: [ros-humble-rqt-msg]
    jammy: [ros-humble-rqt-msg]
rqt_plot:
  ubuntu:
    focal: [ros-humble-rqt-plot]
    jammy: [ros-humble-rqt-plot]
rqt_publisher:
  ubuntu:
    focal: [ros-humble-rqt-publisher]
    jammy: [ros-humble-rqt-publisher]
rqt_py_common:
  ubuntu:
    focal: [ros-humble-rqt-py-common]
    jammy: [ros-humble-rqt-py-common]
rqt_py_console:
  ubuntu:
    focal: [ros-humble-rqt-py-console]
    jammy: [ros-humble-rqt-py-console]
rqt_reconfigure:
  ubuntu:
    focal: [ros-humble-rqt-reconfigure]
    jammy: [ros-humble-rqt-reconfigure]
rqt_robot_dashboard:
  ubuntu:
    focal: [ros-humble-rqt-robot-dashboard]
    jammy: [ros-humble-rqt-robot-dashboard]
rqt_robot_monitor:
  ubuntu:
    focal: [ros-humble-rqt-robot-monitor]
    jammy: [ros-humble-rqt-robot-monitor]
rqt_robot_steering:
  ubuntu:
    focal: [ros-humble-rqt-robot-steering]
    jammy: [ros-humble-rqt-robot-steering]
rqt_runtime_monitor:
  ubuntu:
    focal: [ros-humble-rqt-runtime-monitor]
    jammy: [ros-humble-rqt-runtime-monitor]
rqt_service_caller:
  ubuntu:
    focal: [ros-humble-rqt-service-caller]
    jammy: [ros-humble-rqt-service-caller]
rqt_shell:
  ubuntu:
    focal: [ros-humble-rqt-shell]
    jammy: [ros-humble-rqt-shell]
rqt_srv:
  ubuntu:
    focal: [ros-humble-rqt-srv]
    jammy: [ros-humble-rqt-srv]
rqt_tf_tree:
  ubuntu:
    focal: [ros-humble-rqt-tf-tree]
    jammy: [ros-humble-rqt-tf-tree]
rqt_topic:
  ubuntu:
    focal: [ros-humble-rqt-topic]
    jammy: [ros-humble-rqt-topic]
rsl:
  ubuntu:
    focal: [ros-humble-rsl]
    jammy: [ros-humble-rsl]
rsl_dbgsym:
  ubuntu:
    focal: [ros-humble-rsl-dbgsym]
    jammy: [ros-humble-rsl-dbgsym]
rt_manipulators_cpp:
  ubuntu:
    focal: [ros-humble-rt-manipulators-cpp]
    jammy: [ros-humble-rt-manipulators-cpp]
rt_manipulators_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-rt-manipulators-cpp-dbgsym]
    jammy: [ros-humble-rt-manipulators-cpp-dbgsym]
rt_manipulators_examples:
  ubuntu:
    focal: [ros-humble-rt-manipulators-examples]
    jammy: [ros-humble-rt-manipulators-examples]
rt_manipulators_examples_dbgsym:
  ubuntu:
    focal: [ros-humble-rt-manipulators-examples-dbgsym]
    jammy: [ros-humble-rt-manipulators-examples-dbgsym]
rt_usb_9axisimu_driver:
  ubuntu:
    focal: [ros-humble-rt-usb-9axisimu-driver]
    jammy: [ros-humble-rt-usb-9axisimu-driver]
rt_usb_9axisimu_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-rt-usb-9axisimu-driver-dbgsym]
    jammy: [ros-humble-rt-usb-9axisimu-driver-dbgsym]
rtabmap:
  ubuntu:
    focal: [ros-humble-rtabmap]
    jammy: [ros-humble-rtabmap]
rtabmap_conversions:
  ubuntu:
    focal: [ros-humble-rtabmap-conversions]
    jammy: [ros-humble-rtabmap-conversions]
rtabmap_conversions_dbgsym:
  ubuntu:
    focal: [ros-humble-rtabmap-conversions-dbgsym]
    jammy: [ros-humble-rtabmap-conversions-dbgsym]
rtabmap_dbgsym:
  ubuntu:
    focal: [ros-humble-rtabmap-dbgsym]
    jammy: [ros-humble-rtabmap-dbgsym]
rtabmap_demos:
  ubuntu:
    focal: [ros-humble-rtabmap-demos]
    jammy: [ros-humble-rtabmap-demos]
rtabmap_examples:
  ubuntu:
    focal: [ros-humble-rtabmap-examples]
    jammy: [ros-humble-rtabmap-examples]
rtabmap_launch:
  ubuntu:
    focal: [ros-humble-rtabmap-launch]
    jammy: [ros-humble-rtabmap-launch]
rtabmap_msgs:
  ubuntu:
    focal: [ros-humble-rtabmap-msgs]
    jammy: [ros-humble-rtabmap-msgs]
rtabmap_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rtabmap-msgs-dbgsym]
    jammy: [ros-humble-rtabmap-msgs-dbgsym]
rtabmap_odom:
  ubuntu:
    focal: [ros-humble-rtabmap-odom]
    jammy: [ros-humble-rtabmap-odom]
rtabmap_odom_dbgsym:
  ubuntu:
    focal: [ros-humble-rtabmap-odom-dbgsym]
    jammy: [ros-humble-rtabmap-odom-dbgsym]
rtabmap_python:
  ubuntu:
    focal: [ros-humble-rtabmap-python]
    jammy: [ros-humble-rtabmap-python]
rtabmap_ros:
  ubuntu:
    focal: [ros-humble-rtabmap-ros]
    jammy: [ros-humble-rtabmap-ros]
rtabmap_rviz_plugins:
  ubuntu:
    focal: [ros-humble-rtabmap-rviz-plugins]
    jammy: [ros-humble-rtabmap-rviz-plugins]
rtabmap_rviz_plugins_dbgsym:
  ubuntu:
    focal: [ros-humble-rtabmap-rviz-plugins-dbgsym]
    jammy: [ros-humble-rtabmap-rviz-plugins-dbgsym]
rtabmap_slam:
  ubuntu:
    focal: [ros-humble-rtabmap-slam]
    jammy: [ros-humble-rtabmap-slam]
rtabmap_slam_dbgsym:
  ubuntu:
    focal: [ros-humble-rtabmap-slam-dbgsym]
    jammy: [ros-humble-rtabmap-slam-dbgsym]
rtabmap_sync:
  ubuntu:
    focal: [ros-humble-rtabmap-sync]
    jammy: [ros-humble-rtabmap-sync]
rtabmap_sync_dbgsym:
  ubuntu:
    focal: [ros-humble-rtabmap-sync-dbgsym]
    jammy: [ros-humble-rtabmap-sync-dbgsym]
rtabmap_util:
  ubuntu:
    focal: [ros-humble-rtabmap-util]
    jammy: [ros-humble-rtabmap-util]
rtabmap_util_dbgsym:
  ubuntu:
    focal: [ros-humble-rtabmap-util-dbgsym]
    jammy: [ros-humble-rtabmap-util-dbgsym]
rtabmap_viz:
  ubuntu:
    focal: [ros-humble-rtabmap-viz]
    jammy: [ros-humble-rtabmap-viz]
rtabmap_viz_dbgsym:
  ubuntu:
    focal: [ros-humble-rtabmap-viz-dbgsym]
    jammy: [ros-humble-rtabmap-viz-dbgsym]
rtcm_msgs:
  ubuntu:
    focal: [ros-humble-rtcm-msgs]
    jammy: [ros-humble-rtcm-msgs]
rtcm_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rtcm-msgs-dbgsym]
    jammy: [ros-humble-rtcm-msgs-dbgsym]
rti_connext_dds_cmake_module:
  ubuntu:
    focal: [ros-humble-rti-connext-dds-cmake-module]
    jammy: [ros-humble-rti-connext-dds-cmake-module]
rttest:
  ubuntu:
    focal: [ros-humble-rttest]
    jammy: [ros-humble-rttest]
rttest_dbgsym:
  ubuntu:
    focal: [ros-humble-rttest-dbgsym]
    jammy: [ros-humble-rttest-dbgsym]
ruckig:
  ubuntu:
    focal: [ros-humble-ruckig]
    jammy: [ros-humble-ruckig]
ruckig_dbgsym:
  ubuntu:
    focal: [ros-humble-ruckig-dbgsym]
    jammy: [ros-humble-ruckig-dbgsym]
rviz2:
  ubuntu:
    focal: [ros-humble-rviz2]
    jammy: [ros-humble-rviz2]
rviz2_dbgsym:
  ubuntu:
    focal: [ros-humble-rviz2-dbgsym]
    jammy: [ros-humble-rviz2-dbgsym]
rviz_2d_overlay_msgs:
  ubuntu:
    focal: [ros-humble-rviz-2d-overlay-msgs]
    jammy: [ros-humble-rviz-2d-overlay-msgs]
rviz_2d_overlay_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-rviz-2d-overlay-msgs-dbgsym]
    jammy: [ros-humble-rviz-2d-overlay-msgs-dbgsym]
rviz_2d_overlay_plugins:
  ubuntu:
    focal: [ros-humble-rviz-2d-overlay-plugins]
    jammy: [ros-humble-rviz-2d-overlay-plugins]
rviz_2d_overlay_plugins_dbgsym:
  ubuntu:
    focal: [ros-humble-rviz-2d-overlay-plugins-dbgsym]
    jammy: [ros-humble-rviz-2d-overlay-plugins-dbgsym]
rviz_assimp_vendor:
  ubuntu:
    focal: [ros-humble-rviz-assimp-vendor]
    jammy: [ros-humble-rviz-assimp-vendor]
rviz_common:
  ubuntu:
    focal: [ros-humble-rviz-common]
    jammy: [ros-humble-rviz-common]
rviz_common_dbgsym:
  ubuntu:
    focal: [ros-humble-rviz-common-dbgsym]
    jammy: [ros-humble-rviz-common-dbgsym]
rviz_default_plugins:
  ubuntu:
    focal: [ros-humble-rviz-default-plugins]
    jammy: [ros-humble-rviz-default-plugins]
rviz_default_plugins_dbgsym:
  ubuntu:
    focal: [ros-humble-rviz-default-plugins-dbgsym]
    jammy: [ros-humble-rviz-default-plugins-dbgsym]
rviz_imu_plugin:
  ubuntu:
    focal: [ros-humble-rviz-imu-plugin]
    jammy: [ros-humble-rviz-imu-plugin]
rviz_imu_plugin_dbgsym:
  ubuntu:
    focal: [ros-humble-rviz-imu-plugin-dbgsym]
    jammy: [ros-humble-rviz-imu-plugin-dbgsym]
rviz_marker_tools:
  ubuntu:
    focal: [ros-humble-rviz-marker-tools]
    jammy: [ros-humble-rviz-marker-tools]
rviz_ogre_vendor:
  ubuntu:
    focal: [ros-humble-rviz-ogre-vendor]
    jammy: [ros-humble-rviz-ogre-vendor]
rviz_ogre_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-rviz-ogre-vendor-dbgsym]
    jammy: [ros-humble-rviz-ogre-vendor-dbgsym]
rviz_rendering:
  ubuntu:
    focal: [ros-humble-rviz-rendering]
    jammy: [ros-humble-rviz-rendering]
rviz_rendering_dbgsym:
  ubuntu:
    focal: [ros-humble-rviz-rendering-dbgsym]
    jammy: [ros-humble-rviz-rendering-dbgsym]
rviz_rendering_tests:
  ubuntu:
    focal: [ros-humble-rviz-rendering-tests]
    jammy: [ros-humble-rviz-rendering-tests]
rviz_satellite:
  ubuntu:
    focal: [ros-humble-rviz-satellite]
    jammy: [ros-humble-rviz-satellite]
rviz_satellite_dbgsym:
  ubuntu:
    focal: [ros-humble-rviz-satellite-dbgsym]
    jammy: [ros-humble-rviz-satellite-dbgsym]
rviz_visual_testing_framework:
  ubuntu:
    focal: [ros-humble-rviz-visual-testing-framework]
    jammy: [ros-humble-rviz-visual-testing-framework]
rviz_visual_tools:
  ubuntu:
    focal: [ros-humble-rviz-visual-tools]
    jammy: [ros-humble-rviz-visual-tools]
rviz_visual_tools_dbgsym:
  ubuntu:
    focal: [ros-humble-rviz-visual-tools-dbgsym]
    jammy: [ros-humble-rviz-visual-tools-dbgsym]
schunk_svh_description:
  ubuntu:
    focal: [ros-humble-schunk-svh-description]
    jammy: [ros-humble-schunk-svh-description]
schunk_svh_driver:
  ubuntu:
    focal: [ros-humble-schunk-svh-driver]
    jammy: [ros-humble-schunk-svh-driver]
schunk_svh_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-schunk-svh-driver-dbgsym]
    jammy: [ros-humble-schunk-svh-driver-dbgsym]
schunk_svh_library:
  ubuntu:
    focal: [ros-humble-schunk-svh-library]
    jammy: [ros-humble-schunk-svh-library]
schunk_svh_library_dbgsym:
  ubuntu:
    focal: [ros-humble-schunk-svh-library-dbgsym]
    jammy: [ros-humble-schunk-svh-library-dbgsym]
schunk_svh_tests:
  ubuntu:
    focal: [ros-humble-schunk-svh-tests]
    jammy: [ros-humble-schunk-svh-tests]
sdformat_test_files:
  ubuntu:
    focal: [ros-humble-sdformat-test-files]
    jammy: [ros-humble-sdformat-test-files]
sdformat_urdf:
  ubuntu:
    focal: [ros-humble-sdformat-urdf]
    jammy: [ros-humble-sdformat-urdf]
sdformat_urdf_dbgsym:
  ubuntu:
    focal: [ros-humble-sdformat-urdf-dbgsym]
    jammy: [ros-humble-sdformat-urdf-dbgsym]
sdl2_vendor:
  ubuntu:
    focal: [ros-humble-sdl2-vendor]
    jammy: [ros-humble-sdl2-vendor]
self_test:
  ubuntu:
    focal: [ros-humble-self-test]
    jammy: [ros-humble-self-test]
self_test_dbgsym:
  ubuntu:
    focal: [ros-humble-self-test-dbgsym]
    jammy: [ros-humble-self-test-dbgsym]
semantic_label_conversion:
  ubuntu:
    focal: [ros-humble-semantic-label-conversion]
    jammy: [ros-humble-semantic-label-conversion]
sensor_msgs:
  ubuntu:
    focal: [ros-humble-sensor-msgs]
    jammy: [ros-humble-sensor-msgs]
sensor_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-sensor-msgs-dbgsym]
    jammy: [ros-humble-sensor-msgs-dbgsym]
sensor_msgs_py:
  ubuntu:
    focal: [ros-humble-sensor-msgs-py]
    jammy: [ros-humble-sensor-msgs-py]
septentrio_gnss_driver:
  ubuntu:
    focal: [ros-humble-septentrio-gnss-driver]
    jammy: [ros-humble-septentrio-gnss-driver]
septentrio_gnss_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-septentrio-gnss-driver-dbgsym]
    jammy: [ros-humble-septentrio-gnss-driver-dbgsym]
serial_driver:
  ubuntu:
    focal: [ros-humble-serial-driver]
    jammy: [ros-humble-serial-driver]
serial_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-serial-driver-dbgsym]
    jammy: [ros-humble-serial-driver-dbgsym]
shape_msgs:
  ubuntu:
    focal: [ros-humble-shape-msgs]
    jammy: [ros-humble-shape-msgs]
shape_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-shape-msgs-dbgsym]
    jammy: [ros-humble-shape-msgs-dbgsym]
shared_queues_vendor:
  ubuntu:
    focal: [ros-humble-shared-queues-vendor]
    jammy: [ros-humble-shared-queues-vendor]
sick_safetyscanners2:
  ubuntu:
    focal: [ros-humble-sick-safetyscanners2]
    jammy: [ros-humble-sick-safetyscanners2]
sick_safetyscanners2_dbgsym:
  ubuntu:
    focal: [ros-humble-sick-safetyscanners2-dbgsym]
    jammy: [ros-humble-sick-safetyscanners2-dbgsym]
sick_safetyscanners2_interfaces:
  ubuntu:
    focal: [ros-humble-sick-safetyscanners2-interfaces]
    jammy: [ros-humble-sick-safetyscanners2-interfaces]
sick_safetyscanners2_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-sick-safetyscanners2-interfaces-dbgsym]
    jammy: [ros-humble-sick-safetyscanners2-interfaces-dbgsym]
sick_safetyscanners_base:
  ubuntu:
    focal: [ros-humble-sick-safetyscanners-base]
    jammy: [ros-humble-sick-safetyscanners-base]
sick_safetyscanners_base_dbgsym:
  ubuntu:
    focal: [ros-humble-sick-safetyscanners-base-dbgsym]
    jammy: [ros-humble-sick-safetyscanners-base-dbgsym]
simple_actions:
  ubuntu:
    focal: [ros-humble-simple-actions]
    jammy: [ros-humble-simple-actions]
simple_actions_dbgsym:
  ubuntu:
    focal: [ros-humble-simple-actions-dbgsym]
    jammy: [ros-humble-simple-actions-dbgsym]
simple_launch:
  ubuntu:
    focal: [ros-humble-simple-launch]
    jammy: [ros-humble-simple-launch]
simple_term_menu_vendor:
  ubuntu:
    focal: [ros-humble-simple-term-menu-vendor]
    jammy: [ros-humble-simple-term-menu-vendor]
simulation:
  ubuntu:
    focal: [ros-humble-simulation]
    jammy: [ros-humble-simulation]
slam_toolbox:
  ubuntu:
    focal: [ros-humble-slam-toolbox]
    jammy: [ros-humble-slam-toolbox]
slam_toolbox_dbgsym:
  ubuntu:
    focal: [ros-humble-slam-toolbox-dbgsym]
    jammy: [ros-humble-slam-toolbox-dbgsym]
slider_publisher:
  ubuntu:
    focal: [ros-humble-slider-publisher]
    jammy: [ros-humble-slider-publisher]
sllidar_ros2:
  ubuntu:
    focal: [ros-humble-sllidar-ros2]
    jammy: [ros-humble-sllidar-ros2]
smacc2:
  ubuntu:
    focal: [ros-humble-smacc2]
    jammy: [ros-humble-smacc2]
smacc2_dbgsym:
  ubuntu:
    focal: [ros-humble-smacc2-dbgsym]
    jammy: [ros-humble-smacc2-dbgsym]
smacc2_msgs:
  ubuntu:
    focal: [ros-humble-smacc2-msgs]
    jammy: [ros-humble-smacc2-msgs]
smacc2_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-smacc2-msgs-dbgsym]
    jammy: [ros-humble-smacc2-msgs-dbgsym]
smach:
  ubuntu:
    focal: [ros-humble-smach]
    jammy: [ros-humble-smach]
smach_msgs:
  ubuntu:
    focal: [ros-humble-smach-msgs]
    jammy: [ros-humble-smach-msgs]
smach_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-smach-msgs-dbgsym]
    jammy: [ros-humble-smach-msgs-dbgsym]
smach_ros:
  ubuntu:
    focal: [ros-humble-smach-ros]
    jammy: [ros-humble-smach-ros]
smclib:
  ubuntu:
    focal: [ros-humble-smclib]
    jammy: [ros-humble-smclib]
snowbot_operating_system:
  ubuntu:
    focal: [ros-humble-snowbot-operating-system]
    jammy: [ros-humble-snowbot-operating-system]
snowbot_operating_system_dbgsym:
  ubuntu:
    focal: [ros-humble-snowbot-operating-system-dbgsym]
    jammy: [ros-humble-snowbot-operating-system-dbgsym]
soccer_interfaces:
  ubuntu:
    focal: [ros-humble-soccer-interfaces]
    jammy: [ros-humble-soccer-interfaces]
soccer_marker_generation:
  ubuntu:
    focal: [ros-humble-soccer-marker-generation]
    jammy: [ros-humble-soccer-marker-generation]
soccer_marker_generation_dbgsym:
  ubuntu:
    focal: [ros-humble-soccer-marker-generation-dbgsym]
    jammy: [ros-humble-soccer-marker-generation-dbgsym]
soccer_object_msgs:
  ubuntu:
    focal: [ros-humble-soccer-object-msgs]
    jammy: [ros-humble-soccer-object-msgs]
soccer_object_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-soccer-object-msgs-dbgsym]
    jammy: [ros-humble-soccer-object-msgs-dbgsym]
soccer_vision_2d_msgs:
  ubuntu:
    focal: [ros-humble-soccer-vision-2d-msgs]
    jammy: [ros-humble-soccer-vision-2d-msgs]
soccer_vision_2d_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-soccer-vision-2d-msgs-dbgsym]
    jammy: [ros-humble-soccer-vision-2d-msgs-dbgsym]
soccer_vision_3d_msgs:
  ubuntu:
    focal: [ros-humble-soccer-vision-3d-msgs]
    jammy: [ros-humble-soccer-vision-3d-msgs]
soccer_vision_3d_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-soccer-vision-3d-msgs-dbgsym]
    jammy: [ros-humble-soccer-vision-3d-msgs-dbgsym]
soccer_vision_attribute_msgs:
  ubuntu:
    focal: [ros-humble-soccer-vision-attribute-msgs]
    jammy: [ros-humble-soccer-vision-attribute-msgs]
soccer_vision_attribute_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-soccer-vision-attribute-msgs-dbgsym]
    jammy: [ros-humble-soccer-vision-attribute-msgs-dbgsym]
social_nav_msgs:
  ubuntu:
    focal: [ros-humble-social-nav-msgs]
    jammy: [ros-humble-social-nav-msgs]
social_nav_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-social-nav-msgs-dbgsym]
    jammy: [ros-humble-social-nav-msgs-dbgsym]
social_nav_util:
  ubuntu:
    focal: [ros-humble-social-nav-util]
    jammy: [ros-humble-social-nav-util]
sol_vendor:
  ubuntu:
    focal: [ros-humble-sol-vendor]
    jammy: [ros-humble-sol-vendor]
sophus:
  ubuntu:
    focal: [ros-humble-sophus]
    jammy: [ros-humble-sophus]
spacenav:
  ubuntu:
    focal: [ros-humble-spacenav]
    jammy: [ros-humble-spacenav]
spacenav_dbgsym:
  ubuntu:
    focal: [ros-humble-spacenav-dbgsym]
    jammy: [ros-humble-spacenav-dbgsym]
spdlog_vendor:
  ubuntu:
    focal: [ros-humble-spdlog-vendor]
    jammy: [ros-humble-spdlog-vendor]
splsm_7:
  ubuntu:
    focal: [ros-humble-splsm-7]
    jammy: [ros-humble-splsm-7]
splsm_7_conversion:
  ubuntu:
    focal: [ros-humble-splsm-7-conversion]
    jammy: [ros-humble-splsm-7-conversion]
splsm_7_dbgsym:
  ubuntu:
    focal: [ros-humble-splsm-7-dbgsym]
    jammy: [ros-humble-splsm-7-dbgsym]
sqlite3_vendor:
  ubuntu:
    focal: [ros-humble-sqlite3-vendor]
    jammy: [ros-humble-sqlite3-vendor]
srdfdom:
  ubuntu:
    focal: [ros-humble-srdfdom]
    jammy: [ros-humble-srdfdom]
srdfdom_dbgsym:
  ubuntu:
    focal: [ros-humble-srdfdom-dbgsym]
    jammy: [ros-humble-srdfdom-dbgsym]
sros2:
  ubuntu:
    focal: [ros-humble-sros2]
    jammy: [ros-humble-sros2]
sros2_cmake:
  ubuntu:
    focal: [ros-humble-sros2-cmake]
    jammy: [ros-humble-sros2-cmake]
statistics_msgs:
  ubuntu:
    focal: [ros-humble-statistics-msgs]
    jammy: [ros-humble-statistics-msgs]
statistics_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-statistics-msgs-dbgsym]
    jammy: [ros-humble-statistics-msgs-dbgsym]
std_msgs:
  ubuntu:
    focal: [ros-humble-std-msgs]
    jammy: [ros-humble-std-msgs]
std_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-std-msgs-dbgsym]
    jammy: [ros-humble-std-msgs-dbgsym]
std_srvs:
  ubuntu:
    focal: [ros-humble-std-srvs]
    jammy: [ros-humble-std-srvs]
std_srvs_dbgsym:
  ubuntu:
    focal: [ros-humble-std-srvs-dbgsym]
    jammy: [ros-humble-std-srvs-dbgsym]
steering_controllers_library:
  ubuntu:
    focal: [ros-humble-steering-controllers-library]
    jammy: [ros-humble-steering-controllers-library]
steering_controllers_library_dbgsym:
  ubuntu:
    focal: [ros-humble-steering-controllers-library-dbgsym]
    jammy: [ros-humble-steering-controllers-library-dbgsym]
stereo_image_proc:
  ubuntu:
    focal: [ros-humble-stereo-image-proc]
    jammy: [ros-humble-stereo-image-proc]
stereo_image_proc_dbgsym:
  ubuntu:
    focal: [ros-humble-stereo-image-proc-dbgsym]
    jammy: [ros-humble-stereo-image-proc-dbgsym]
stereo_msgs:
  ubuntu:
    focal: [ros-humble-stereo-msgs]
    jammy: [ros-humble-stereo-msgs]
stereo_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-stereo-msgs-dbgsym]
    jammy: [ros-humble-stereo-msgs-dbgsym]
stomp:
  ubuntu:
    focal: [ros-humble-stomp]
    jammy: [ros-humble-stomp]
stomp_dbgsym:
  ubuntu:
    focal: [ros-humble-stomp-dbgsym]
    jammy: [ros-humble-stomp-dbgsym]
stubborn_buddies:
  ubuntu:
    focal: [ros-humble-stubborn-buddies]
    jammy: [ros-humble-stubborn-buddies]
stubborn_buddies_dbgsym:
  ubuntu:
    focal: [ros-humble-stubborn-buddies-dbgsym]
    jammy: [ros-humble-stubborn-buddies-dbgsym]
stubborn_buddies_msgs:
  ubuntu:
    focal: [ros-humble-stubborn-buddies-msgs]
    jammy: [ros-humble-stubborn-buddies-msgs]
stubborn_buddies_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-stubborn-buddies-msgs-dbgsym]
    jammy: [ros-humble-stubborn-buddies-msgs-dbgsym]
swri_cli_tools:
  ubuntu:
    focal: [ros-humble-swri-cli-tools]
    jammy: [ros-humble-swri-cli-tools]
swri_console:
  ubuntu:
    focal: [ros-humble-swri-console]
    jammy: [ros-humble-swri-console]
swri_console_dbgsym:
  ubuntu:
    focal: [ros-humble-swri-console-dbgsym]
    jammy: [ros-humble-swri-console-dbgsym]
swri_dbw_interface:
  ubuntu:
    focal: [ros-humble-swri-dbw-interface]
    jammy: [ros-humble-swri-dbw-interface]
swri_math_util:
  ubuntu:
    focal: [ros-humble-swri-math-util]
    jammy: [ros-humble-swri-math-util]
swri_math_util_dbgsym:
  ubuntu:
    focal: [ros-humble-swri-math-util-dbgsym]
    jammy: [ros-humble-swri-math-util-dbgsym]
swri_opencv_util:
  ubuntu:
    focal: [ros-humble-swri-opencv-util]
    jammy: [ros-humble-swri-opencv-util]
swri_opencv_util_dbgsym:
  ubuntu:
    focal: [ros-humble-swri-opencv-util-dbgsym]
    jammy: [ros-humble-swri-opencv-util-dbgsym]
swri_prefix_tools:
  ubuntu:
    focal: [ros-humble-swri-prefix-tools]
    jammy: [ros-humble-swri-prefix-tools]
swri_serial_util:
  ubuntu:
    focal: [ros-humble-swri-serial-util]
    jammy: [ros-humble-swri-serial-util]
swri_serial_util_dbgsym:
  ubuntu:
    focal: [ros-humble-swri-serial-util-dbgsym]
    jammy: [ros-humble-swri-serial-util-dbgsym]
swri_system_util:
  ubuntu:
    focal: [ros-humble-swri-system-util]
    jammy: [ros-humble-swri-system-util]
swri_system_util_dbgsym:
  ubuntu:
    focal: [ros-humble-swri-system-util-dbgsym]
    jammy: [ros-humble-swri-system-util-dbgsym]
system_fingerprint:
  ubuntu:
    focal: [ros-humble-system-fingerprint]
    jammy: [ros-humble-system-fingerprint]
system_modes:
  ubuntu:
    focal: [ros-humble-system-modes]
    jammy: [ros-humble-system-modes]
system_modes_dbgsym:
  ubuntu:
    focal: [ros-humble-system-modes-dbgsym]
    jammy: [ros-humble-system-modes-dbgsym]
system_modes_examples:
  ubuntu:
    focal: [ros-humble-system-modes-examples]
    jammy: [ros-humble-system-modes-examples]
system_modes_examples_dbgsym:
  ubuntu:
    focal: [ros-humble-system-modes-examples-dbgsym]
    jammy: [ros-humble-system-modes-examples-dbgsym]
system_modes_msgs:
  ubuntu:
    focal: [ros-humble-system-modes-msgs]
    jammy: [ros-humble-system-modes-msgs]
system_modes_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-system-modes-msgs-dbgsym]
    jammy: [ros-humble-system-modes-msgs-dbgsym]
tango_icons_vendor:
  ubuntu:
    focal: [ros-humble-tango-icons-vendor]
    jammy: [ros-humble-tango-icons-vendor]
tcb_span:
  ubuntu:
    focal: [ros-humble-tcb-span]
    jammy: [ros-humble-tcb-span]
teleop_tools:
  ubuntu:
    focal: [ros-humble-teleop-tools]
    jammy: [ros-humble-teleop-tools]
teleop_tools_msgs:
  ubuntu:
    focal: [ros-humble-teleop-tools-msgs]
    jammy: [ros-humble-teleop-tools-msgs]
teleop_tools_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-teleop-tools-msgs-dbgsym]
    jammy: [ros-humble-teleop-tools-msgs-dbgsym]
teleop_twist_joy:
  ubuntu:
    focal: [ros-humble-teleop-twist-joy]
    jammy: [ros-humble-teleop-twist-joy]
teleop_twist_joy_dbgsym:
  ubuntu:
    focal: [ros-humble-teleop-twist-joy-dbgsym]
    jammy: [ros-humble-teleop-twist-joy-dbgsym]
teleop_twist_keyboard:
  ubuntu:
    focal: [ros-humble-teleop-twist-keyboard]
    jammy: [ros-humble-teleop-twist-keyboard]
tensorrt_cmake_module:
  ubuntu:
    focal: [ros-humble-tensorrt-cmake-module]
    jammy: [ros-humble-tensorrt-cmake-module]
test_apex_test_tools:
  ubuntu:
    focal: [ros-humble-test-apex-test-tools]
    jammy: [ros-humble-test-apex-test-tools]
test_interface_files:
  ubuntu:
    focal: [ros-humble-test-interface-files]
    jammy: [ros-humble-test-interface-files]
test_msgs:
  ubuntu:
    focal: [ros-humble-test-msgs]
    jammy: [ros-humble-test-msgs]
test_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-test-msgs-dbgsym]
    jammy: [ros-humble-test-msgs-dbgsym]
tf2:
  ubuntu:
    focal: [ros-humble-tf2]
    jammy: [ros-humble-tf2]
tf2_bullet:
  ubuntu:
    focal: [ros-humble-tf2-bullet]
    jammy: [ros-humble-tf2-bullet]
tf2_dbgsym:
  ubuntu:
    focal: [ros-humble-tf2-dbgsym]
    jammy: [ros-humble-tf2-dbgsym]
tf2_eigen:
  ubuntu:
    focal: [ros-humble-tf2-eigen]
    jammy: [ros-humble-tf2-eigen]
tf2_eigen_kdl:
  ubuntu:
    focal: [ros-humble-tf2-eigen-kdl]
    jammy: [ros-humble-tf2-eigen-kdl]
tf2_eigen_kdl_dbgsym:
  ubuntu:
    focal: [ros-humble-tf2-eigen-kdl-dbgsym]
    jammy: [ros-humble-tf2-eigen-kdl-dbgsym]
tf2_geometry_msgs:
  ubuntu:
    focal: [ros-humble-tf2-geometry-msgs]
    jammy: [ros-humble-tf2-geometry-msgs]
tf2_kdl:
  ubuntu:
    focal: [ros-humble-tf2-kdl]
    jammy: [ros-humble-tf2-kdl]
tf2_msgs:
  ubuntu:
    focal: [ros-humble-tf2-msgs]
    jammy: [ros-humble-tf2-msgs]
tf2_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-tf2-msgs-dbgsym]
    jammy: [ros-humble-tf2-msgs-dbgsym]
tf2_py:
  ubuntu:
    focal: [ros-humble-tf2-py]
    jammy: [ros-humble-tf2-py]
tf2_py_dbgsym:
  ubuntu:
    focal: [ros-humble-tf2-py-dbgsym]
    jammy: [ros-humble-tf2-py-dbgsym]
tf2_ros:
  ubuntu:
    focal: [ros-humble-tf2-ros]
    jammy: [ros-humble-tf2-ros]
tf2_ros_dbgsym:
  ubuntu:
    focal: [ros-humble-tf2-ros-dbgsym]
    jammy: [ros-humble-tf2-ros-dbgsym]
tf2_ros_py:
  ubuntu:
    focal: [ros-humble-tf2-ros-py]
    jammy: [ros-humble-tf2-ros-py]
tf2_sensor_msgs:
  ubuntu:
    focal: [ros-humble-tf2-sensor-msgs]
    jammy: [ros-humble-tf2-sensor-msgs]
tf2_tools:
  ubuntu:
    focal: [ros-humble-tf2-tools]
    jammy: [ros-humble-tf2-tools]
tf_transformations:
  ubuntu:
    focal: [ros-humble-tf-transformations]
    jammy: [ros-humble-tf-transformations]
theora_image_transport:
  ubuntu:
    focal: [ros-humble-theora-image-transport]
    jammy: [ros-humble-theora-image-transport]
theora_image_transport_dbgsym:
  ubuntu:
    focal: [ros-humble-theora-image-transport-dbgsym]
    jammy: [ros-humble-theora-image-transport-dbgsym]
tiago_2dnav:
  ubuntu:
    focal: [ros-humble-tiago-2dnav]
    jammy: [ros-humble-tiago-2dnav]
tiago_bringup:
  ubuntu:
    focal: [ros-humble-tiago-bringup]
    jammy: [ros-humble-tiago-bringup]
tiago_controller_configuration:
  ubuntu:
    focal: [ros-humble-tiago-controller-configuration]
    jammy: [ros-humble-tiago-controller-configuration]
tiago_description:
  ubuntu:
    focal: [ros-humble-tiago-description]
    jammy: [ros-humble-tiago-description]
tiago_laser_sensors:
  ubuntu:
    focal: [ros-humble-tiago-laser-sensors]
    jammy: [ros-humble-tiago-laser-sensors]
tiago_moveit_config:
  ubuntu:
    focal: [ros-humble-tiago-moveit-config]
    jammy: [ros-humble-tiago-moveit-config]
tiago_navigation:
  ubuntu:
    focal: [ros-humble-tiago-navigation]
    jammy: [ros-humble-tiago-navigation]
tiago_robot:
  ubuntu:
    focal: [ros-humble-tiago-robot]
    jammy: [ros-humble-tiago-robot]
tinyspline_vendor:
  ubuntu:
    focal: [ros-humble-tinyspline-vendor]
    jammy: [ros-humble-tinyspline-vendor]
tinyspline_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-tinyspline-vendor-dbgsym]
    jammy: [ros-humble-tinyspline-vendor-dbgsym]
tinyxml2_vendor:
  ubuntu:
    focal: [ros-humble-tinyxml2-vendor]
    jammy: [ros-humble-tinyxml2-vendor]
tinyxml_vendor:
  ubuntu:
    focal: [ros-humble-tinyxml-vendor]
    jammy: [ros-humble-tinyxml-vendor]
tl_expected:
  ubuntu:
    focal: [ros-humble-tl-expected]
    jammy: [ros-humble-tl-expected]
tlsf:
  ubuntu:
    focal: [ros-humble-tlsf]
    jammy: [ros-humble-tlsf]
tlsf_cpp:
  ubuntu:
    focal: [ros-humble-tlsf-cpp]
    jammy: [ros-humble-tlsf-cpp]
tlsf_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-tlsf-cpp-dbgsym]
    jammy: [ros-humble-tlsf-cpp-dbgsym]
topic_based_ros2_control:
  ubuntu:
    focal: [ros-humble-topic-based-ros2-control]
    jammy: [ros-humble-topic-based-ros2-control]
topic_based_ros2_control_dbgsym:
  ubuntu:
    focal: [ros-humble-topic-based-ros2-control-dbgsym]
    jammy: [ros-humble-topic-based-ros2-control-dbgsym]
topic_monitor:
  ubuntu:
    focal: [ros-humble-topic-monitor]
    jammy: [ros-humble-topic-monitor]
topic_statistics_demo:
  ubuntu:
    focal: [ros-humble-topic-statistics-demo]
    jammy: [ros-humble-topic-statistics-demo]
topic_statistics_demo_dbgsym:
  ubuntu:
    focal: [ros-humble-topic-statistics-demo-dbgsym]
    jammy: [ros-humble-topic-statistics-demo-dbgsym]
topic_tools:
  ubuntu:
    focal: [ros-humble-topic-tools]
    jammy: [ros-humble-topic-tools]
topic_tools_dbgsym:
  ubuntu:
    focal: [ros-humble-topic-tools-dbgsym]
    jammy: [ros-humble-topic-tools-dbgsym]
topic_tools_interfaces:
  ubuntu:
    focal: [ros-humble-topic-tools-interfaces]
    jammy: [ros-humble-topic-tools-interfaces]
topic_tools_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-topic-tools-interfaces-dbgsym]
    jammy: [ros-humble-topic-tools-interfaces-dbgsym]
tracetools:
  ubuntu:
    focal: [ros-humble-tracetools]
    jammy: [ros-humble-tracetools]
tracetools_acceleration:
  ubuntu:
    focal: [ros-humble-tracetools-acceleration]
    jammy: [ros-humble-tracetools-acceleration]
tracetools_acceleration_dbgsym:
  ubuntu:
    focal: [ros-humble-tracetools-acceleration-dbgsym]
    jammy: [ros-humble-tracetools-acceleration-dbgsym]
tracetools_analysis:
  ubuntu:
    focal: [ros-humble-tracetools-analysis]
    jammy: [ros-humble-tracetools-analysis]
tracetools_dbgsym:
  ubuntu:
    focal: [ros-humble-tracetools-dbgsym]
    jammy: [ros-humble-tracetools-dbgsym]
tracetools_image_pipeline:
  ubuntu:
    focal: [ros-humble-tracetools-image-pipeline]
    jammy: [ros-humble-tracetools-image-pipeline]
tracetools_image_pipeline_dbgsym:
  ubuntu:
    focal: [ros-humble-tracetools-image-pipeline-dbgsym]
    jammy: [ros-humble-tracetools-image-pipeline-dbgsym]
tracetools_launch:
  ubuntu:
    focal: [ros-humble-tracetools-launch]
    jammy: [ros-humble-tracetools-launch]
tracetools_read:
  ubuntu:
    focal: [ros-humble-tracetools-read]
    jammy: [ros-humble-tracetools-read]
tracetools_test:
  ubuntu:
    focal: [ros-humble-tracetools-test]
    jammy: [ros-humble-tracetools-test]
tracetools_trace:
  ubuntu:
    focal: [ros-humble-tracetools-trace]
    jammy: [ros-humble-tracetools-trace]
trajectory_msgs:
  ubuntu:
    focal: [ros-humble-trajectory-msgs]
    jammy: [ros-humble-trajectory-msgs]
trajectory_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-trajectory-msgs-dbgsym]
    jammy: [ros-humble-trajectory-msgs-dbgsym]
transmission_interface:
  ubuntu:
    focal: [ros-humble-transmission-interface]
    jammy: [ros-humble-transmission-interface]
transmission_interface_dbgsym:
  ubuntu:
    focal: [ros-humble-transmission-interface-dbgsym]
    jammy: [ros-humble-transmission-interface-dbgsym]
tricycle_controller:
  ubuntu:
    focal: [ros-humble-tricycle-controller]
    jammy: [ros-humble-tricycle-controller]
tricycle_controller_dbgsym:
  ubuntu:
    focal: [ros-humble-tricycle-controller-dbgsym]
    jammy: [ros-humble-tricycle-controller-dbgsym]
tricycle_steering_controller:
  ubuntu:
    focal: [ros-humble-tricycle-steering-controller]
    jammy: [ros-humble-tricycle-steering-controller]
tricycle_steering_controller_dbgsym:
  ubuntu:
    focal: [ros-humble-tricycle-steering-controller-dbgsym]
    jammy: [ros-humble-tricycle-steering-controller-dbgsym]
turbojpeg_compressed_image_transport:
  ubuntu:
    focal: [ros-humble-turbojpeg-compressed-image-transport]
    jammy: [ros-humble-turbojpeg-compressed-image-transport]
turbojpeg_compressed_image_transport_dbgsym:
  ubuntu:
    focal: [ros-humble-turbojpeg-compressed-image-transport-dbgsym]
    jammy: [ros-humble-turbojpeg-compressed-image-transport-dbgsym]
turtle_tf2_cpp:
  ubuntu:
    focal: [ros-humble-turtle-tf2-cpp]
    jammy: [ros-humble-turtle-tf2-cpp]
turtle_tf2_cpp_dbgsym:
  ubuntu:
    focal: [ros-humble-turtle-tf2-cpp-dbgsym]
    jammy: [ros-humble-turtle-tf2-cpp-dbgsym]
turtle_tf2_py:
  ubuntu:
    focal: [ros-humble-turtle-tf2-py]
    jammy: [ros-humble-turtle-tf2-py]
turtlebot3:
  ubuntu:
    focal: [ros-humble-turtlebot3]
    jammy: [ros-humble-turtlebot3]
turtlebot3_bringup:
  ubuntu:
    focal: [ros-humble-turtlebot3-bringup]
    jammy: [ros-humble-turtlebot3-bringup]
turtlebot3_cartographer:
  ubuntu:
    focal: [ros-humble-turtlebot3-cartographer]
    jammy: [ros-humble-turtlebot3-cartographer]
turtlebot3_description:
  ubuntu:
    focal: [ros-humble-turtlebot3-description]
    jammy: [ros-humble-turtlebot3-description]
turtlebot3_example:
  ubuntu:
    focal: [ros-humble-turtlebot3-example]
    jammy: [ros-humble-turtlebot3-example]
turtlebot3_fake_node:
  ubuntu:
    focal: [ros-humble-turtlebot3-fake-node]
    jammy: [ros-humble-turtlebot3-fake-node]
turtlebot3_fake_node_dbgsym:
  ubuntu:
    focal: [ros-humble-turtlebot3-fake-node-dbgsym]
    jammy: [ros-humble-turtlebot3-fake-node-dbgsym]
turtlebot3_manipulation_cartographer:
  ubuntu:
    focal: [ros-humble-turtlebot3-manipulation-cartographer]
    jammy: [ros-humble-turtlebot3-manipulation-cartographer]
turtlebot3_manipulation_description:
  ubuntu:
    focal: [ros-humble-turtlebot3-manipulation-description]
    jammy: [ros-humble-turtlebot3-manipulation-description]
turtlebot3_manipulation_hardware:
  ubuntu:
    focal: [ros-humble-turtlebot3-manipulation-hardware]
    jammy: [ros-humble-turtlebot3-manipulation-hardware]
turtlebot3_manipulation_hardware_dbgsym:
  ubuntu:
    focal: [ros-humble-turtlebot3-manipulation-hardware-dbgsym]
    jammy: [ros-humble-turtlebot3-manipulation-hardware-dbgsym]
turtlebot3_manipulation_moveit_config:
  ubuntu:
    focal: [ros-humble-turtlebot3-manipulation-moveit-config]
    jammy: [ros-humble-turtlebot3-manipulation-moveit-config]
turtlebot3_manipulation_navigation2:
  ubuntu:
    focal: [ros-humble-turtlebot3-manipulation-navigation2]
    jammy: [ros-humble-turtlebot3-manipulation-navigation2]
turtlebot3_msgs:
  ubuntu:
    focal: [ros-humble-turtlebot3-msgs]
    jammy: [ros-humble-turtlebot3-msgs]
turtlebot3_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-turtlebot3-msgs-dbgsym]
    jammy: [ros-humble-turtlebot3-msgs-dbgsym]
turtlebot3_navigation2:
  ubuntu:
    focal: [ros-humble-turtlebot3-navigation2]
    jammy: [ros-humble-turtlebot3-navigation2]
turtlebot3_node:
  ubuntu:
    focal: [ros-humble-turtlebot3-node]
    jammy: [ros-humble-turtlebot3-node]
turtlebot3_node_dbgsym:
  ubuntu:
    focal: [ros-humble-turtlebot3-node-dbgsym]
    jammy: [ros-humble-turtlebot3-node-dbgsym]
turtlebot3_teleop:
  ubuntu:
    focal: [ros-humble-turtlebot3-teleop]
    jammy: [ros-humble-turtlebot3-teleop]
turtlebot4_base:
  ubuntu:
    focal: [ros-humble-turtlebot4-base]
    jammy: [ros-humble-turtlebot4-base]
turtlebot4_base_dbgsym:
  ubuntu:
    focal: [ros-humble-turtlebot4-base-dbgsym]
    jammy: [ros-humble-turtlebot4-base-dbgsym]
turtlebot4_bringup:
  ubuntu:
    focal: [ros-humble-turtlebot4-bringup]
    jammy: [ros-humble-turtlebot4-bringup]
turtlebot4_cpp_tutorials:
  ubuntu:
    focal: [ros-humble-turtlebot4-cpp-tutorials]
    jammy: [ros-humble-turtlebot4-cpp-tutorials]
turtlebot4_cpp_tutorials_dbgsym:
  ubuntu:
    focal: [ros-humble-turtlebot4-cpp-tutorials-dbgsym]
    jammy: [ros-humble-turtlebot4-cpp-tutorials-dbgsym]
turtlebot4_description:
  ubuntu:
    focal: [ros-humble-turtlebot4-description]
    jammy: [ros-humble-turtlebot4-description]
turtlebot4_desktop:
  ubuntu:
    focal: [ros-humble-turtlebot4-desktop]
    jammy: [ros-humble-turtlebot4-desktop]
turtlebot4_diagnostics:
  ubuntu:
    focal: [ros-humble-turtlebot4-diagnostics]
    jammy: [ros-humble-turtlebot4-diagnostics]
turtlebot4_ignition_bringup:
  ubuntu:
    focal: [ros-humble-turtlebot4-ignition-bringup]
    jammy: [ros-humble-turtlebot4-ignition-bringup]
turtlebot4_ignition_gui_plugins:
  ubuntu:
    focal: [ros-humble-turtlebot4-ignition-gui-plugins]
    jammy: [ros-humble-turtlebot4-ignition-gui-plugins]
turtlebot4_ignition_gui_plugins_dbgsym:
  ubuntu:
    focal: [ros-humble-turtlebot4-ignition-gui-plugins-dbgsym]
    jammy: [ros-humble-turtlebot4-ignition-gui-plugins-dbgsym]
turtlebot4_ignition_toolbox:
  ubuntu:
    focal: [ros-humble-turtlebot4-ignition-toolbox]
    jammy: [ros-humble-turtlebot4-ignition-toolbox]
turtlebot4_ignition_toolbox_dbgsym:
  ubuntu:
    focal: [ros-humble-turtlebot4-ignition-toolbox-dbgsym]
    jammy: [ros-humble-turtlebot4-ignition-toolbox-dbgsym]
turtlebot4_msgs:
  ubuntu:
    focal: [ros-humble-turtlebot4-msgs]
    jammy: [ros-humble-turtlebot4-msgs]
turtlebot4_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-turtlebot4-msgs-dbgsym]
    jammy: [ros-humble-turtlebot4-msgs-dbgsym]
turtlebot4_navigation:
  ubuntu:
    focal: [ros-humble-turtlebot4-navigation]
    jammy: [ros-humble-turtlebot4-navigation]
turtlebot4_node:
  ubuntu:
    focal: [ros-humble-turtlebot4-node]
    jammy: [ros-humble-turtlebot4-node]
turtlebot4_node_dbgsym:
  ubuntu:
    focal: [ros-humble-turtlebot4-node-dbgsym]
    jammy: [ros-humble-turtlebot4-node-dbgsym]
turtlebot4_python_tutorials:
  ubuntu:
    focal: [ros-humble-turtlebot4-python-tutorials]
    jammy: [ros-humble-turtlebot4-python-tutorials]
turtlebot4_robot:
  ubuntu:
    focal: [ros-humble-turtlebot4-robot]
    jammy: [ros-humble-turtlebot4-robot]
turtlebot4_setup:
  ubuntu:
    focal: [ros-humble-turtlebot4-setup]
    jammy: [ros-humble-turtlebot4-setup]
turtlebot4_simulator:
  ubuntu:
    focal: [ros-humble-turtlebot4-simulator]
    jammy: [ros-humble-turtlebot4-simulator]
turtlebot4_tests:
  ubuntu:
    focal: [ros-humble-turtlebot4-tests]
    jammy: [ros-humble-turtlebot4-tests]
turtlebot4_tutorials:
  ubuntu:
    focal: [ros-humble-turtlebot4-tutorials]
    jammy: [ros-humble-turtlebot4-tutorials]
turtlebot4_viz:
  ubuntu:
    focal: [ros-humble-turtlebot4-viz]
    jammy: [ros-humble-turtlebot4-viz]
turtlesim:
  ubuntu:
    focal: [ros-humble-turtlesim]
    jammy: [ros-humble-turtlesim]
turtlesim_dbgsym:
  ubuntu:
    focal: [ros-humble-turtlesim-dbgsym]
    jammy: [ros-humble-turtlesim-dbgsym]
tuw_airskin_msgs:
  ubuntu:
    focal: [ros-humble-tuw-airskin-msgs]
    jammy: [ros-humble-tuw-airskin-msgs]
tuw_airskin_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-tuw-airskin-msgs-dbgsym]
    jammy: [ros-humble-tuw-airskin-msgs-dbgsym]
tuw_geometry:
  ubuntu:
    focal: [ros-humble-tuw-geometry]
    jammy: [ros-humble-tuw-geometry]
tuw_geometry_dbgsym:
  ubuntu:
    focal: [ros-humble-tuw-geometry-dbgsym]
    jammy: [ros-humble-tuw-geometry-dbgsym]
tuw_geometry_msgs:
  ubuntu:
    focal: [ros-humble-tuw-geometry-msgs]
    jammy: [ros-humble-tuw-geometry-msgs]
tuw_geometry_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-tuw-geometry-msgs-dbgsym]
    jammy: [ros-humble-tuw-geometry-msgs-dbgsym]
tuw_msgs:
  ubuntu:
    focal: [ros-humble-tuw-msgs]
    jammy: [ros-humble-tuw-msgs]
tuw_multi_robot_msgs:
  ubuntu:
    focal: [ros-humble-tuw-multi-robot-msgs]
    jammy: [ros-humble-tuw-multi-robot-msgs]
tuw_multi_robot_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-tuw-multi-robot-msgs-dbgsym]
    jammy: [ros-humble-tuw-multi-robot-msgs-dbgsym]
tuw_nav_msgs:
  ubuntu:
    focal: [ros-humble-tuw-nav-msgs]
    jammy: [ros-humble-tuw-nav-msgs]
tuw_nav_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-tuw-nav-msgs-dbgsym]
    jammy: [ros-humble-tuw-nav-msgs-dbgsym]
tuw_object_msgs:
  ubuntu:
    focal: [ros-humble-tuw-object-msgs]
    jammy: [ros-humble-tuw-object-msgs]
tuw_object_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-tuw-object-msgs-dbgsym]
    jammy: [ros-humble-tuw-object-msgs-dbgsym]
tvm_vendor:
  ubuntu:
    focal: [ros-humble-tvm-vendor]
    jammy: [ros-humble-tvm-vendor]
tvm_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-tvm-vendor-dbgsym]
    jammy: [ros-humble-tvm-vendor-dbgsym]
twist_mux:
  ubuntu:
    focal: [ros-humble-twist-mux]
    jammy: [ros-humble-twist-mux]
twist_mux_dbgsym:
  ubuntu:
    focal: [ros-humble-twist-mux-dbgsym]
    jammy: [ros-humble-twist-mux-dbgsym]
twist_mux_msgs:
  ubuntu:
    focal: [ros-humble-twist-mux-msgs]
    jammy: [ros-humble-twist-mux-msgs]
twist_mux_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-twist-mux-msgs-dbgsym]
    jammy: [ros-humble-twist-mux-msgs-dbgsym]
twist_stamper:
  ubuntu:
    focal: [ros-humble-twist-stamper]
    jammy: [ros-humble-twist-stamper]
ublox:
  ubuntu:
    focal: [ros-humble-ublox]
    jammy: [ros-humble-ublox]
ublox_dgnss:
  ubuntu:
    focal: [ros-humble-ublox-dgnss]
    jammy: [ros-humble-ublox-dgnss]
ublox_dgnss_node:
  ubuntu:
    focal: [ros-humble-ublox-dgnss-node]
    jammy: [ros-humble-ublox-dgnss-node]
ublox_dgnss_node_dbgsym:
  ubuntu:
    focal: [ros-humble-ublox-dgnss-node-dbgsym]
    jammy: [ros-humble-ublox-dgnss-node-dbgsym]
ublox_gps:
  ubuntu:
    focal: [ros-humble-ublox-gps]
    jammy: [ros-humble-ublox-gps]
ublox_gps_dbgsym:
  ubuntu:
    focal: [ros-humble-ublox-gps-dbgsym]
    jammy: [ros-humble-ublox-gps-dbgsym]
ublox_msgs:
  ubuntu:
    focal: [ros-humble-ublox-msgs]
    jammy: [ros-humble-ublox-msgs]
ublox_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-ublox-msgs-dbgsym]
    jammy: [ros-humble-ublox-msgs-dbgsym]
ublox_nav_sat_fix_hp_node:
  ubuntu:
    focal: [ros-humble-ublox-nav-sat-fix-hp-node]
    jammy: [ros-humble-ublox-nav-sat-fix-hp-node]
ublox_nav_sat_fix_hp_node_dbgsym:
  ubuntu:
    focal: [ros-humble-ublox-nav-sat-fix-hp-node-dbgsym]
    jammy: [ros-humble-ublox-nav-sat-fix-hp-node-dbgsym]
ublox_serialization:
  ubuntu:
    focal: [ros-humble-ublox-serialization]
    jammy: [ros-humble-ublox-serialization]
ublox_ubx_interfaces:
  ubuntu:
    focal: [ros-humble-ublox-ubx-interfaces]
    jammy: [ros-humble-ublox-ubx-interfaces]
ublox_ubx_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-ublox-ubx-interfaces-dbgsym]
    jammy: [ros-humble-ublox-ubx-interfaces-dbgsym]
ublox_ubx_msgs:
  ubuntu:
    focal: [ros-humble-ublox-ubx-msgs]
    jammy: [ros-humble-ublox-ubx-msgs]
ublox_ubx_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-ublox-ubx-msgs-dbgsym]
    jammy: [ros-humble-ublox-ubx-msgs-dbgsym]
udp_driver:
  ubuntu:
    focal: [ros-humble-udp-driver]
    jammy: [ros-humble-udp-driver]
udp_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-udp-driver-dbgsym]
    jammy: [ros-humble-udp-driver-dbgsym]
udp_msgs:
  ubuntu:
    focal: [ros-humble-udp-msgs]
    jammy: [ros-humble-udp-msgs]
udp_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-udp-msgs-dbgsym]
    jammy: [ros-humble-udp-msgs-dbgsym]
uncrustify_vendor:
  ubuntu:
    focal: [ros-humble-uncrustify-vendor]
    jammy: [ros-humble-uncrustify-vendor]
uncrustify_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-uncrustify-vendor-dbgsym]
    jammy: [ros-humble-uncrustify-vendor-dbgsym]
unique_identifier_msgs:
  ubuntu:
    focal: [ros-humble-unique-identifier-msgs]
    jammy: [ros-humble-unique-identifier-msgs]
unique_identifier_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-unique-identifier-msgs-dbgsym]
    jammy: [ros-humble-unique-identifier-msgs-dbgsym]
ur:
  ubuntu:
    focal: [ros-humble-ur]
    jammy: [ros-humble-ur]
ur_bringup:
  ubuntu:
    focal: [ros-humble-ur-bringup]
    jammy: [ros-humble-ur-bringup]
ur_calibration:
  ubuntu:
    focal: [ros-humble-ur-calibration]
    jammy: [ros-humble-ur-calibration]
ur_calibration_dbgsym:
  ubuntu:
    focal: [ros-humble-ur-calibration-dbgsym]
    jammy: [ros-humble-ur-calibration-dbgsym]
ur_client_library:
  ubuntu:
    focal: [ros-humble-ur-client-library]
    jammy: [ros-humble-ur-client-library]
ur_client_library_dbgsym:
  ubuntu:
    focal: [ros-humble-ur-client-library-dbgsym]
    jammy: [ros-humble-ur-client-library-dbgsym]
ur_controllers:
  ubuntu:
    focal: [ros-humble-ur-controllers]
    jammy: [ros-humble-ur-controllers]
ur_controllers_dbgsym:
  ubuntu:
    focal: [ros-humble-ur-controllers-dbgsym]
    jammy: [ros-humble-ur-controllers-dbgsym]
ur_dashboard_msgs:
  ubuntu:
    focal: [ros-humble-ur-dashboard-msgs]
    jammy: [ros-humble-ur-dashboard-msgs]
ur_dashboard_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-ur-dashboard-msgs-dbgsym]
    jammy: [ros-humble-ur-dashboard-msgs-dbgsym]
ur_description:
  ubuntu:
    focal: [ros-humble-ur-description]
    jammy: [ros-humble-ur-description]
ur_moveit_config:
  ubuntu:
    focal: [ros-humble-ur-moveit-config]
    jammy: [ros-humble-ur-moveit-config]
ur_msgs:
  ubuntu:
    focal: [ros-humble-ur-msgs]
    jammy: [ros-humble-ur-msgs]
ur_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-ur-msgs-dbgsym]
    jammy: [ros-humble-ur-msgs-dbgsym]
ur_robot_driver:
  ubuntu:
    focal: [ros-humble-ur-robot-driver]
    jammy: [ros-humble-ur-robot-driver]
ur5_gripper_moveit_config:
  ubuntu:
    focal: [ros-humble-ur5-gripper-moveit-config]
    jammy: [ros-humble-ur5-gripper-moveit-config]
ur5_robotiq_85_description:
  ubuntu:
    focal: [ros-humble-ur5-robotiq-85-description]
    jammy: [ros-humble-ur5-robotiq-85-description]
ur_robot_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-ur-robot-driver-dbgsym]
    jammy: [ros-humble-ur-robot-driver-dbgsym]
urdf:
  ubuntu:
    focal: [ros-humble-urdf]
    jammy: [ros-humble-urdf]
urdf_dbgsym:
  ubuntu:
    focal: [ros-humble-urdf-dbgsym]
    jammy: [ros-humble-urdf-dbgsym]
urdf_launch:
  ubuntu:
    focal: [ros-humble-urdf-launch]
    jammy: [ros-humble-urdf-launch]
urdf_parser_plugin:
  ubuntu:
    focal: [ros-humble-urdf-parser-plugin]
    jammy: [ros-humble-urdf-parser-plugin]
urdf_test:
  ubuntu:
    focal: [ros-humble-urdf-test]
    jammy: [ros-humble-urdf-test]
urdf_tutorial:
  ubuntu:
    focal: [ros-humble-urdf-tutorial]
    jammy: [ros-humble-urdf-tutorial]
urdfdom:
  ubuntu:
    focal: [ros-humble-urdfdom]
    jammy: [ros-humble-urdfdom]
urdfdom_dbgsym:
  ubuntu:
    focal: [ros-humble-urdfdom-dbgsym]
    jammy: [ros-humble-urdfdom-dbgsym]
urdfdom_headers:
  ubuntu:
    focal: [ros-humble-urdfdom-headers]
    jammy: [ros-humble-urdfdom-headers]
urdfdom_py:
  ubuntu:
    focal: [ros-humble-urdfdom-py]
    jammy: [ros-humble-urdfdom-py]
urg_c:
  ubuntu:
    focal: [ros-humble-urg-c]
    jammy: [ros-humble-urg-c]
urg_c_dbgsym:
  ubuntu:
    focal: [ros-humble-urg-c-dbgsym]
    jammy: [ros-humble-urg-c-dbgsym]
urg_node:
  ubuntu:
    focal: [ros-humble-urg-node]
    jammy: [ros-humble-urg-node]
urg_node_dbgsym:
  ubuntu:
    focal: [ros-humble-urg-node-dbgsym]
    jammy: [ros-humble-urg-node-dbgsym]
urg_node_msgs:
  ubuntu:
    focal: [ros-humble-urg-node-msgs]
    jammy: [ros-humble-urg-node-msgs]
urg_node_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-urg-node-msgs-dbgsym]
    jammy: [ros-humble-urg-node-msgs-dbgsym]
usb_cam:
  ubuntu:
    focal: [ros-humble-usb-cam]
    jammy: [ros-humble-usb-cam]
usb_cam_dbgsym:
  ubuntu:
    focal: [ros-humble-usb-cam-dbgsym]
    jammy: [ros-humble-usb-cam-dbgsym]
v4l2_camera:
  ubuntu:
    focal: [ros-humble-v4l2-camera]
    jammy: [ros-humble-v4l2-camera]
v4l2_camera_dbgsym:
  ubuntu:
    focal: [ros-humble-v4l2-camera-dbgsym]
    jammy: [ros-humble-v4l2-camera-dbgsym]
vda5050_msgs:
  ubuntu:
    focal: [ros-humble-vda5050-msgs]
    jammy: [ros-humble-vda5050-msgs]
velocity_controllers:
  ubuntu:
    focal: [ros-humble-velocity-controllers]
    jammy: [ros-humble-velocity-controllers]
velocity_controllers_dbgsym:
  ubuntu:
    focal: [ros-humble-velocity-controllers-dbgsym]
    jammy: [ros-humble-velocity-controllers-dbgsym]
velodyne:
  ubuntu:
    focal: [ros-humble-velodyne]
    jammy: [ros-humble-velodyne]
velodyne_description:
  ubuntu:
    focal: [ros-humble-velodyne-description]
    jammy: [ros-humble-velodyne-description]
velodyne_driver:
  ubuntu:
    focal: [ros-humble-velodyne-driver]
    jammy: [ros-humble-velodyne-driver]
velodyne_driver_dbgsym:
  ubuntu:
    focal: [ros-humble-velodyne-driver-dbgsym]
    jammy: [ros-humble-velodyne-driver-dbgsym]
velodyne_laserscan:
  ubuntu:
    focal: [ros-humble-velodyne-laserscan]
    jammy: [ros-humble-velodyne-laserscan]
velodyne_laserscan_dbgsym:
  ubuntu:
    focal: [ros-humble-velodyne-laserscan-dbgsym]
    jammy: [ros-humble-velodyne-laserscan-dbgsym]
velodyne_msgs:
  ubuntu:
    focal: [ros-humble-velodyne-msgs]
    jammy: [ros-humble-velodyne-msgs]
velodyne_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-velodyne-msgs-dbgsym]
    jammy: [ros-humble-velodyne-msgs-dbgsym]
velodyne_pointcloud:
  ubuntu:
    focal: [ros-humble-velodyne-pointcloud]
    jammy: [ros-humble-velodyne-pointcloud]
velodyne_pointcloud_dbgsym:
  ubuntu:
    focal: [ros-humble-velodyne-pointcloud-dbgsym]
    jammy: [ros-humble-velodyne-pointcloud-dbgsym]
vision_msgs:
  ubuntu:
    focal: [ros-humble-vision-msgs]
    jammy: [ros-humble-vision-msgs]
vision_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-vision-msgs-dbgsym]
    jammy: [ros-humble-vision-msgs-dbgsym]
vision_msgs_layers:
  ubuntu:
    focal: [ros-humble-vision-msgs-layers]
    jammy: [ros-humble-vision-msgs-layers]
vision_msgs_layers_dbgsym:
  ubuntu:
    focal: [ros-humble-vision-msgs-layers-dbgsym]
    jammy: [ros-humble-vision-msgs-layers-dbgsym]
vision_msgs_rviz_plugins:
  ubuntu:
    focal: [ros-humble-vision-msgs-rviz-plugins]
    jammy: [ros-humble-vision-msgs-rviz-plugins]
vision_msgs_rviz_plugins_dbgsym:
  ubuntu:
    focal: [ros-humble-vision-msgs-rviz-plugins-dbgsym]
    jammy: [ros-humble-vision-msgs-rviz-plugins-dbgsym]
vision_opencv:
  ubuntu:
    focal: [ros-humble-vision-opencv]
    jammy: [ros-humble-vision-opencv]
visp:
  ubuntu:
    focal: [ros-humble-visp]
    jammy: [ros-humble-visp]
visp_dbgsym:
  ubuntu:
    focal: [ros-humble-visp-dbgsym]
    jammy: [ros-humble-visp-dbgsym]
visualization_msgs:
  ubuntu:
    focal: [ros-humble-visualization-msgs]
    jammy: [ros-humble-visualization-msgs]
visualization_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-visualization-msgs-dbgsym]
    jammy: [ros-humble-visualization-msgs-dbgsym]
vitis_common:
  ubuntu:
    focal: [ros-humble-vitis-common]
    jammy: [ros-humble-vitis-common]
vrpn:
  ubuntu:
    focal: [ros-humble-vrpn]
    jammy: [ros-humble-vrpn]
vrpn_mocap:
  ubuntu:
    focal: [ros-humble-vrpn-mocap]
    jammy: [ros-humble-vrpn-mocap]
vrpn_mocap_dbgsym:
  ubuntu:
    focal: [ros-humble-vrpn-mocap-dbgsym]
    jammy: [ros-humble-vrpn-mocap-dbgsym]
wall_follower_ros2:
  ubuntu:
    focal: [ros-humble-wall-follower-ros2]
    jammy: [ros-humble-wall-follower-ros2]
wall_follower_ros2_dbgsym:
  ubuntu:
    focal: [ros-humble-wall-follower-ros2-dbgsym]
    jammy: [ros-humble-wall-follower-ros2-dbgsym]
warehouse_ros:
  ubuntu:
    focal: [ros-humble-warehouse-ros]
    jammy: [ros-humble-warehouse-ros]
warehouse_ros_dbgsym:
  ubuntu:
    focal: [ros-humble-warehouse-ros-dbgsym]
    jammy: [ros-humble-warehouse-ros-dbgsym]
warehouse_ros_sqlite:
  ubuntu:
    focal: [ros-humble-warehouse-ros-sqlite]
    jammy: [ros-humble-warehouse-ros-sqlite]
warehouse_ros_sqlite_dbgsym:
  ubuntu:
    focal: [ros-humble-warehouse-ros-sqlite-dbgsym]
    jammy: [ros-humble-warehouse-ros-sqlite-dbgsym]
webots_ros2_importer:
  ubuntu:
    focal: [ros-humble-webots-ros2-importer]
    jammy: [ros-humble-webots-ros2-importer]
webots_ros2_msgs:
  ubuntu:
    focal: [ros-humble-webots-ros2-msgs]
    jammy: [ros-humble-webots-ros2-msgs]
webots_ros2_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-webots-ros2-msgs-dbgsym]
    jammy: [ros-humble-webots-ros2-msgs-dbgsym]
weight_scale_interfaces:
  ubuntu:
    focal: [ros-humble-weight-scale-interfaces]
    jammy: [ros-humble-weight-scale-interfaces]
weight_scale_interfaces_dbgsym:
  ubuntu:
    focal: [ros-humble-weight-scale-interfaces-dbgsym]
    jammy: [ros-humble-weight-scale-interfaces-dbgsym]
wiimote:
  ubuntu:
    focal: [ros-humble-wiimote]
    jammy: [ros-humble-wiimote]
wiimote_dbgsym:
  ubuntu:
    focal: [ros-humble-wiimote-dbgsym]
    jammy: [ros-humble-wiimote-dbgsym]
wiimote_msgs:
  ubuntu:
    focal: [ros-humble-wiimote-msgs]
    jammy: [ros-humble-wiimote-msgs]
wiimote_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-wiimote-msgs-dbgsym]
    jammy: [ros-humble-wiimote-msgs-dbgsym]
wireless_msgs:
  ubuntu:
    focal: [ros-humble-wireless-msgs]
    jammy: [ros-humble-wireless-msgs]
wireless_msgs_dbgsym:
  ubuntu:
    focal: [ros-humble-wireless-msgs-dbgsym]
    jammy: [ros-humble-wireless-msgs-dbgsym]
wireless_watcher:
  ubuntu:
    focal: [ros-humble-wireless-watcher]
    jammy: [ros-humble-wireless-watcher]
x264:
  ubuntu:
    focal: [libx264-dev]
    jammy: [libx264-dev]
xacro:
  ubuntu:
    focal: [ros-humble-xacro]
    jammy: [ros-humble-xacro]
yaml_cpp_vendor:
  ubuntu:
    focal: [ros-humble-yaml-cpp-vendor]
    jammy: [ros-humble-yaml-cpp-vendor]
zbar_ros:
  ubuntu:
    focal: [ros-humble-zbar-ros]
    jammy: [ros-humble-zbar-ros]
zbar_ros_dbgsym:
  ubuntu:
    focal: [ros-humble-zbar-ros-dbgsym]
    jammy: [ros-humble-zbar-ros-dbgsym]
zed_components:
  ubuntu:
    focal: [ros-humble-zed-components]
    jammy: [ros-humble-zed-components]
zed_interfaces:
  ubuntu:
    focal: [ros-humble-zed-interfaces]
    jammy: [ros-humble-zed-interfaces]
zed_ros2:
  ubuntu:
    focal: [ros-humble-zed-ros2]
    jammy: [ros-humble-zed-ros2]
zed_wrapper:
  ubuntu:
    focal: [ros-humble-zed-wrapper]
    jammy: [ros-humble-zed-wrapper]
zenoh_bridge_dds:
  ubuntu:
    focal: [ros-humble-zenoh-bridge-dds]
    jammy: [ros-humble-zenoh-bridge-dds]
zenoh_bridge_dds_dbgsym:
  ubuntu:
    focal: [ros-humble-zenoh-bridge-dds-dbgsym]
    jammy: [ros-humble-zenoh-bridge-dds-dbgsym]
zlib_point_cloud_transport:
  ubuntu:
    focal: [ros-humble-zlib-point-cloud-transport]
    jammy: [ros-humble-zlib-point-cloud-transport]
zlib_point_cloud_transport_dbgsym:
  ubuntu:
    focal: [ros-humble-zlib-point-cloud-transport-dbgsym]
    jammy: [ros-humble-zlib-point-cloud-transport-dbgsym]
zmqpp_vendor:
  ubuntu:
    focal: [ros-humble-zmqpp-vendor]
    jammy: [ros-humble-zmqpp-vendor]
zmqpp_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-zmqpp-vendor-dbgsym]
    jammy: [ros-humble-zmqpp-vendor-dbgsym]
zstd_point_cloud_transport:
  ubuntu:
    focal: [ros-humble-zstd-point-cloud-transport]
    jammy: [ros-humble-zstd-point-cloud-transport]
zstd_point_cloud_transport_dbgsym:
  ubuntu:
    focal: [ros-humble-zstd-point-cloud-transport-dbgsym]
    jammy: [ros-humble-zstd-point-cloud-transport-dbgsym]
zstd_vendor:
  ubuntu:
    focal: [ros-humble-zstd-vendor]
    jammy: [ros-humble-zstd-vendor]
zstd_vendor_dbgsym:
  ubuntu:
    focal: [ros-humble-zstd-vendor-dbgsym]
    jammy: [ros-humble-zstd-vendor-dbgsym]
nodejs:
  ubuntu:
    focal: [nodejs]
    jammy: [nodejs]
yarnpkg:
  ubuntu:
    focal: [yarn]
    jammy: [yarn]
