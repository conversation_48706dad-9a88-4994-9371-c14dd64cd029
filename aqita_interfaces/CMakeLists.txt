
# TODO: License description

cmake_minimum_required(VERSION 3.8)
project(aqita_interfaces)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake_auto REQUIRED)
find_package(geometry_msgs REQUIRED)
ament_auto_find_build_dependencies()

# define the service interface files
set(SRV_FILES 
  "srv/ConsumeThreeFloats.srv" 
  "srv/MissionStartAck.srv" 
  "srv/MissionStartTrigger.srv" 
)

# define the message interface files
set(MSG_FILES
  "msg/Histogram.msg"
  "msg/ComponentStatus.msg"
)

# define the action interface files
set(ACT_FILES
  "action/MissionControl.action"
)

# generate interfaces
rosidl_generate_interfaces(${PROJECT_NAME}
  ${SRV_FILES}
  ${MSG_FILES}
  ${ACT_FILES}
  DEPENDENCIES std_msgs geometry_msgs builtin_interfaces 
)
ament_export_dependencies(rosidl_default_runtime)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_auto_package()
