# aqita_interfaces

`aqita_interfaces` provides custom ROS2 message and service definitions used by the AQITA system. This package defines common interfaces that can be shared across multiple nodes and components.

---

## Package Overview

This package includes:

- **Custom Messages**
  - `ComponentStatus`: Represents the status of a component, including:
    - A timestamp
    - A state (`OK`, `Degraded`, `Error`)
    - A description string
  - `Histogram`: Custom image histogram message.

- **Custom Services**
  - `ConsumeThreeFloats`: A service to send three floating point numbers to a node.
  - `MissionStartAck`: A service used to acknowledge mission start.
  - `MissionStartTrigger`: A service used to trigger the start of a mission.

These interfaces standardize communication within the AQITA system.

---

## Usage

After building and sourcing, you can inspect the interfaces, for example:

```bash
ros2 interface show aqita_interfaces/msg/ComponentStatus
```

---

## Dependencies

This package depends on:

- std_msgs
- builtin_interfaces
- rosidl_default_runtime
- rosidl_default_generators
