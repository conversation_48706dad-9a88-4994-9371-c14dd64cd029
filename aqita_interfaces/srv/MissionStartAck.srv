# Service interface for mission start acknowledge.
# Used by client and server nodes which communicate with each other to determine whether a mission should start or not
# Request
string mission_id   # unique ID of the mission to start
string drone_id     # unique ID of the drone which should execute the mission
---
# Response
bool ack            # response indicating whether the mission should start (true) or should not start (false)

