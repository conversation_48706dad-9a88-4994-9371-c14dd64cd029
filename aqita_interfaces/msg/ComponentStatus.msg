# Message describing the status of a system component.
# The possible component states are:
#   OK          - component is working normally
#   DEGRADED    - component operation is not optimal due to some reason. Its output should be used considering the outputs of other components
#   ERROR       - component output should NOT be used. There is an error, preventing the normal component operation
builtin_interfaces/Time stamp
uint8 state
string description

uint8 OK=0
uint8 DEGRADED=1
uint8 ERROR=2

