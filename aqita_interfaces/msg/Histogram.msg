# This is an image histogram message.
# The histogram is represented as an array of uint32 elements and each element should correspond to a range of pixel values.
# The value of an array element should be the number of pixels within the corresponding range.
# The image to which the histogram applies should be specified with the frame_id parameter within the header field

std_msgs/Header header
int32 range
int32 resolution
int32 image_width
int32 image_height
std_msgs/UInt32MultiArray histogram

