from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PythonExpression
from launch_ros.actions import Node


def generate_launch_description():
    """Generate launch description for AQITA monitoring system."""

    # Declare launch arguments
    launch_args = []
    launch_args.append(
        DeclareLaunchArgument(
            'camera_mode', default_value='stereo', choices=['rgbd', 'stereo'], description='Camera mode: rgbd or stereo'
        )
    )
    launch_args.append(
        DeclareLaunchArgument(
            'cameras', default_value='cam_front,cam_left,cam_right', description='Comma-separated list of camera names'
        )
    )
    launch_args.append(
        DeclareLaunchArgument('base_frame', default_value='aqita', description='Robot base frame for TF')
    )
    launch_args.append(DeclareLaunchArgument('map_frame', default_value='map', description='Map frame for TF'))
    launch_args.append(DeclareLaunchArgument('odom_frame', default_value='odom', description='Odometry frame for TF'))
    launch_args.append(DeclareLaunchArgument('odom_topic', default_value='/odom', description='Odometry topic'))
    launch_args.append(
        DeclareLaunchArgument(
            'odom_info_topic', default_value='/odom_info', description='Odometry info topic from RTAB-Map'
        )
    )
    launch_args.append(
        DeclareLaunchArgument('enable_respawn', default_value='true', description='Enable automatic respawn of nodes')
    )

    # Get launch configurations
    camera_mode = LaunchConfiguration('camera_mode')
    base_frame = LaunchConfiguration('base_frame')
    map_frame = LaunchConfiguration('map_frame')
    odom_frame = LaunchConfiguration('odom_frame')
    odom_topic = LaunchConfiguration('odom_topic')
    odom_info_topic = LaunchConfiguration('odom_info_topic')
    enable_respawn = LaunchConfiguration('enable_respawn')

    # Define default cameras (can be overridden by launch argument)
    cameras = ['cam_front', 'cam_left', 'cam_right']

    # Camera monitor nodes
    camera_nodes = []
    for camera in cameras:
        camera_nodes.append(
            Node(
                package='aqita_monitor',
                executable='camera_monitor_node',
                name=f'{camera}',
                namespace=camera,
                parameters=[
                    {
                        'camera_name': camera,
                        'camera_mode': camera_mode,
                        'primary_topic': PythonExpression(
                            [
                                "'",
                                f"/{camera}/infra1/image_rect_raw' if '",
                                camera_mode,
                                "' == 'rgbd' else '",
                                f"/{camera}/infra1/image_rect_raw",
                                "'",
                            ]
                        ),
                        'secondary_topic': PythonExpression(
                            [
                                "'",
                                f"/{camera}/depth/image_rect_raw' if '",
                                camera_mode,
                                "' == 'rgbd' else '",
                                f"/{camera}/infra2/image_rect_raw",
                                "'",
                            ]
                        ),
                        'image_timeout': 5.0,
                        'fps_threshold': 15.0,
                        'sync_tolerance': 0.05,
                    }
                ],
                output='screen',
                respawn=PythonExpression(["'", enable_respawn, "' == 'true'"]),
                respawn_delay=2.0,
            )
        )

    odometry_monitor = Node(
        package='aqita_monitor',
        executable='odometry_monitor_node',
        name='odometry_monitor',
        namespace='odometry',
        parameters=[{'odom_topic': odom_topic, 'odom_info_topic': odom_info_topic, 'odom_timeout': 5.0}],
        output='screen',
        respawn=PythonExpression(["'", enable_respawn, "' == 'true'"]),
        respawn_delay=2.0,
    )

    slam_monitor = Node(
        package='aqita_monitor',
        executable='slam_monitor_node',
        name='slam_monitor',
        namespace='slam',
        parameters=[
            {
                'base_frame': base_frame,
                'map_frame': map_frame,
                'odom_frame': odom_frame,
                'transform_timeout': 2.0,
                'cameras': cameras,
                'subsystem_timeout': 5.0,
            }
        ],
        output='screen',
        respawn=PythonExpression(["'", enable_respawn, "' == 'true'"]),
        respawn_delay=2.0,
    )

    return LaunchDescription([*launch_args, *camera_nodes, odometry_monitor, slam_monitor])
