# AQITA Monitor Launch Usage
## Basic Launch
```bash
ros2 launch aqita_monitor monitoring.launch.py
```

## Launch with RGBD cameras
```bash
ros2 launch aqita_monitor monitoring.launch.py camera_mode:=rgbd
```

## Launch with custom frames
```bash
ros2 launch aqita_monitor monitoring.launch.py base_frame:=base_footprint map_frame:=world
```

## Launch without respawn
```bash
ros2 launch aqita_monitor monitoring.launch.py enable_respawn:=false
```

## Check monitor statuses
```bash
# List all status topics
ros2 topic list | grep status

# Expected topics:
# /odometry/status
# /slam/status
# /cam_front/status
# /cam_left/status
# /cam_right/status

# Monitor individual status
ros2 topic echo /slam/status

# Monitor all statuses at once
ros2 topic echo /odometry/status &
ros2 topic echo /cam_front/status &
ros2 topic echo /cam_left/status &
ros2 topic echo /cam_right/status &
ros2 topic echo /slam/status
```

## Status Message Format
The ComponentStatus message contains:
- `stamp`: Timestamp
- `state`: OK (0), DEGRADED (1), or ERROR (2)
- `description`: Detailed status information

Example output:
```
stamp:
  sec: 1234567890
  nanosec: 123456789
state: 1  # DEGRADED
description: "Camera issues (2/3 OK): cam_left: Low fps: 15 (need >20)"
```

## Monitoring GUI
To run the monitoring gui:
```
#from this dir
python3 monitoring_gui.py
```
If no tk python:
```
sudo apt update && sudo apt install python3-tk -y
```