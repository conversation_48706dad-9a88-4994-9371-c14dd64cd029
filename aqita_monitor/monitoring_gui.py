#!/usr/bin/env python3
"""
AQITA Status Monitor GUI
Real-time monitoring interface for robot subsystems
"""

import rclpy
from rclpy.node import Node
from aqita_interfaces.msg import ComponentStatus
import threading
import tkinter as tk
from tkinter import ttk, font, filedialog, messagebox
from datetime import datetime
import time
import os
import signal
import sys


class StatusMonitorGUI(Node):
    """Status monitor GUI for AQITA robot subsystems"""

    def __init__(self):
        super().__init__('aqita_status_monitor_gui')

        # Shutdown handling
        self.shutdown_event = threading.Event()
        self.gui_running = False

        # Define subsystems
        self.subsystems = {
            '/odometry/status': {
                'name': 'Visual Odometry',
                'short': 'ODOM',
                'description': 'Visual position tracking and motion estimation',
            },
            '/slam/status': {'name': 'SLAM System', 'short': 'SLAM', 'description': 'Localization and mapping'},
            '/cam_front/status': {'name': 'Front Camera', 'short': 'CAM-F', 'description': 'Forward stereo vision'},
            '/cam_left/status': {'name': 'Left Camera', 'short': 'CAM-L', 'description': 'Left stereo vision'},
            '/cam_right/status': {'name': 'Right Camera', 'short': 'CAM-R', 'description': 'Right stereo vision'},
        }

        # State tracking
        self.current_states = {}
        self.last_update_times = {}
        self.status_widgets = {}
        self.log_entries = []
        self.max_log_entries = 1000

        # Modern color palette
        self.colors = {
            'ok': '#22C55E',  # Green
            'degraded': '#F59E0B',  # Amber
            'error': '#EF4444',  # Red
            'unknown': '#6B7280',  # Gray
            'bg': '#111827',  # Dark
            'card': '#1F2937',  # Card background
            'card_hover': '#374151',  # Card hover
            'text': '#F9FAFB',  # Light text
            'subtext': '#9CA3AF',  # Muted text
            'border': '#374151',  # Border color
            'log_bg': '#0F172A',  # Log background
            'button': '#3B82F6',  # Blue button
            'button_hover': '#2563EB',
            'button_danger': '#DC2626',
        }

        # Subscribe to topics
        self.subscribers = []
        for topic in self.subsystems.keys():
            sub = self.create_subscription(
                ComponentStatus, topic, lambda msg, t=topic: self.handle_status_update(msg, t), 10
            )
            self.subscribers.append(sub)

        # Set up the GUI
        self.setup_gui()
        self.get_logger().info("Status monitor started")

    def handle_status_update(self, msg, topic):
        """Process incoming status messages"""
        if self.shutdown_event.is_set():
            return

        state_names = {ComponentStatus.OK: 'OK', ComponentStatus.DEGRADED: 'DEGRADED', ComponentStatus.ERROR: 'ERROR'}

        state = state_names.get(msg.state, 'UNKNOWN')
        description = msg.description if msg.description else 'Operating normally'
        timestamp = datetime.now()

        # Check if state changed
        old_state = self.current_states.get(topic, {}).get('state', 'UNKNOWN')
        old_desc = self.current_states.get(topic, {}).get('description', '')

        # Update current state
        self.current_states[topic] = {'state': state, 'description': description, 'timestamp': timestamp}
        self.last_update_times[topic] = time.time()

        # Log significant changes
        if old_state != state or (state != 'OK' and description != old_desc):
            self.add_log_entry(topic, state, description, timestamp)

    def add_log_entry(self, topic, state, description, timestamp):
        """Add entry to the log"""
        if self.shutdown_event.is_set():
            return

        subsystem = self.subsystems[topic]['short']
        entry = {'timestamp': timestamp, 'subsystem': subsystem, 'state': state, 'description': description}

        self.log_entries.append(entry)

        # Keep log size manageable
        if len(self.log_entries) > self.max_log_entries:
            self.log_entries.pop(0)

        # Update log display if GUI is running
        if self.gui_running:
            self.window.after_idle(self.update_log_display)

    def setup_gui(self):
        """Create the main window and all widgets"""
        self.window = tk.Tk()
        self.window.title("AQITA System Monitor")
        self.window.geometry("1400x900")
        self.window.configure(bg=self.colors['bg'])
        self.window.minsize(1200, 700)

        # Set window icon (if you have one)
        # self.window.iconbitmap('icon.ico')

        # Configure grid
        self.window.grid_rowconfigure(1, weight=1)
        self.window.grid_columnconfigure(0, weight=1)

        # Custom fonts
        self.fonts = {
            'title': font.Font(family="Arial", size=18, weight="bold"),
            'heading': font.Font(family="Arial", size=12, weight="bold"),
            'body': font.Font(family="Arial", size=10),
            'mono': font.Font(family="Consolas", size=9),
            'small': font.Font(family="Arial", size=9),
        }

        # Build interface
        self.create_header()
        self.create_main_content()
        self.create_footer()

        # Bind keyboard shortcuts
        self.window.bind('<Control-s>', lambda e: self.save_log())
        self.window.bind('<Control-l>', lambda e: self.clear_log())
        self.window.bind('<Escape>', lambda e: self.shutdown())

        # Handle window close
        self.window.protocol("WM_DELETE_WINDOW", self.shutdown)

        # Mark GUI as running
        self.gui_running = True

        # Start update loop
        self.update_display()

    def create_header(self):
        """Create header section"""
        header = tk.Frame(self.window, bg=self.colors['bg'], height=70)
        header.grid(row=0, column=0, sticky='ew', padx=20, pady=(15, 5))
        header.grid_columnconfigure(1, weight=1)

        # Title section
        title_frame = tk.Frame(header, bg=self.colors['bg'])
        title_frame.grid(row=0, column=0, sticky='w')

        title = tk.Label(
            title_frame,
            text="AQITA SYSTEM MONITOR",
            font=self.fonts['title'],
            bg=self.colors['bg'],
            fg=self.colors['text'],
        )
        title.pack(anchor='w')

        subtitle = tk.Label(
            title_frame,
            text="Real-time subsystem health tracking",
            font=self.fonts['body'],
            bg=self.colors['bg'],
            fg=self.colors['subtext'],
        )
        subtitle.pack(anchor='w')

        # Status section
        status_frame = tk.Frame(header, bg=self.colors['bg'])
        status_frame.grid(row=0, column=2, sticky='e')

        self.time_label = tk.Label(
            status_frame, text="", font=self.fonts['small'], bg=self.colors['bg'], fg=self.colors['subtext']
        )
        self.time_label.pack(anchor='e')

        self.summary_label = tk.Label(
            status_frame, text="INITIALIZING", font=self.fonts['heading'], bg=self.colors['bg'], fg=self.colors['text']
        )
        self.summary_label.pack(anchor='e', pady=(5, 0))

    def create_main_content(self):
        """Create main content area"""
        main = tk.Frame(self.window, bg=self.colors['bg'])
        main.grid(row=1, column=0, sticky='nsew', padx=20, pady=10)
        main.grid_columnconfigure(0, weight=3)
        main.grid_columnconfigure(1, weight=2)
        main.grid_rowconfigure(0, weight=1)

        # Status cards panel
        self.create_status_panel(main)

        # Log panel
        self.create_log_panel(main)

    def create_status_panel(self, parent):
        """Create status cards panel"""
        panel = tk.Frame(parent, bg=self.colors['bg'])
        panel.grid(row=0, column=0, sticky='nsew', padx=(0, 10))

        # Cards container
        self.cards_frame = tk.Frame(panel, bg=self.colors['bg'])
        self.cards_frame.pack(fill='both', expand=True)

        # Create cards
        for topic, info in self.subsystems.items():
            self.create_status_card(self.cards_frame, topic, info)

    def create_status_card(self, parent, topic, info):
        """Create a status card"""
        # Card container
        card = tk.Frame(parent, bg=self.colors['card'], highlightbackground=self.colors['border'], highlightthickness=1)
        card.pack(fill='x', pady=5)

        # Grid layout
        card.grid_columnconfigure(1, weight=1)

        # Status indicator
        indicator_frame = tk.Frame(card, bg=self.colors['card'], width=100)
        indicator_frame.grid(row=0, column=0, rowspan=2, sticky='ns', padx=15, pady=15)
        indicator_frame.grid_propagate(False)

        indicator = tk.Label(
            indicator_frame,
            text="WAIT",
            font=font.Font(family="Arial", size=10, weight="bold"),
            bg=self.colors['card'],
            fg=self.colors['unknown'],
        )
        indicator.place(relx=0.5, rely=0.5, anchor='center')

        # Info section
        name_label = tk.Label(
            card,
            text=info['name'],
            font=self.fonts['heading'],
            bg=self.colors['card'],
            fg=self.colors['text'],
            anchor='w',
        )
        name_label.grid(row=0, column=1, sticky='w', padx=(0, 10), pady=(15, 2))

        # Details with fixed frame
        details_frame = tk.Frame(card, bg=self.colors['card'], height=35)
        details_frame.grid(row=1, column=1, sticky='ew', padx=(0, 10), pady=(0, 15))
        details_frame.grid_propagate(False)

        details = tk.Label(
            details_frame,
            text=info['description'],
            font=self.fonts['mono'],
            bg=self.colors['card'],
            fg=self.colors['subtext'],
            anchor='w',
            justify='left',
        )
        details.pack(anchor='w')

        # Time label
        time_label = tk.Label(
            card, text="—", font=self.fonts['small'], bg=self.colors['card'], fg=self.colors['subtext']
        )
        time_label.grid(row=0, column=2, sticky='ne', padx=15, pady=(15, 0))

        # Hover effect
        def on_enter(e):
            if not self.shutdown_event.is_set():
                card.config(bg=self.colors['card_hover'])
                for widget in [indicator_frame, indicator, name_label, details_frame, details, time_label]:
                    widget.config(bg=self.colors['card_hover'])

        def on_leave(e):
            if not self.shutdown_event.is_set():
                card.config(bg=self.colors['card'])
                for widget in [indicator_frame, indicator, name_label, details_frame, details, time_label]:
                    widget.config(bg=self.colors['card'])

        card.bind("<Enter>", on_enter)
        card.bind("<Leave>", on_leave)

        # Store references
        self.status_widgets[topic] = {'card': card, 'indicator': indicator, 'details': details, 'time': time_label}

    def create_log_panel(self, parent):
        """Create log panel"""
        panel = tk.Frame(
            parent, bg=self.colors['card'], highlightbackground=self.colors['border'], highlightthickness=1
        )
        panel.grid(row=0, column=1, sticky='nsew')

        # Header
        header = tk.Frame(panel, bg=self.colors['card'])
        header.pack(fill='x', padx=15, pady=(15, 10))

        log_title = tk.Label(
            header, text="ACTIVITY LOG", font=self.fonts['heading'], bg=self.colors['card'], fg=self.colors['text']
        )
        log_title.pack(side='left')

        # Buttons
        btn_frame = tk.Frame(header, bg=self.colors['card'])
        btn_frame.pack(side='right')

        clear_btn = tk.Button(
            btn_frame,
            text="Clear",
            font=self.fonts['small'],
            bg=self.colors['button_danger'],
            fg='white',
            bd=0,
            padx=12,
            pady=4,
            cursor='hand2',
            command=self.clear_log,
        )
        clear_btn.pack(side='left', padx=2)

        save_btn = tk.Button(
            btn_frame,
            text="Export",
            font=self.fonts['small'],
            bg=self.colors['button'],
            fg='white',
            bd=0,
            padx=12,
            pady=4,
            cursor='hand2',
            command=self.save_log,
        )
        save_btn.pack(side='left', padx=2)

        # Add hover effects
        def btn_hover(btn, hover_color):
            original_color = btn['bg']
            btn.bind('<Enter>', lambda e: btn.config(bg=hover_color))
            btn.bind('<Leave>', lambda e: btn.config(bg=original_color))

        btn_hover(clear_btn, '#B91C1C')
        btn_hover(save_btn, self.colors['button_hover'])

        # Log text area
        log_frame = tk.Frame(panel, bg=self.colors['log_bg'])
        log_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))

        self.log_text = tk.Text(
            log_frame,
            font=self.fonts['mono'],
            bg=self.colors['log_bg'],
            fg=self.colors['text'],
            wrap='word',
            relief='flat',
            padx=10,
            pady=10,
            state='disabled',  # Make read-only
        )

        scrollbar = ttk.Scrollbar(log_frame, orient='vertical', command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # Configure text tags
        self.log_text.tag_configure('time', foreground=self.colors['subtext'])
        self.log_text.tag_configure('ok', foreground=self.colors['ok'])
        self.log_text.tag_configure('degraded', foreground=self.colors['degraded'])
        self.log_text.tag_configure('error', foreground=self.colors['error'])
        self.log_text.tag_configure('subsystem', foreground='#60A5FA')

    def create_footer(self):
        """Create footer"""
        footer = tk.Frame(self.window, bg=self.colors['border'], height=35)
        footer.grid(row=2, column=0, sticky='ew')

        inner = tk.Frame(footer, bg=self.colors['card'])
        inner.pack(fill='both', expand=True, padx=1, pady=1)

        # Status
        self.connection_label = tk.Label(
            inner, text="● CONNECTED", font=self.fonts['small'], bg=self.colors['card'], fg=self.colors['ok']
        )
        self.connection_label.pack(side='left', padx=20, pady=8)

        # Shortcuts hint
        shortcuts = tk.Label(
            inner,
            text="Ctrl+S: Save log | Ctrl+L: Clear | ESC: Exit",
            font=self.fonts['small'],
            bg=self.colors['card'],
            fg=self.colors['subtext'],
        )
        shortcuts.pack(side='right', padx=20, pady=8)

    def update_display(self):
        """Update displays periodically"""
        if self.shutdown_event.is_set():
            return

        current_time = time.time()

        # Update time
        self.time_label.config(text=datetime.now().strftime('%H:%M:%S'))

        # Track states
        counts = {'OK': 0, 'DEGRADED': 0, 'ERROR': 0, 'STALE': 0}

        # Update cards
        for topic, widgets in self.status_widgets.items():
            if topic in self.current_states:
                info = self.current_states[topic]
                state = info['state']

                # Time since update
                elapsed = current_time - self.last_update_times.get(topic, 0)

                # Check if stale
                if elapsed > 10:
                    widgets['indicator'].config(text="STALE", fg=self.colors['unknown'])
                    widgets['time'].config(text="No data")
                    counts['STALE'] += 1
                else:
                    # Update indicator
                    color = self.colors.get(state.lower(), self.colors['unknown'])
                    widgets['indicator'].config(text=state, fg=color)

                    # Update details
                    desc = info['description']
                    if len(desc) > 80:
                        desc = desc[:77] + "..."
                    widgets['details'].config(text=desc)

                    # Update time
                    if elapsed < 1:
                        time_str = "now"
                    elif elapsed < 60:
                        time_str = f"{int(elapsed)}s"
                    else:
                        time_str = f"{int(elapsed/60)}m"
                    widgets['time'].config(text=time_str)

                    counts[state] += 1
            else:
                counts['STALE'] += 1

        # Update summary
        if counts['ERROR'] > 0:
            self.summary_label.config(text=f"ERRORS: {counts['ERROR']}", fg=self.colors['error'])
        elif counts['DEGRADED'] > 0:
            self.summary_label.config(text=f"WARNINGS: {counts['DEGRADED']}", fg=self.colors['degraded'])
        elif counts['STALE'] == len(self.subsystems):
            self.summary_label.config(text="NO DATA", fg=self.colors['unknown'])
        else:
            self.summary_label.config(text="ALL SYSTEMS OK", fg=self.colors['ok'])

        # Schedule next update
        if not self.shutdown_event.is_set():
            self.window.after(500, self.update_display)

    def update_log_display(self):
        """Update log text widget"""
        if self.shutdown_event.is_set():
            return

        self.log_text.config(state='normal')
        self.log_text.delete(1.0, tk.END)

        # Show last 100 entries
        for entry in self.log_entries[-100:]:
            # Time
            time_str = entry['timestamp'].strftime('%H:%M:%S')
            self.log_text.insert(tk.END, f"[{time_str}] ", 'time')

            # Subsystem
            self.log_text.insert(tk.END, f"{entry['subsystem']:<6} ", 'subsystem')

            # State
            state_tag = entry['state'].lower()
            self.log_text.insert(tk.END, f"{entry['state']:<8} ", state_tag)

            # Description
            self.log_text.insert(tk.END, f"{entry['description']}\n")

        self.log_text.config(state='disabled')
        self.log_text.see(tk.END)

    def clear_log(self):
        """Clear log entries"""
        self.log_entries.clear()
        self.update_log_display()
        self.get_logger().info("Log cleared")

    def save_log(self):
        """Save log to file"""
        if not self.log_entries:
            messagebox.showinfo("Info", "Log is empty")
            return

        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            initialfile=f"aqita_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
        )

        if filename:
            try:
                with open(filename, 'w') as f:
                    f.write(f"AQITA System Log\n")
                    f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("-" * 80 + "\n\n")

                    for entry in self.log_entries:
                        f.write(f"[{entry['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}] ")
                        f.write(f"{entry['subsystem']:<6} ")
                        f.write(f"{entry['state']:<8} ")
                        f.write(f"{entry['description']}\n")

                self.get_logger().info(f"Log saved to {filename}")
                messagebox.showinfo("Success", f"Log saved successfully")
            except Exception as e:
                self.get_logger().error(f"Failed to save log: {e}")
                messagebox.showerror("Error", f"Failed to save: {str(e)}")

    def shutdown(self):
        """Clean shutdown"""
        self.get_logger().info("Shutting down...")
        self.shutdown_event.set()
        self.gui_running = False

        # Close window
        try:
            self.window.quit()
            self.window.destroy()
        except:
            pass

    def run(self):
        """Run the GUI"""
        # Handle Ctrl+C
        signal.signal(signal.SIGINT, lambda s, f: self.shutdown())

        # ROS spinner thread
        ros_thread = threading.Thread(target=self.ros_spin_thread, daemon=True)
        ros_thread.start()

        # Run GUI
        try:
            self.window.mainloop()
        except KeyboardInterrupt:
            self.shutdown()
        except Exception as e:
            self.get_logger().error(f"GUI error: {e}")
        finally:
            self.shutdown()

    def ros_spin_thread(self):
        """ROS spinning in separate thread"""
        while not self.shutdown_event.is_set():
            rclpy.spin_once(self, timeout_sec=0.1)


def main(args=None):
    """Main entry point"""
    rclpy.init(args=args)

    monitor = StatusMonitorGUI()

    try:
        monitor.run()
    except KeyboardInterrupt:
        print("\nShutdown requested")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        monitor.shutdown()
        rclpy.shutdown()
        print("Shutdown complete")


if __name__ == '__main__':
    main()
