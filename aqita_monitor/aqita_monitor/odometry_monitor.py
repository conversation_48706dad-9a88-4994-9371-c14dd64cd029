import time
import math
from nav_msgs.msg import Odometry
from rtabmap_msgs.msg import OdomInfo
from aqita_monitor.base_monitor import BaseSubsystemMonitor
from aqita_interfaces.msg import ComponentStatus


class OdometryMonitor(BaseSubsystemMonitor):
    """Monitor for AQITA visual odometry using RTAB-Map."""

    def __init__(self, odom_topic="/odom", odom_info_topic="/odom_info", odom_timeout=2.0):
        self.odom_topic = odom_topic
        self.odom_info_topic = odom_info_topic
        self.odom_timeout = odom_timeout

        self.last_odom_time = None
        self.last_odom = None
        self.last_odom_info = None
        self.position_history = []

        # Quality thresholds
        self.min_inliers = 20
        self.min_inlier_ratio = 0.2
        self.min_features = 50
        self.min_local_map_size = 100
        self.max_time_estimation = 0.2
        self.warn_position_variance = 0.001
        self.error_position_variance = 0.01
        self.max_rotation_variance = 0.001
        self.max_velocity = 5.0
        self.max_angular_velocity = 3.0

        # Bundle adjustment thresholds
        self.max_bundle_outlier_ratio = 0.2
        self.max_bundle_time = 0.05

        # Gravity alignment thresholds
        self.max_gravity_error = 0.05

        # Tracking statistics
        self.tracking_quality_history = []
        self.quality_window_size = 10

        # State stability
        self.state_change_time = None
        self.min_state_duration = 0.5

        super().__init__("odometry")

        self.state_change_time = None

        self.odom_sub = self.create_subscription(Odometry, odom_topic, self.odom_callback, 10)
        self.odom_info_sub = self.create_subscription(OdomInfo, odom_info_topic, self.odom_info_callback, 10)

        self.get_logger().info(f"AQITA monitoring visual odometry: {odom_topic} & {odom_info_topic}")

    def odom_callback(self, msg):
        """Process odometry messages."""
        self.last_odom_time = time.time()
        self.last_odom = msg

        pos = msg.pose.pose.position
        self.position_history.append((pos.x, pos.y, pos.z, time.time()))

        cutoff_time = time.time() - 10.0
        self.position_history = [p for p in self.position_history if p[3] > cutoff_time]

        self.check_basic_odom_quality(msg)

    def odom_info_callback(self, msg):
        """Process detailed odometry info for quality assessment."""
        self.last_odom_info = msg

        if msg.lost:
            pos = self.last_odom.pose.pose.position if self.last_odom else None
            if pos:
                self.report_error(f"Visual odometry LOST - last position: ({pos.x:.2f}, {pos.y:.2f}, {pos.z:.2f})")
            else:
                self.report_error("Visual odometry LOST - no position available")
            return

        inlier_ratio = msg.inliers / msg.matches if msg.matches > 0 else 0

        self.tracking_quality_history.append(inlier_ratio)
        if len(self.tracking_quality_history) > self.quality_window_size:
            self.tracking_quality_history.pop(0)

        quality_issues = []
        severity = ComponentStatus.OK

        if msg.inliers < 10:
            quality_issues.append(f"Critical: only {msg.inliers} inliers!")
            severity = ComponentStatus.ERROR
        elif msg.inliers < self.min_inliers:
            quality_issues.append(f"Low inliers: {msg.inliers} (need >{self.min_inliers})")
            severity = ComponentStatus.DEGRADED

        if inlier_ratio < self.min_inlier_ratio and msg.inliers >= self.min_inliers:
            quality_issues.append(f"Poor matches: {msg.inliers}/{msg.matches} ({inlier_ratio:.1%})")
            severity = max(severity, ComponentStatus.DEGRADED)

        if msg.features < self.min_features:
            quality_issues.append(f"Few features: {msg.features} (need >{self.min_features})")
            severity = max(severity, ComponentStatus.DEGRADED)

        if msg.local_map_size < self.min_local_map_size:
            quality_issues.append(f"Small map: {msg.local_map_size} points")
            severity = max(severity, ComponentStatus.DEGRADED)

        if msg.time_estimation > self.max_time_estimation:
            quality_issues.append(f"Slow: {msg.time_estimation*1000:.0f}ms/frame")
            severity = max(severity, ComponentStatus.DEGRADED)

        if msg.local_bundle_constraints > 50:
            outlier_ratio = msg.local_bundle_outliers / msg.local_bundle_constraints
            if outlier_ratio > self.max_bundle_outlier_ratio:
                quality_issues.append(f"Bundle outliers: {outlier_ratio:.1%}")
                severity = max(severity, ComponentStatus.DEGRADED)

        if msg.local_bundle_time > self.max_bundle_time:
            quality_issues.append(f"Slow bundle: {msg.local_bundle_time*1000:.1f}ms")
            severity = max(severity, ComponentStatus.DEGRADED)

        pos_variance = max(msg.covariance[0], msg.covariance[7], msg.covariance[14])
        rot_variance = max(msg.covariance[21], msg.covariance[28], msg.covariance[35])

        std_dev_mm = math.sqrt(pos_variance) * 1000

        if pos_variance > self.error_position_variance:
            quality_issues.append(f"High position uncertainty: ±{std_dev_mm:.0f}mm")
            severity = ComponentStatus.ERROR
        elif std_dev_mm > 50:
            quality_issues.append(f"Position uncertain: ±{std_dev_mm:.0f}mm")
            severity = max(severity, ComponentStatus.DEGRADED)

        if rot_variance > self.max_rotation_variance:
            std_dev_deg = math.degrees(math.sqrt(rot_variance))
            if std_dev_deg > 5.0:
                quality_issues.append(f"Rotation uncertain: ±{std_dev_deg:.1f}°")
                severity = max(severity, ComponentStatus.DEGRADED)

        if msg.gravity_roll_error > self.max_gravity_error or msg.gravity_pitch_error > self.max_gravity_error:
            quality_issues.append(
                f"IMU misaligned: roll={math.degrees(msg.gravity_roll_error):.1f}°, "
                f"pitch={math.degrees(msg.gravity_pitch_error):.1f}°"
            )
            severity = max(severity, ComponentStatus.DEGRADED)

        if msg.interval > 0:
            linear_velocity = (
                math.sqrt(
                    msg.transform.translation.x**2 + msg.transform.translation.y**2 + msg.transform.translation.z**2
                )
                / msg.interval
            )

            if linear_velocity > self.max_velocity:
                quality_issues.append(f"High velocity: {linear_velocity:.1f}m/s")
                if linear_velocity > self.max_velocity * 2:
                    severity = ComponentStatus.ERROR
                else:
                    severity = max(severity, ComponentStatus.DEGRADED)

        if len(self.tracking_quality_history) >= self.quality_window_size:
            avg_quality = sum(self.tracking_quality_history) / len(self.tracking_quality_history)
            if avg_quality < self.min_inlier_ratio:
                quality_issues.append(f"Declining quality trend: {avg_quality:.1%} avg")
                severity = max(severity, ComponentStatus.DEGRADED)

        current_time = time.time()

        can_change_state = True
        if self.state_change_time is not None:
            time_since_change = current_time - self.state_change_time
            if time_since_change < self.min_state_duration and severity != ComponentStatus.ERROR:
                can_change_state = False

        if severity == ComponentStatus.ERROR and can_change_state:
            self.report_error(" | ".join(quality_issues))
            self.state_change_time = current_time
        elif severity == ComponentStatus.DEGRADED and can_change_state:
            if len(quality_issues) > 1 or any(
                keyword in " ".join(quality_issues)
                for keyword in ["Poor matches", "Critical", "Few features", "Small map"]
            ):
                self.report_degraded(" | ".join(quality_issues))
                self.state_change_time = current_time
        elif severity == ComponentStatus.OK and can_change_state and self.current_state != ComponentStatus.OK:
            self.report_ok()
            self.state_change_time = current_time

        if severity == ComponentStatus.OK:
            self.get_logger().debug(
                f"Tracking OK: {msg.inliers}/{msg.matches} ({inlier_ratio:.1%}), "
                f"{msg.features} features, {msg.local_map_size} map points, "
                f"{msg.time_estimation*1000:.0f}ms"
            )

    def check_basic_odom_quality(self, odom_msg):
        """Check odometry quality using basic odometry message."""
        if self.last_odom_info and time.time() - self.last_odom_time < 0.5:
            return

        twist = odom_msg.twist.twist
        linear_speed = math.sqrt(twist.linear.x**2 + twist.linear.y**2 + twist.linear.z**2)
        angular_speed = math.sqrt(twist.angular.x**2 + twist.angular.y**2 + twist.angular.z**2)

        if linear_speed > self.max_velocity:
            self.report_error(f"Unreasonable linear velocity: {linear_speed:.2f}m/s")
            return

        if angular_speed > self.max_angular_velocity:
            self.report_error(f"Unreasonable angular velocity: {angular_speed:.2f}rad/s")
            return

        if len(self.position_history) >= 2:
            current_pos = self.position_history[-1]
            prev_pos = self.position_history[-2]

            distance = math.sqrt(
                (current_pos[0] - prev_pos[0]) ** 2
                + (current_pos[1] - prev_pos[1]) ** 2
                + (current_pos[2] - prev_pos[2]) ** 2
            )
            time_diff = current_pos[3] - prev_pos[3]

            if time_diff > 0:
                implied_speed = distance / time_diff
                if implied_speed > self.max_velocity * 2.0:
                    self.report_error(
                        f"Position jump detected: {distance:.2f}m in {time_diff:.2f}s " f"({implied_speed:.1f}m/s)"
                    )
                    return

    def monitoring_loop(self):
        """Main monitoring loop."""
        while self.monitoring_active:
            current_time = time.time()

            if self.last_odom_time is None:
                if current_time > 5.0:
                    self.report_error("No odometry received")
            else:
                time_since_odom = current_time - self.last_odom_time

                if time_since_odom > self.odom_timeout:
                    self.report_error(f"Odometry timeout: {time_since_odom:.1f}s")
                elif time_since_odom > self.odom_timeout * 0.8:
                    self.report_degraded(f"Odometry delayed: {time_since_odom:.1f}s")

            time.sleep(0.5)
