import time
from sensor_msgs.msg import Image
from cv_bridge import CvBridge
import cv2
import numpy as np
from aqita_monitor.base_monitor import BaseSubsystemMonitor
from aqita_interfaces.msg import ComponentStatus


class CameraMonitor(BaseSubsystemMonitor):
    """Monitor for AQITA camera subsystem supporting RGBD and stereo configurations."""

    def __init__(
        self,
        camera_name,
        camera_mode="rgbd",
        primary_topic="/camera/color/image_raw",
        secondary_topic="/camera/depth/image_rect_raw",
        image_timeout=3.0,
        fps_threshold=15.0,
        sync_tolerance=0.1,
    ):
        self.camera_mode = camera_mode
        self.primary_topic = primary_topic
        self.secondary_topic = secondary_topic
        self.image_timeout = image_timeout
        self.fps_threshold = fps_threshold
        self.sync_tolerance = sync_tolerance

        # Image reception tracking
        self.last_primary_time = None
        self.last_secondary_time = None
        self.primary_count = 0
        self.secondary_count = 0
        self.primary_fps_window = []
        self.secondary_fps_window = []
        self.fps_window_size = 10

        # Synchronization tracking
        self.sync_errors = 0
        self.max_sync_errors = 10

        # Image quality tracking
        self.bridge = CvBridge()
        self.brightness_threshold_low = 30
        self.brightness_threshold_high = 220
        self.blur_threshold = 100
        self.depth_min_valid = 0.1
        self.depth_max_valid = 10.0

        super().__init__(camera_name)

        # Subscribe to camera topics
        self.primary_sub = self.create_subscription(Image, primary_topic, self.primary_callback, 10)
        self.secondary_sub = self.create_subscription(Image, secondary_topic, self.secondary_callback, 10)

        mode_desc = "RGB-D" if camera_mode == "rgbd" else "Stereo"
        self.get_logger().info(f"AQITA monitoring {mode_desc} camera: {primary_topic} & {secondary_topic}")

    def primary_callback(self, msg):
        """Process primary camera stream (RGB or left stereo)."""
        current_time = time.time()

        if self.last_primary_time is not None:
            frame_interval = current_time - self.last_primary_time
            if frame_interval > 0:
                fps = 1.0 / frame_interval
                self.primary_fps_window.append(fps)
                if len(self.primary_fps_window) > self.fps_window_size:
                    self.primary_fps_window.pop(0)

        self.last_primary_time = current_time
        self.primary_count += 1

        if self.primary_count % 30 == 0:
            try:
                if msg.encoding in ["mono8", "8UC1"]:
                    cv_image = self.bridge.imgmsg_to_cv2(msg, "mono8")
                    self.analyze_mono_quality(cv_image)
                else:
                    cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
                    self.analyze_rgb_quality(cv_image)
            except Exception as e:
                self.get_logger().warn(f"Failed to analyze primary image: {e}")

        self.check_synchronization()

    def secondary_callback(self, msg):
        """Process secondary camera stream (depth or right stereo)."""
        current_time = time.time()

        if self.last_secondary_time is not None:
            frame_interval = current_time - self.last_secondary_time
            if frame_interval > 0:
                fps = 1.0 / frame_interval
                self.secondary_fps_window.append(fps)
                if len(self.secondary_fps_window) > self.fps_window_size:
                    self.secondary_fps_window.pop(0)

        self.last_secondary_time = current_time
        self.secondary_count += 1

        if self.camera_mode == "rgbd" and self.secondary_count % 30 == 0:
            try:
                if msg.encoding in ["16UC1", "32FC1"]:
                    cv_image = self.bridge.imgmsg_to_cv2(msg, desired_encoding="passthrough")
                    self.analyze_depth_quality(cv_image)
            except Exception as e:
                self.get_logger().warn(f"Failed to analyze depth image: {e}")
        elif self.camera_mode == "stereo" and self.secondary_count % 30 == 0:
            try:
                if msg.encoding in ["mono8", "8UC1"]:
                    cv_image = self.bridge.imgmsg_to_cv2(msg, "mono8")
                    self.analyze_mono_quality(cv_image)
                else:
                    cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
                    self.analyze_rgb_quality(cv_image)
            except Exception as e:
                self.get_logger().warn(f"Failed to analyze secondary image: {e}")

        self.check_synchronization()

    def analyze_rgb_quality(self, cv_image):
        """Analyze RGB image quality."""
        gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
        mean_brightness = np.mean(gray)

        if mean_brightness < self.brightness_threshold_low:
            self.report_degraded(f"Image too dark - brightness: {mean_brightness:.1f}")
        elif mean_brightness > self.brightness_threshold_high:
            self.report_degraded(f"Image too bright - brightness: {mean_brightness:.1f}")

        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        if laplacian_var < self.blur_threshold:
            self.report_degraded(f"Image appears blurry - sharpness: {laplacian_var:.1f}")

    def analyze_mono_quality(self, cv_image):
        """Analyze monochrome image quality."""
        mean_brightness = np.mean(cv_image)

        if mean_brightness < self.brightness_threshold_low:
            self.report_degraded(f"Image too dark - brightness: {mean_brightness:.1f}")
        elif mean_brightness > self.brightness_threshold_high:
            self.report_degraded(f"Image too bright - brightness: {mean_brightness:.1f}")

        laplacian_var = cv2.Laplacian(cv_image, cv2.CV_64F).var()
        if laplacian_var < self.blur_threshold:
            self.report_degraded(f"Image appears blurry - sharpness: {laplacian_var:.1f}")

    def analyze_depth_quality(self, depth_image):
        """Analyze depth image quality."""
        if depth_image.dtype == np.uint16:
            depth_meters = depth_image.astype(np.float32) / 1000.0
        else:
            depth_meters = depth_image

        valid_mask = (depth_meters > self.depth_min_valid) & (depth_meters < self.depth_max_valid)
        valid_ratio = np.sum(valid_mask) / depth_image.size

        if valid_ratio < 0.5:
            self.report_degraded(f"Low depth coverage: {valid_ratio*100:.1f}% valid pixels")

        if valid_ratio > 0.1:
            mean_depth = np.mean(depth_meters[valid_mask])
            if mean_depth < 0.5:
                self.report_degraded(f"Depth too close: mean={mean_depth:.2f}m")
            elif mean_depth > 5.0:
                self.report_degraded(f"Depth too far: mean={mean_depth:.2f}m")

    def check_synchronization(self):
        """Check synchronization between camera streams."""
        if self.last_primary_time and self.last_secondary_time:
            time_diff = abs(self.last_primary_time - self.last_secondary_time)
            if time_diff > self.sync_tolerance:
                self.sync_errors += 1
                if self.sync_errors > self.max_sync_errors:
                    self.report_degraded(f"Stream sync issues: {time_diff*1000:.0f}ms offset")
            else:
                self.sync_errors = max(0, self.sync_errors - 1)

    def monitoring_loop(self):
        """Main monitoring loop."""
        while self.monitoring_active:
            current_time = time.time()

            primary_timeout = self._check_stream_timeout(
                self.last_primary_time, current_time, "left" if self.camera_mode == "stereo" else "RGB"
            )
            secondary_timeout = self._check_stream_timeout(
                self.last_secondary_time, current_time, "right" if self.camera_mode == "stereo" else "depth"
            )

            if not primary_timeout and not secondary_timeout:
                self._check_fps()

            time.sleep(0.5)

    def _check_stream_timeout(self, last_time, current_time, stream_name):
        """Check if a stream has timed out."""
        if last_time is None:
            if current_time > 5.0:
                self.report_error(f"No {stream_name} images received")
                return True
        else:
            time_since_last = current_time - last_time
            if time_since_last > self.image_timeout:
                self.report_error(f"{stream_name.capitalize()} timeout - {time_since_last:.1f}s")
                return True
        return False

    def _check_fps(self):
        """Check FPS for both streams."""
        issues = []
        primary_name = "Left" if self.camera_mode == "stereo" else "RGB"
        secondary_name = "Right" if self.camera_mode == "stereo" else "Depth"

        if len(self.primary_fps_window) >= 5:
            avg_primary_fps = np.mean(self.primary_fps_window)
            if avg_primary_fps < self.fps_threshold * 0.5:
                self.report_error(f"{primary_name} FPS critical: {avg_primary_fps:.1f} fps")
                return
            elif avg_primary_fps < self.fps_threshold * 0.8:
                issues.append(f"{primary_name} FPS low: {avg_primary_fps:.1f}")

        if len(self.secondary_fps_window) >= 5:
            avg_secondary_fps = np.mean(self.secondary_fps_window)
            if avg_secondary_fps < self.fps_threshold * 0.5:
                self.report_error(f"{secondary_name} FPS critical: {avg_secondary_fps:.1f} fps")
                return
            elif avg_secondary_fps < self.fps_threshold * 0.8:
                issues.append(f"{secondary_name} FPS low: {avg_secondary_fps:.1f}")

        if issues:
            self.report_degraded(" | ".join(issues))
        else:
            self.report_ok()
