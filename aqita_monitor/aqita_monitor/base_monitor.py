import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy, Duration
from aqita_interfaces.msg import ComponentStatus
import threading
import time
from abc import ABC, abstractmethod


class BaseSubsystemMonitor(Node, ABC):
    """Base class for all subsystem monitors in AQITA system"""

    def __init__(self, subsystem_name, status_interval=1.0):
        super().__init__(f"{subsystem_name}_status_monitor")
        self.subsystem_name = subsystem_name
        self.status_interval = status_interval

        # QoS for status publishing
        qos_profile = QoSProfile(
            reliability=ReliabilityPolicy.RELIABLE,
            history=HistoryPolicy.KEEP_LAST,
            depth=10,
            deadline=Duration(seconds=2)
            )

        # Publisher for subsystem status
        self.status_pub = self.create_publisher(ComponentStatus, f'/{subsystem_name}/status', qos_profile)

        # Timer for periodic OK status publishing
        self.status_timer = self.create_timer(status_interval, self.publish_periodic_status)

        # Current subsystem state tracking
        self.current_state = ComponentStatus.OK
        self.last_error_time = None
        self.last_degraded_time = None

        self.get_logger().info(f"AQITA {subsystem_name} status monitor started")

        # Start monitoring in separate thread
        self.monitoring_thread = threading.Thread(target=self.monitoring_loop, daemon=True)
        self.monitoring_active = True
        self.monitoring_thread.start()

    def publish_status(self, state, description=""):
        """Publish subsystem status immediately"""
        msg = ComponentStatus()
        msg.stamp = self.get_clock().now().to_msg()
        msg.state = state
        msg.description = description

        self.status_pub.publish(msg)
        self.current_state = state

        # Log significant state changes
        if state != ComponentStatus.OK:
            state_name = {ComponentStatus.DEGRADED: "DEGRADED", ComponentStatus.ERROR: "ERROR"}[state]
            self.get_logger().warn(f"AQITA {self.subsystem_name}: {state_name} - {description}")

    def publish_periodic_status(self):
        """Publish periodic OK status if no issues detected"""
        if self.current_state == ComponentStatus.OK:
            self.publish_status(ComponentStatus.OK, "")

    def report_degraded(self, description):
        """Report degraded state immediately"""
        self.last_degraded_time = time.time()
        self.publish_status(ComponentStatus.DEGRADED, description)

    def report_error(self, description):
        """Report error state immediately"""
        self.last_error_time = time.time()
        self.publish_status(ComponentStatus.ERROR, description)

    def report_ok(self):
        """Report OK state (clears previous error/degraded)"""
        if self.current_state != ComponentStatus.OK:
            self.get_logger().info(f"AQITA {self.subsystem_name}: Recovered to OK state")
        self.current_state = ComponentStatus.OK

    @abstractmethod
    def monitoring_loop(self):
        """
        Continuous monitoring loop - to be implemented by subclasses
        Should call report_error(), report_degraded(), or report_ok() as needed
        """
        pass

    def destroy_node(self):
        """Cleanup when shutting down"""
        self.monitoring_active = False
        if self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=1.0)
        super().destroy_node()
