import time
import rclpy
from tf2_ros import B<PERSON><PERSON>, TransformListener, LookupException, ConnectivityException, ExtrapolationException
from aqita_monitor.base_monitor import BaseSubsystemMonitor
from aqita_interfaces.msg import ComponentStatus


class SlamMonitor(BaseSubsystemMonitor):
    """Monitor for SLAM system health by checking TF and aggregating subsystem statuses."""

    def __init__(
        self,
        base_frame="base_link",
        map_frame="map",
        odom_frame="odom",
        transform_timeout=2.0,
        cameras=None,
        subsystem_timeout=5.0,
    ):
        self.base_frame = base_frame
        self.map_frame = map_frame
        self.odom_frame = odom_frame
        self.transform_timeout = transform_timeout
        self.subsystem_timeout = subsystem_timeout
        self.odometry_status = None
        self.camera_statuses = {}
        self.last_odometry_update = None
        self.last_camera_updates = {}
        self.last_transform_time = None
        self.transform_age_threshold = 5.0

        # Default cameras if none specified
        if cameras is None:
            cameras = ['cam_front', 'cam_left', 'cam_right']
        self.cameras = cameras

        super().__init__("slam")

        # TF2 buffer and listener
        self.tf_buffer = Buffer()
        self.tf_listener = TransformListener(self.tf_buffer, self)

        # Subscribe to odometry status
        self.odometry_status_sub = self.create_subscription(
            ComponentStatus, '/odometry/status', self.odometry_status_callback, 10
        )

        # Subscribe to all camera statuses
        self.camera_status_subs = []
        for camera in self.cameras:
            sub = self.create_subscription(
                ComponentStatus, f'/{camera}/status', lambda msg, cam=camera: self.camera_status_callback(msg, cam), 10
            )
            self.camera_status_subs.append(sub)
            self.last_camera_updates[camera] = None

        self.get_logger().info(
            f"AQITA monitoring SLAM: {map_frame} -> {base_frame}, " f"cameras: {', '.join(self.cameras)}"
        )

    def odometry_status_callback(self, msg):
        """Handle odometry status updates."""
        self.odometry_status = msg
        self.last_odometry_update = time.time()

    def camera_status_callback(self, msg, camera_name):
        """Handle camera status updates."""
        self.camera_statuses[camera_name] = msg
        self.last_camera_updates[camera_name] = time.time()

    def check_transform(self):
        """Check if transform from base_link to map exists and is recent."""
        try:
            now = rclpy.time.Time()
            timeout_duration = rclpy.duration.Duration(seconds=self.transform_timeout)
            transform = self.tf_buffer.lookup_transform(self.map_frame, self.base_frame, now, timeout=timeout_duration)

            self.last_transform_time = time.time()
            return True, "Transform OK"

        except (LookupException, ConnectivityException, ExtrapolationException, Exception) as e:
            return False, f"Transform missing or error: {str(e)}"

    def aggregate_status(self):
        """Determine overall SLAM status from subsystems and transform."""
        current_time = time.time()
        issues = []
        severity = ComponentStatus.OK

        # Check transform
        transform_ok, transform_msg = self.check_transform()
        if not transform_ok:
            issues.append(transform_msg)
            if "missing" in transform_msg.lower():
                severity = ComponentStatus.ERROR
            else:
                severity = ComponentStatus.DEGRADED

        if self.last_transform_time is not None:
            transform_age = current_time - self.last_transform_time
            if transform_age > self.transform_age_threshold:
                issues.append(f"Transform age too high: {transform_age:.1f}s")
                severity = max(severity, ComponentStatus.ERROR)

        # Check odometry status
        if self.last_odometry_update is None:
            issues.append("No odometry status received")
            severity = max(severity, ComponentStatus.DEGRADED)
        elif current_time - self.last_odometry_update > self.subsystem_timeout:
            issues.append(f"Odometry status timeout: {current_time - self.last_odometry_update:.1f}s")
            severity = max(severity, ComponentStatus.DEGRADED)
        elif self.odometry_status:
            if self.odometry_status.state == ComponentStatus.ERROR:
                issues.append(f"Odometry ERROR: {self.odometry_status.description}")
                severity = ComponentStatus.ERROR
            elif self.odometry_status.state == ComponentStatus.DEGRADED:
                issues.append(f"Odometry DEGRADED: {self.odometry_status.description}")
                severity = max(severity, ComponentStatus.DEGRADED)

        # Check all camera statuses
        cameras_with_issues = []
        cameras_ok_count = 0

        for camera in self.cameras:
            if camera not in self.last_camera_updates or self.last_camera_updates[camera] is None:
                cameras_with_issues.append(f"{camera}: no status received")
            elif current_time - self.last_camera_updates[camera] > self.subsystem_timeout:
                timeout = current_time - self.last_camera_updates[camera]
                cameras_with_issues.append(f"{camera}: timeout {timeout:.1f}s")
            elif camera in self.camera_statuses:
                status = self.camera_statuses[camera]
                if status.state == ComponentStatus.ERROR:
                    cameras_with_issues.append(f"{camera}: ERROR - {status.description}")
                elif status.state == ComponentStatus.DEGRADED:
                    cameras_with_issues.append(f"{camera}: DEGRADED - {status.description}")
                else:
                    cameras_ok_count += 1

        # Determine camera severity
        if len(cameras_with_issues) == len(self.cameras):
            issues.append(f"All cameras failed: {'; '.join(cameras_with_issues)}")
            severity = max(severity, ComponentStatus.DEGRADED)
        elif len(cameras_with_issues) > 0:
            issues.append(
                f"Camera issues ({cameras_ok_count}/{len(self.cameras)} OK): {'; '.join(cameras_with_issues)}"
            )
            severity = max(severity, ComponentStatus.DEGRADED)

        return severity, issues

    def monitoring_loop(self):
        """Main monitoring loop."""
        while self.monitoring_active:
            severity, issues = self.aggregate_status()
            if severity == ComponentStatus.ERROR:
                self.report_error(" | ".join(issues))
            elif severity == ComponentStatus.DEGRADED:
                self.report_degraded(" | ".join(issues))
            else:
                self.report_ok()

            time.sleep(0.5)
