import rclpy
from aqita_monitor.camera_monitor import CameraMonitor


def main(args=None):
    """Main entry point for camera monitor node."""
    rclpy.init(args=args)

    temp_node = rclpy.create_node('temp_param_node')

    # Declare parameters with appropriate defaults
    temp_node.declare_parameter('camera_name', 'front_camera')
    temp_node.declare_parameter('camera_mode', 'rgbd')  # 'rgbd' or 'stereo'
    temp_node.declare_parameter('primary_topic', '/camera/color/image_raw')  # RGB or left image
    temp_node.declare_parameter('secondary_topic', '/camera/depth/image_rect_raw')  # Depth or right image
    temp_node.declare_parameter('image_timeout', 3.0)
    temp_node.declare_parameter('fps_threshold', 20.0)
    temp_node.declare_parameter('sync_tolerance', 0.1)

    # Get parameters
    camera_name = temp_node.get_parameter('camera_name').value
    camera_mode = temp_node.get_parameter('camera_mode').value
    primary_topic = temp_node.get_parameter('primary_topic').value
    secondary_topic = temp_node.get_parameter('secondary_topic').value
    image_timeout = temp_node.get_parameter('image_timeout').value
    fps_threshold = temp_node.get_parameter('fps_threshold').value
    sync_tolerance = temp_node.get_parameter('sync_tolerance').value

    temp_node.destroy_node()

    camera_monitor = CameraMonitor(
        camera_name=camera_name,
        camera_mode=camera_mode,
        primary_topic=primary_topic,
        secondary_topic=secondary_topic,
        image_timeout=image_timeout,
        fps_threshold=fps_threshold,
        sync_tolerance=sync_tolerance,
    )

    try:
        rclpy.spin(camera_monitor)
    except KeyboardInterrupt:
        camera_monitor.get_logger().info("Camera monitor shutting down...")
    except Exception as e:
        camera_monitor.get_logger().error(f"Unexpected error: {e}")
    finally:
        try:
            camera_monitor.destroy_node()
        except:
            pass
        try:
            if rclpy.ok():
                rclpy.shutdown()
        except:
            pass


if __name__ == '__main__':
    main()
