import rclpy
from aqita_monitor.odometry_monitor import OdometryMonitor


def main(args=None):
    """Main entry point for odometry monitor node"""
    rclpy.init(args=args)

    # Create a temporary node to get parameters
    temp_node = rclpy.create_node('temp_param_node')

    # Get parameters
    temp_node.declare_parameter('odom_topic', '/rtabmap/odom')
    temp_node.declare_parameter('odom_info_topic', '/rtabmap/odom_info')
    temp_node.declare_parameter('odom_timeout', 2.0)

    odom_topic = temp_node.get_parameter('odom_topic').value
    odom_info_topic = temp_node.get_parameter('odom_info_topic').value
    odom_timeout = temp_node.get_parameter('odom_timeout').value

    temp_node.destroy_node()

    odometry_monitor = OdometryMonitor(
        odom_topic=odom_topic, odom_info_topic=odom_info_topic, odom_timeout=odom_timeout
    )

    try:
        rclpy.spin(odometry_monitor)
    except KeyboardInterrupt:
        odometry_monitor.get_logger().info("Odometry monitor shutting down...")
    except Exception as e:
        odometry_monitor.get_logger().error(f"Unexpected error: {e}")
    finally:
        try:
            odometry_monitor.destroy_node()
        except:
            pass
        try:
            if rclpy.ok():
                rclpy.shutdown()
        except:
            pass


if __name__ == '__main__':
    main()
