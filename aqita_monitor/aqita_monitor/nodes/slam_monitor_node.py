import rclpy
from aqita_monitor.slam_monitor import Slam<PERSON>oni<PERSON>


def main(args=None):
    """Main entry point for SLAM monitor node."""
    rclpy.init(args=args)

    # Create temporary node for parameters
    temp_node = rclpy.create_node('temp_param_node')

    # Declare parameters
    temp_node.declare_parameter('base_frame', 'base_link')
    temp_node.declare_parameter('map_frame', 'map')
    temp_node.declare_parameter('odom_frame', 'odom')
    temp_node.declare_parameter('transform_timeout', 2.0)
    temp_node.declare_parameter('cameras', ['cam_front', 'cam_left', 'cam_right'])
    temp_node.declare_parameter('subsystem_timeout', 5.0)

    # Get parameters
    base_frame = temp_node.get_parameter('base_frame').value
    map_frame = temp_node.get_parameter('map_frame').value
    odom_frame = temp_node.get_parameter('odom_frame').value
    transform_timeout = temp_node.get_parameter('transform_timeout').value
    cameras = temp_node.get_parameter('cameras').value
    subsystem_timeout = temp_node.get_parameter('subsystem_timeout').value

    temp_node.destroy_node()

    slam_monitor = SlamMonitor(
        base_frame=base_frame,
        map_frame=map_frame,
        odom_frame=odom_frame,
        transform_timeout=transform_timeout,
        cameras=cameras,
        subsystem_timeout=subsystem_timeout,
    )

    try:
        rclpy.spin(slam_monitor)
    except KeyboardInterrupt:
        slam_monitor.get_logger().info("SLAM monitor shutting down...")
    except Exception as e:
        slam_monitor.get_logger().error(f"Unexpected error: {e}")
    finally:
        try:
            slam_monitor.destroy_node()
        except:
            pass
        try:
            if rclpy.ok():
                rclpy.shutdown()
        except:
            pass


if __name__ == '__main__':
    main()
