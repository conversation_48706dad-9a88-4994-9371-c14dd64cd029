<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>aqita_monitor</name>
  <version>0.0.1</version>
  <description>System monitoring package for AQITA robot subsystems</description>
  <maintainer email="<EMAIL>">Petar Velev</maintainer>
  <license>MIT</license>

  <depend>rclpy</depend>
  <depend>aqita_interfaces</depend>
  <depend>sensor_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>cv_bridge</depend>
  <depend>opencv2</depend>

  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>python3-pytest</test_depend>

  <export>
    <build_type>ament_python</build_type>
  </export>
</package>