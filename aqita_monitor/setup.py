from setuptools import setup
import os
from glob import glob

package_name = 'aqita_monitor'

setup(
    name=package_name,
    version='0.0.1',
    packages=[package_name, package_name + '.nodes'],
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        (os.path.join('share', package_name, 'launch'), glob('launch/*.launch.py')),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='Petar Velev',
    maintainer_email='<EMAIL>',
    description='System monitoring package for AQITA robot subsystems',
    license='MIT',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'camera_monitor_node = aqita_monitor.nodes.camera_monitor_node:main',
            'odometry_monitor_node = aqita_monitor.nodes.odometry_monitor_node:main',
            'slam_monitor_node = aqita_monitor.nodes.slam_monitor_node:main',
        ],
    },
)